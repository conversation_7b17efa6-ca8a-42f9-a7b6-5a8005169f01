<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Role extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
    ];

    /**
     * Get the users that belong to this role.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Check if the role is super admin.
     *
     * @return bool
     */
    public function isSuperAdmin(): bool
    {
        return $this->slug === 'super_admin';
    }

    /**
     * Check if the role is admin.
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->slug === 'admin';
    }

    /**
     * Check if the role is staff.
     *
     * @return bool
     */
    public function isStaff(): bool
    {
        return $this->slug === 'staff';
    }
}
