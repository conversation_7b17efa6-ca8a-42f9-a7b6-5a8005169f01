<?php

namespace App\Observers;

use App\Models\DocumentTracking;
use App\Services\OperatingUnitSyncService;
use Illuminate\Support\Facades\Log;

class DocumentTrackingObserver
{
    /**
     * @var OperatingUnitSyncService
     */
    protected $syncService;

    /**
     * Constructor
     * 
     * @param OperatingUnitSyncService $syncService
     */
    public function __construct(OperatingUnitSyncService $syncService)
    {
        $this->syncService = $syncService;
    }

    /**
     * Handle the DocumentTracking "created" event.
     */
    public function created(DocumentTracking $tracking): void
    {
        $this->clearCacheForTracking($tracking);
        Log::info('Document tracking created, cache cleared', ['tracking_id' => $tracking->id]);
    }

    /**
     * Handle the DocumentTracking "updated" event.
     */
    public function updated(DocumentTracking $tracking): void
    {
        $this->clearCacheForTracking($tracking);
        Log::info('Document tracking updated, cache cleared', ['tracking_id' => $tracking->id]);
    }

    /**
     * Handle the DocumentTracking "deleted" event.
     */
    public function deleted(DocumentTracking $tracking): void
    {
        $this->clearCacheForTracking($tracking);
        Log::info('Document tracking deleted, cache cleared', ['tracking_id' => $tracking->id]);
    }

    /**
     * Clear cache for the tracking's operating unit
     */
    protected function clearCacheForTracking(DocumentTracking $tracking): void
    {
        // Load relationships if not already loaded
        if (!$tracking->relationLoaded('handledBy')) {
            $tracking->load('handledBy');
        }

        // Clear cache for the handler's operating unit
        if ($tracking->handledBy && $tracking->handledBy->operating_unit_id) {
            $this->syncService->clearOperatingUnitCache($tracking->handledBy->operating_unit_id);
        }

        // If the tracking has a document, clear cache for the document's operating unit
        if ($tracking->document_id) {
            if (!$tracking->relationLoaded('document')) {
                $tracking->load('document');
            }

            if ($tracking->document && $tracking->document->origin_unit_id) {
                $this->syncService->clearOperatingUnitCache($tracking->document->origin_unit_id);
            }
        }
    }
}
