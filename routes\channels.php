<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// Private channel for user inbox messages
Broadcast::channel('inbox.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Private channel for operating unit synchronization
// This allows users in the same operating unit to receive updates
Broadcast::channel('operating-unit.{id}', function ($user, $id) {
    return (int) $user->operating_unit_id === (int) $id;
});
