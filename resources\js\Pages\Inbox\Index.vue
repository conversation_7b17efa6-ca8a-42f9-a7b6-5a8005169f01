<template>
  <Head title="Inbox" />

  <AppSidebarLayout>
    <v-container fluid class="px-6">
      <v-row>
        <v-col cols="12">
          <v-card elevation="2">
            <!-- Toolbar with reference dropdown and search -->
            <div
              class="px-4 py-3 border-b d-flex align-center justify-space-between bg-grey-lighten-5"
            >
              <div class="d-flex align-center">
                <v-chip
                  v-if="unreadCount > 0"
                  color="error"
                  size="small"
                  class="mr-2"
                >
                  {{ unreadCount }} unread
                </v-chip>

                <!-- Reference dropdown moved to left side -->
                <v-select
                  v-model="activeTab"
                  :items="referenceOptions"
                  item-title="title"
                  item-value="value"
                  label="Reference"
                  variant="outlined"
                  density="compact"
                  hide-details
                  class="reference-select"
                  bg-color="white"
                  color="primary"
                  @update:model-value="changeReference"
                ></v-select>
              </div>

              <!-- Right side controls: Search only -->
              <div class="d-flex align-center">
                <!-- Search field -->
                <div style="width: 300px" class="mr-2">
                  <v-text-field
                    v-model="search"
                    append-icon="mdi-magnify"
                    label="Search messages..."
                    single-line
                    hide-details
                    variant="outlined"
                    density="compact"
                  ></v-text-field>
                </div>
              </div>
            </div>

            <!-- Data table for messages -->
            <v-data-table
              :headers="headers"
              :items="filteredMessages"
              :search="search"
              :loading="loading"
              class="elevation-1"
              density="compact"
              fixed-header
              hover
              :items-per-page-options="itemsPerPageOptions"
              :items-per-page="itemsPerPage"
              @update:options="handleTableOptions"
              :header-props="{ class: 'bg-white' }"
              @mounted="
                () =>
                  console.log(
                    'Data table mounted with items:',
                    filteredMessages.length
                  )
              "
            >
              <template v-slot:item.sender="{ item }">
                <div
                  class="pl-0 d-flex align-center"
                  @click="viewMessage(item)"
                  style="
                    cursor: pointer;
                    white-space: nowrap;
                    margin-left: -16px;
                  "
                >
                  <v-icon
                    v-if="!item.is_read"
                    color="primary"
                    size="small"
                    style="margin-right: 4px"
                    >mdi-circle</v-icon
                  >
                  <v-icon
                    v-else
                    color="transparent"
                    size="small"
                    style="margin-right: 4px"
                    >mdi-circle-outline</v-icon
                  >
                  <span
                    :class="{ 'font-weight-bold': !item.is_read }"
                    class="sender-name"
                  >
                    {{ item.sender ? item.sender.name : "Unknown" }}
                  </span>
                </div>
              </template>

              <!-- Subject column with concise message -->
              <template v-slot:item.subject="{ item }">
                <div @click="viewMessage(item)" style="cursor: pointer">
                  <div>
                    <span
                      :class="{ 'font-weight-bold': !item.is_read }"
                      class="subject"
                    >
                      {{ item.subject }}
                    </span>
                  </div>
                  <div v-if="item.document" class="message-preview">
                    <v-chip
                      v-if="item.priority"
                      :color="getPriorityColor(item.priority)"
                      size="x-small"
                      class="mr-1"
                    >
                      {{ item.priority }}
                    </v-chip>
                    <span class="message-preview-text">{{
                      getShortMessage(item.message)
                    }}</span>
                  </div>
                </div>
              </template>

              <!-- Document column with preview -->
              <template v-slot:item.document="{ item }">
                <div style="cursor: pointer">
                  <template v-if="item.document && item.document.file_path">
                    <div class="d-flex align-center">
                      <!-- <div class="mr-2 document-preview-container"
                                                @click.stop="openDocumentPreview(item.document)">
                                                <img v-if="isImageFile(item.document.file_path)"
                                                    :src="getFileUrl(item.document.file_path)" alt="Document Preview"
                                                    class="document-thumbnail" />
                                                <div v-else class="pdf-thumbnail">
                                                    <v-icon color="primary">mdi-file-pdf-box</v-icon>
                                                </div>
                                            </div> -->
                      <div @click="viewMessage(item)">
                        <span class="font-medium">{{
                          item.document.control_number
                        }}</span>
                        <span class="ml-1 font-medium"
                          >({{ item.document.reference_document || "" }})</span
                        >
                      </div>
                    </div>
                  </template>
                  <template v-else-if="item.document">
                    <div class="d-flex align-center" @click="viewMessage(item)">
                      <!-- <v-icon size="small" color="primary"
                                                class="mr-1">mdi-file-document-outline</v-icon> -->
                      <span class="font-medium">{{
                        item.document.control_number
                      }}</span>
                      <span class="ml-1 font-medium"
                        >({{ item.document.reference_document || "" }})</span
                      >
                    </div>
                  </template>
                  <template v-else>
                    <div class="d-flex align-center" @click="viewMessage(item)">
                      <!-- <v-icon size="small" color="grey-darken-2"
                                                class="mr-1">mdi-file-outline</v-icon> -->
                      <span class="font-medium">No document attached</span>
                    </div>
                  </template>
                </div>
              </template>

              <!-- Reference column -->
              <template v-slot:item.reference="{ item }">
                <div @click="viewMessage(item)" style="cursor: pointer">
                  <v-chip
                    v-if="item.document"
                    color="primary"
                    size="x-small"
                    class="text-white"
                  >
                    {{ item.document.reference_document || "" }}
                  </v-chip>
                  <v-chip v-else color="grey" size="x-small" class="text-white">
                    No Doc
                  </v-chip>
                </div>
              </template>

              <!-- Priority column -->
              <template v-slot:item.priority="{ item }">
                <div @click="viewMessage(item)" style="cursor: pointer">
                  <v-chip
                    v-if="item.priority"
                    :color="getPriorityColor(item.priority)"
                    size="x-small"
                    class="text-white"
                  >
                    {{ item.priority }}
                  </v-chip>
                  <span v-else>-</span>
                </div>
              </template>

              <!-- Date column -->
              <template v-slot:item.created_at="{ item }">
                <div @click="viewMessage(item)" style="cursor: pointer">
                  <span class="message-date">{{
                    formatDate(item.created_at)
                  }}</span>
                </div>
              </template>

              <!-- Actions column -->
              <template v-slot:item.actions="{ item }">
                <div class="justify-center d-flex">
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <Link
                        :href="route('inbox.show', item.id)"
                        class="mr-2 text-decoration-none"
                      >
                        <v-btn
                          icon
                          size="small"
                          color="blue-lighten-1"
                          v-bind="props"
                        >
                          <v-icon>mdi-eye</v-icon>
                        </v-btn>
                      </Link>
                    </template>
                    View
                  </v-tooltip>

                  <!-- <v-tooltip location="top">
                                        <template v-slot:activator="{ props }">
                                            <v-btn icon size="small" color="error" @click.stop="confirmDelete(item)"
                                                v-bind="props">
                                                <v-icon>mdi-delete-outline</v-icon>
                                            </v-btn>
                                        </template>
                                        Delete
                                    </v-tooltip> -->
                </div>
              </template>

              <!-- Empty state -->
              <template v-slot:no-data>
                <div class="text-center pa-8">
                  <v-icon size="large" color="grey-lighten-1" class="mb-4"
                    >mdi-email-outline</v-icon
                  >
                  <div class="mb-2 text-h6" v-if="activeTab === 'All'">
                    No messages in your inbox
                  </div>
                  <div class="mb-2 text-h6" v-else>
                    No messages in the {{ activeTab }} category
                  </div>
                  <div class="text-body-2">
                    Messages will appear here when they are received
                  </div>
                </div>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="deleteDialog" max-width="500">
      <v-card>
        <v-card-title class="text-white text-h5 bg-error"
          >Confirm Delete</v-card-title
        >
        <v-card-text class="pt-4">
          <p>Are you sure you want to delete this message?</p>
          <p v-if="messageToDelete" class="mt-2 font-weight-medium">
            Subject: {{ messageToDelete.subject }}
          </p>
          <p class="mt-4 text-error font-weight-bold">
            This action cannot be undone.
          </p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="deleteDialog = false"
          >
            Cancel
          </v-btn>
          <v-btn
            color="error"
            variant="text"
            @click="deleteMessage"
            :loading="deleting"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Document Preview Modal -->
    <v-dialog
      v-model="documentPreviewDialog"
      max-width="90%"
      max-height="90%"
      transition="dialog-bottom-transition"
    >
      <v-card>
        <v-toolbar dark color="primary">
          <v-btn icon dark @click="documentPreviewDialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
          <v-toolbar-title v-if="selectedDocument">
            {{ selectedDocument.control_number }} ({{
              selectedDocument.reference_document || ""
            }})
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <v-toolbar-items>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-if="
                    selectedDocument && isImageFile(selectedDocument.file_path)
                  "
                  icon
                  @click="zoomIn"
                  v-bind="props"
                >
                  <v-icon>mdi-magnify-plus</v-icon>
                </v-btn>
              </template>
              <span>Zoom In</span>
            </v-tooltip>

            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-if="
                    selectedDocument && isImageFile(selectedDocument.file_path)
                  "
                  icon
                  @click="zoomOut"
                  v-bind="props"
                >
                  <v-icon>mdi-magnify-minus</v-icon>
                </v-btn>
              </template>
              <span>Zoom Out</span>
            </v-tooltip>

            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-if="
                    selectedDocument && isImageFile(selectedDocument.file_path)
                  "
                  icon
                  @click="resetZoom"
                  v-bind="props"
                >
                  <v-icon>mdi-magnify-scan</v-icon>
                </v-btn>
              </template>
              <span>Reset Zoom</span>
            </v-tooltip>
          </v-toolbar-items>
        </v-toolbar>
        <v-card-text class="pa-0">
          <div class="document-preview-modal-container">
            <div
              v-if="selectedDocument && isImageFile(selectedDocument.file_path)"
              class="image-preview-container"
            >
              <img
                :src="getFileUrl(selectedDocument.file_path)"
                alt="Document Preview"
                class="document-preview-image"
                :style="{ transform: `scale(${zoomLevel})` }"
              />
            </div>
            <div
              v-else-if="selectedDocument && selectedDocument.file_path"
              class="pdf-preview-container"
            >
              <iframe
                :src="getFileUrl(selectedDocument.file_path)"
                class="pdf-preview-frame"
                frameborder="0"
              ></iframe>
            </div>
            <div v-else class="no-document-container">
              <v-icon size="large" color="grey"
                >mdi-file-document-outline</v-icon
              >
              <p class="mt-4">No document available for preview</p>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Debug Information Dialog -->
    <v-dialog v-model="showDebugDialog" max-width="800px">
      <v-card>
        <v-toolbar dark color="info">
          <v-toolbar-title>Debug Information</v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn icon dark @click="showDebugDialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
      </v-card>
    </v-dialog>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
    inboxMessages: Object,
    unreadCount: Number,
    referenceTypeCounts: Object,
    activeReference: String,
});

// Reference dropdown options (All, F01-F04)
const activeTab = ref(props.activeReference || 'All');

const search = ref('');
const loading = ref(false);
const deleting = ref(false);
const currentPage = ref(props.inboxMessages ? props.inboxMessages.current_page : 1);
const deleteDialog = ref(false);
const messageToDelete = ref(null);
const itemsPerPage = ref(10);

// Initialize component
onMounted(() => {
    // Set activeTab based on props.activeReference, defaulting to 'All' if not specified
    activeTab.value = props.activeReference || 'All';
});

// Document preview modal state
const documentPreviewDialog = ref(false);
const selectedDocument = ref(null);
const zoomLevel = ref(1);
const itemsPerPageOptions = [
    { title: '5', value: 5 },
    { title: '10', value: 10 },
    { title: '15', value: 15 },
    { title: '20', value: 20 },
    { title: 'All', value: -1 },
];

// Define table headers
const headers = [
    { title: 'Sender', key: 'sender', sortable: true, width: '180px' },
    { title: 'Subject Matter', key: 'subject', sortable: true },
    { title: 'Document', key: 'document', sortable: false },
    { title: 'Reference', key: 'reference', sortable: true, width: '120px' },
    { title: 'Priority', key: 'priority', sortable: true, width: '100px' },
    { title: 'Date', key: 'created_at', sortable: true, width: '120px' },
    { title: 'Actions', key: 'actions', sortable: false, width: '100px' },
];

// Process messages to add category and selected properties
const processedMessages = computed(() => {
    // Check if inboxMessages exists
    if (!props.inboxMessages) {
        return [];
    }

    // Handle the case where inboxMessages.data might not exist or might not be an array
    let messagesData = [];

    // Check if inboxMessages.data exists and is an array
    if (props.inboxMessages.data && Array.isArray(props.inboxMessages.data)) {
        messagesData = props.inboxMessages.data;
    }
    // If inboxMessages.data exists but is not an array, it might be a paginated object
    else if (props.inboxMessages.data) {
        // Try to access data property if it exists (might be nested)
        if (props.inboxMessages.data.data && Array.isArray(props.inboxMessages.data.data)) {
            messagesData = props.inboxMessages.data.data;
        } else {
            // If we can't find a data array, convert the object to an array if possible
            try {
                // If it's an object with numeric keys, convert to array
                const keys = Object.keys(props.inboxMessages.data).filter(key => !isNaN(parseInt(key)));
                if (keys.length > 0) {
                    messagesData = keys.map(key => props.inboxMessages.data[key]);
                }
            } catch (e) {
                // Silently handle errors
            }
        }
    }

    // If there are no messages, return empty array
    if (messagesData.length === 0) {
        return [];
    }

    // Process each message to ensure it has a category
    try {
        const processed = messagesData.map(message => {
            // Use the reference column directly, with fallback to F04 if null/undefined
            // This ensures all messages have a valid category
            let category = message.reference || 'F04';

            return {
                ...message,
                category
            };
        });

        return processed;
    } catch (error) {
        return [];
    }
});

// Filter messages based on active tab
const filteredMessages = computed(() => {
    // Ensure processedMessages.value is an array
    const messages = Array.isArray(processedMessages.value)
        ? processedMessages.value
        : [];

    if (messages.length === 0) {
        return [];
    }

    // Helper function for search filtering
    const matchesSearchTerm = (message) => {
        if (!search.value) {
            return true; // No search term, so all messages match
        }

        const searchLower = search.value.toLowerCase();

        // Check if subject contains search term
        const subjectMatches = message.subject &&
            message.subject.toLowerCase().includes(searchLower);

        // Check if sender name contains search term
        const senderMatches = message.sender &&
            message.sender.name &&
            message.sender.name.toLowerCase().includes(searchLower);

        return subjectMatches || senderMatches;
    };

    // IMPORTANT: When 'All' is selected, only apply search filtering, not reference filtering
    if (activeTab.value === 'All') {
        return messages.filter(matchesSearchTerm);
    }

    // For specific reference filters (F01, F02, etc.), filter by both search term and reference category
    return messages.filter(message => {
        // First check if message matches search term
        const matchesSearch = matchesSearchTerm(message);

        // Then check if message matches the selected reference category
        const matchesCategory = message.category === activeTab.value;

        // Message must match both conditions
        return matchesSearch && matchesCategory;
    });
});

// Get unread count for each tab
const getTabUnreadCount = (tabName) => {
    // Check if processedMessages has a value and is an array
    if (!processedMessages.value || !Array.isArray(processedMessages.value)) {
        return 0;
    }

    // If we have backend counts, use those
    if (props.referenceTypeCounts && props.referenceTypeCounts[tabName] !== undefined) {
        // Count only unread messages in this category
        return processedMessages.value.filter(message =>
            message.category === tabName && !message.is_read
        ).length;
    }

    // Fallback to frontend counting
    return processedMessages.value.filter(message =>
        message.category === tabName && !message.is_read
    ).length;
};

// Reference dropdown options as computed property to update when unread counts change
const referenceOptions = computed(() => [
    {
        title: `All ${props.unreadCount > 0 ? `(${props.unreadCount})` : ''}`,
        value: 'All'
    },
    {
        title: `F01 ${getTabUnreadCount('F01') > 0 ? `(${getTabUnreadCount('F01')})` : ''}`,
        value: 'F01'
    },
    {
        title: `F02 ${getTabUnreadCount('F02') > 0 ? `(${getTabUnreadCount('F02')})` : ''}`,
        value: 'F02'
    },
    {
        title: `F03 ${getTabUnreadCount('F03') > 0 ? `(${getTabUnreadCount('F03')})` : ''}`,
        value: 'F03'
    },
    {
        title: `F04 ${getTabUnreadCount('F04') > 0 ? `(${getTabUnreadCount('F04')})` : ''}`,
        value: 'F04'
    },
]);

const getPriorityColor = (priority) => {
    switch (priority) {
        case 'urgent':
            return 'error';
        case 'routinary':
            return 'success';
    }
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);

    // If the message is from today, show only the time
    if (date.toDateString() === now.toDateString()) {
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
        });
    }
    // If the message is from yesterday, show "Yesterday"
    else if (date.toDateString() === yesterday.toDateString()) {
        return 'Yesterday';
    }
    // If the message is from this year, show month and day
    else if (date.getFullYear() === now.getFullYear()) {
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
        });
    }
    // Otherwise show the full date
    else {
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    }
};

// Helper function to check if a file is an image
const isImageFile = (filePath) => {
    if (!filePath) return false;
    const extension = filePath.split('.').pop().toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension);
};

// Helper function to get the file URL
const getFileUrl = (filePath) => {
    if (!filePath) return '';
    return `/storage/${filePath}`;
};

// Handle table options changes (pagination, sorting, etc.)
const handleTableOptions = (options) => {
    if (options.itemsPerPage !== itemsPerPage.value) {
        itemsPerPage.value = options.itemsPerPage;
    }

    // Handle page changes
    if (options.page !== currentPage.value) {
        changePage(options.page);
    }
};

const changePage = (page) => {
    loading.value = true;

    // Preserve the active reference when changing pages, but only include reference parameter if not 'All'
    let url = `/inbox?page=${page}`;
    if (activeTab.value !== 'All') {
        url += `&reference=${activeTab.value}`;
    }

    router.visit(url, {
        preserveState: true,
        onSuccess: () => {
            loading.value = false;
            currentPage.value = page;
        },
        onError: (errors) => {
            loading.value = false;
            console.error('Error changing page:', errors);
        }
    });
};



const confirmDelete = (item) => {
    messageToDelete.value = item;
    deleteDialog.value = true;
};

const deleteMessage = () => {
    if (messageToDelete.value) {
        deleting.value = true;
        router.delete(`/inbox/${messageToDelete.value.id}`, {
            onSuccess: () => {
                deleteDialog.value = false;
                messageToDelete.value = null;
                deleting.value = false;
            },
            onError: () => {
                deleting.value = false;
            }
        });
    }
};

// View message function
const viewMessage = (item) => {
    router.visit(route('inbox.show', item.id));
};

// Change reference dropdown selection
const changeReference = (newReference) => {
    // Update the active tab immediately for better UI responsiveness
    activeTab.value = newReference;

    // Navigate to the selected reference
    // IMPORTANT: For "All" filter, use the base URL without any query parameters
    let url = '/inbox';
    if (newReference !== 'All') {
        url += `?reference=${newReference}`;
    }

    // Show loading indicator
    loading.value = true;

    // Use router.visit to navigate to the new URL (better compatibility with Inertia.js)
    router.visit(url, {
        onSuccess: (page) => {
            loading.value = false;

            // Ensure the activeTab is in sync with the server response
            // If no activeReference is provided from the server, default to 'All'
            activeTab.value = page.props.activeReference || 'All';
        },
        onError: () => {
            loading.value = false;
        },
        // Force a complete page reload to ensure we get fresh data
        preserveState: false
    });
};

// Document preview functions
const openDocumentPreview = (document) => {
    selectedDocument.value = document;
    documentPreviewDialog.value = true;
    zoomLevel.value = 1; // Reset zoom level when opening a new document
};

// Zoom functions for image documents
const zoomIn = () => {
    zoomLevel.value = Math.min(zoomLevel.value + 0.25, 3); // Max zoom: 3x
};

const zoomOut = () => {
    zoomLevel.value = Math.max(zoomLevel.value - 0.25, 0.5); // Min zoom: 0.5x
};

const resetZoom = () => {
    zoomLevel.value = 1; // Reset to original size
};

// Function to get a shortened version of the message
const getShortMessage = (message) => {
    if (!message) return '';

    // Remove any verbose text like "A document has been forwarded to your operating unit."
    const cleanMessage = message.replace(/A document has been forwarded to your operating unit\.\s*/g, '');

    // Split by newlines and filter out empty lines
    const lines = cleanMessage.split('\n').filter(line => line.trim() !== '');

    // Join the first 2 non-empty lines with a comma
    return lines.slice(0, 2).join(', ');
};



// Function to handle new message notifications
const handleNewMessage = (data) => {
    // Show notification
    const notification = new Notification('New Inbox Message', {
        body: `From: ${data.sender.name}\nSubject: ${data.subject}`,
        icon: '/favicon.ico'
    });

    // Reload the page when notification is clicked
    notification.onclick = () => {
        window.focus();
        router.reload();
    };

    // Refresh the page to show the new message
    router.reload();
};

// Set up Echo listeners when component is mounted
onMounted(() => {
    try {
        // Request notification permission
        if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
            Notification.requestPermission();
        }

        // Listen for private channel events
        if (window.Echo && typeof $page !== 'undefined' && $page.props && $page.props.auth && $page.props.auth.user) {
            const userId = $page.props.auth.user.id;

            if (userId) {
                window.Echo.private(`inbox.${userId}`)
                    .listen('.new.message', (data) => {
                        handleNewMessage(data);
                    });

                console.log(`Listening for new messages on channel: inbox.${userId}`);
            } else {
                console.warn('User ID not available for Echo listener setup');
            }
        } else {
            console.warn('Echo or user authentication data not available');
        }
    } catch (error) {
        console.error('Error setting up Echo listeners:', error);
    }
});

// Clean up Echo listeners when component is unmounted
onUnmounted(() => {
    try {
        if (window.Echo && typeof $page !== 'undefined' && $page.props && $page.props.auth && $page.props.auth.user) {
            const userId = $page.props.auth.user.id;

            if (userId) {
                window.Echo.leave(`inbox.${userId}`);
                console.log('Stopped listening for new messages');
            }
        }
    } catch (error) {
        console.error('Error cleaning up Echo listeners:', error);
    }
});
</script>

<style scoped>
/* Basic styling */
.border-b {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

/* Table header styling */
:deep(.v-data-table-header th) {
  font-weight: bold;
  color: rgba(0, 0, 0, 0.87) !important;
  background-color: white !important;
}

:deep(.v-data-table .v-data-table__td) {
  color: rgba(0, 0, 0, 0.87) !important;
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}

/* Remove blue focus outline from input fields */
:deep(.v-field__input:focus-visible) {
  outline: none !important;
}

/* Table row hover styling */
:deep(.v-data-table .v-data-table__tr:hover) {
  background-color: rgba(25, 118, 210, 0.05) !important;
}

/* Pagination styling */
:deep(.v-data-table-footer) {
  border-top: thin solid rgba(0, 0, 0, 0.12);
  padding: 0 16px;
}

:deep(.v-data-table-footer__items-per-page) {
  margin-right: 16px;
}

:deep(.v-data-table-footer__info) {
  margin-right: 16px;
}

/* Button styling */
:deep(.v-btn--icon.v-btn--density-default) {
  width: 36px;
  height: 36px;
}

/* Tooltip styling */
:deep(.v-tooltip > .v-overlay__content) {
  background-color: rgba(33, 33, 33, 0.9);
  color: white;
  border-radius: 4px;
  font-size: 0.75rem;
  padding: 4px 8px;
}

/* Reference dropdown styling */
.reference-select {
  min-width: 150px;
  max-width: 180px;
  margin-left: 8px;
}

/* Sender name styling */
.sender-name {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  font-size: 14px;
  margin-left: 0;
  padding-left: 0;
}

/* Subject styling */
.subject {
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
}

/* Message preview styling */
.message-preview {
  margin-top: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
}

.message-preview-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300px;
  display: inline-block;
}

/* Date styling */
.message-date {
  white-space: nowrap;
  color: #1a73e8;
  font-weight: 600;
  font-size: 14px;
  text-align: right;
}

/* Improve chip visibility */
.v-chip.v-chip--size-x-small {
  font-size: 12px;
  height: 22px;
  font-weight: 600;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Unread message styling */
.unread-message {
  background-color: #f2f6fc !important;
  font-weight: 500;
}

/* Hide the extra pagination arrows at the bottom of the page */
:deep(.flex.items-center.gap-4) {
  display: none !important;
}

/* Document preview styling */
.document-preview-container {
  width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  transition: transform 0.2s ease;
}

.document-preview-container:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #1976d2;
}

.document-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pdf-thumbnail {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

/* Document preview modal styling */
.document-preview-modal-container {
  width: 100%;
  height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  overflow: auto;
}

.image-preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 20px;
}

.document-preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pdf-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pdf-preview-frame {
  width: 100%;
  height: 100%;
  border: none;
}

.no-document-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #757575;
}
</style>
