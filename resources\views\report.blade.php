<!DOCTYPE html>
<html>

<head>
    <title>PDF Report</title>
    <style>
        @page {
            margin-top: 160px;
            margin-bottom: 60px;
        }

        body {
            font-family: 'Trebuchet';
            font-size: 12px;
        }

        .page-container {
            position: fixed;
            top: -120PX;
            left: 0;
            width: 100%;
            height: 110px;
            border: 1px solid black;
        }

        .header-left {
            position: absolute;
            left: 0;
            top: 0;
            width: 120px;
            border-right: 1px solid black;
        }

        .logo {
            width: 80%;
            padding-left: 12px;
            padding-top: 7px;
            padding-bottom: 7px;
        }

        .header-right {
            position: absolute;
            height: 60px;
            left: 120px;
            top: 0;
            right: 0;
            font-size: 18px;
            text-align: center;
            padding: 25px 0px;
        }

        h2 {
            background-color: #2e7d32;
            color: white;
            padding: 10px;
            margin-bottom: 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-family: sans-serif;
            font-size: 10px;
        }

        .content-wrapper {
            margin-top: 120px;
            /* Push content down below the fixed header */
        }

        table,
        th,
        td {
            border: 1px solid black;
        }

        td,
        th {
            padding: 3px;
            text-align: center;
        }

        .footer {
            margin-top: 20px;
            font-size: 10px;
        }
    </style>
</head>

<body>
    <div class="page-container">
        <div class="header-left">
            <img class="logo" src="{{ public_path('dmmmsu.png') }}" alt="DMMMSU Logo" />
        </div>
        <div class="header-right">COLLECTION AND IDENTIFICATION FORM FOR EXTERNALLY <br>
            GENERATED DOCUMENTS
        </div>
    </div>
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Date of Document</th>
                <th>Signatory</th>
                <th>Origin</th>
                <th>Subject Matter</th>
                <th>Referred To</th>
                <th>Document Code</th>
                <th>Reference Document</th>
                <th>Control Number</th>
                <th>Received By</th>
                <th>Remarks</th>
            </tr>
        </thead>
        <tbody>
            @php $row = 1; @endphp
            @forelse($documents as $doc)
            <tr>
                <td>{{ $row }}</td>
                <td>{{ \Carbon\Carbon::parse($doc['created_at'])->format('Y-m-d') }}</td>
                <td>{{ $doc['signatory'] }}</td>
                <td>{{ $doc['origin'] }}</td>
                <td>{{ $doc['subject_matter'] }}</td>
                <td>{{ $doc['referred_to'] }}</td>
                <td>{{ $doc['document_code'] }}</td>
                <td>{{ $doc['reference_document'] }}</td>
                <td>{{ $doc['control_number'] }}</td>
                <td>{{ $doc['received_by']['name'] ?? '—' }}</td>
                <td>{{ $doc['remarks'] }}</td>
            </tr>
            @php $row++; @endphp
            @empty
            <tr>
                <td colspan="11">No documents found.</td>
            </tr>
            @endforelse
        </tbody>
    </table>
    <div class="footer">
        <p>DMMMSU-REC-02-F004<br />
            Rev No. 00 (07.15.2020)</p>
    </div>
</body>


</html>