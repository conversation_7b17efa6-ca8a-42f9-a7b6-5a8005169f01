<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Inertia\Response;

class CustomLoginController extends Controller
{
    /**
     * Display the login view.
     */
    public function showLoginForm(): Response
    {
        return Inertia::render('Auth/Login', [
            'canResetPassword' => Route::has('password.request'),
            'status' => session('status'),
        ]);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function login(Request $request): RedirectResponse
    {
        // Define custom validation messages
        $messages = [
            'email.required' => trans('auth.email_required'),
            'email.email' => trans('auth.email_invalid'),
            'password.required' => trans('auth.password_required'),
        ];

        // Validate the form data with custom messages
        $validator = Validator::make($request->all(), [
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ], $messages);

        if ($validator->fails()) {
            // For validation failures, we'll return the specific error messages
            // Using a more explicit approach with Inertia
            return redirect()->back()
                ->withInput($request->except('password'))
                ->withErrors($validator);
        }

        // Check if the email exists in our database
        $user = \App\Models\User::where('email', $request->email)->first();

        if (!$user) {
            // If email doesn't exist, return a specific error for the email field
            // Using a more explicit approach with Inertia
            return redirect()->back()
                ->withInput($request->except('password'))
                ->withErrors([
                    'email' => trans('auth.email_not_found'),
                ]);
        }

        // Attempt to authenticate
        if (!Auth::attempt($request->only('email', 'password'), $request->boolean('remember'))) {
            // If authentication fails at this point, it means the password is incorrect
            // Using a more explicit approach with Inertia
            return redirect()->back()
                ->withInput($request->except('password'))
                ->withErrors([
                    'password' => trans('auth.password_incorrect'),
                ]);
        }

        // If authentication is successful, regenerate the session
        $request->session()->regenerate();

        // Redirect to the intended page or dashboard
        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function logout(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
