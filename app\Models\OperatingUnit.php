<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OperatingUnit extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'email',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        // No casts needed
    ];

    /**
     * Get the documents that originated from this operating unit.
     */
    public function documents(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Document::class, 'origin_unit_id');
    }

    /**
     * Get the documents that are currently owned by this operating unit.
     */
    public function currentDocuments(): Has<PERSON>any
    {
        return $this->hasMany(Document::class, 'current_unit_id');
    }

    /**
     * Get the users associated with this operating unit.
     */
    public function users(): Has<PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the department units associated with this operating unit.
     */
    public function departmentUnits(): Has<PERSON>any
    {
        return $this->hasMany(DepartmentUnit::class);
    }

    public function documentCodes()
    {
        return $this->hasMany(DocumentCode::class);
    }

    /**
     * Check if this operating unit is the Central Administration unit.
     *
     * @return bool
     */
    public function isCentralAdministration(): bool
    {
        return $this->name === 'Central Administration' ||
            $this->code === 'CENTRAL' ||
            $this->code === 'CA';
    }
}
