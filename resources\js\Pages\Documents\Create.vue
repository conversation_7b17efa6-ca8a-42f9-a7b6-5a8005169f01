<template>

  <Head title="Create Document" />
  <AppSidebarLayout>
    <v-container>
      <v-row>
        <v-col>
          <v-card>
            <v-card-title class="py-3 text-white bg-green-900">
              Register New Document
            </v-card-title>
            <v-card-text class="bg-gray-50 pa-6">
              <v-form @submit.prevent="submit">
                <div class="form-grid">
                  <div class="form-row">
                    <div class="form-label">Document Date:</div>
                    <!-- <div class="form-value">{{ currentDate }}</div> -->
                    <div class="form-value">
                      <v-text-field required v-model="form.date_received" type="date" variant="outlined"
                        bg-color="white" color="primary" density="compact" hide-details>
                      </v-text-field>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-label">Received By:</div>
                    <div class="form-value">
                      {{ $page.props.auth.user.name }}
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-label">Reference Document:</div>
                    <div class="form-field">
                      <v-select v-model="form.reference_document" :items="referenceDocuments" :error-messages="errors.reference_document ||
                        validationErrors.reference_document
                        " required variant="outlined" bg-color="white" color="primary" density="compact" hide-details
                        @update:model-value="updateControlNumber"></v-select>
                    </div>
                    <div class="ml-4 form-label">Control Number:</div>
                    <div class="form-field">
                      <v-text-field v-model="displayControlNumber" readonly variant="outlined" bg-color="white"
                        color="primary" hide-details density="compact"></v-text-field>
                    </div>
                  </div>

                  <div v-if="showDocumentCodeField" class="form-row">
                    <div class="form-label">Document Code:</div>
                    <div class="form-field-full">
                      <v-select v-model="form.document_code" :items="documentCodes" :error-messages="errors.document_code || validationErrors.document_code
                        " item-title="label" item-value="value" required hide-details density="compact"
                        bg-color="white" variant="outlined" color="primary"></v-select>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-label">Origin Office:</div>
                    <div class="form-field-full">
                      <v-autocomplete hide-details v-model="form.origin" :items="filteredOriginOffices"
                        :loading="loadingOriginOffices" :error-messages="errors.origin || validationErrors.origin
                          " required density="compact" variant="outlined" color="primary" auto-select-first clearable
                        :search-input="originSearch" @update:search="updateOriginSearch"
                        placeholder="Type to search or add a new origin office" bg-color="white">
                        <template v-slot:prepend-inner>
                          <v-icon color="green-darken-3" size="medium">mdi-office-building</v-icon>
                        </template>
                        <template v-slot:no-data>
                          <v-list-item>
                            <v-list-item-title>
                              No matching origin offices found. Type to create a
                              new one.
                            </v-list-item-title>
                          </v-list-item>
                        </template>
                      </v-autocomplete>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-label">Subject Matter:</div>
                    <div class="form-field-full">
                      <v-textarea hide-details v-model="form.subject_matter" :error-messages="errors.subject_matter ||
                        validationErrors.subject_matter
                        " required variant="outlined" color="primary" rows="4" auto-grow bg-color="white"></v-textarea>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-label">Referred to:</div>
                    <div class="form-field-full">
                      <v-text-field v-model="form.referred_to" :error-messages="errors.referred_to || validationErrors.referred_to
                        " required hide-details density="compact" variant="outlined" color="primary"></v-text-field>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-label">Signatory:</div>
                    <div class="form-field-full">
                      <v-autocomplete hide-details v-model="form.signatory" :items="filteredSignatories"
                        :loading="loadingSignatories" :error-messages="errors.signatory || validationErrors.signatory
                          " required density="compact" variant="outlined" color="primary" auto-select-first clearable
                        :search-input="signatorySearch" @update:search="updateSignatorySearch"
                        placeholder="Type to search or add a new signatory" bg-color="white">
                        <template v-slot:prepend-inner>
                          <v-icon color="primary" size="small">mdi-draw</v-icon>
                        </template>
                        <template v-slot:no-data>
                          <v-list-item>
                            <v-list-item-title>
                              No matching signatories found. Type to create a
                              new one.
                            </v-list-item-title>
                          </v-list-item>
                        </template>
                      </v-autocomplete>
                    </div>
                  </div>
                </div>

                <div class="justify-end mt-8 d-flex">
                  <v-btn color="error" variant="outlined" class="px-6 py-3 text-button mr-4" size="large"
                    @click="showCancelDialog">
                    CANCEL
                  </v-btn>
                  <v-btn type="submit" color="green-darken-1" variant="flat" :loading="form.processing"
                    class="px-8 py-3 text-button" size="large">
                    SUBMIT NEW DOCUMENT
                  </v-btn>
                </div>
              </v-form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- Cancel Confirmation Dialog -->
    <v-dialog v-model="cancelDialog" max-width="500">
      <v-card>
        <v-card-title class="text-white text-h5 bg-error">Confirm Cancellation</v-card-title>
        <v-card-text class="pt-4">
          <p>Are you sure you want to cancel creating this document?</p>
          <p class="mt-2">Any information you have entered will be lost.</p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey-darken-1" variant="text" @click="cancelDialog = false">
            No, Continue Editing
          </v-btn>
          <v-btn color="error" variant="text" @click="confirmCancel">
            Yes, Cancel
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, useForm, usePage, router } from "@inertiajs/vue3";
import { ref, computed, watch, onMounted } from "vue";
import AppSidebarLayout from "@/Layouts/AppSidebarLayout.vue";

const props = defineProps({
  errors: Object,
  documents: Object,
  documentCodes: {
    type: Array,
    default: () => [],
  },
  originOffices: {
    type: Array,
    default: () => [],
  },
  signatories: {
    type: Array,
    default: () => [],
  },
  controlNumber: {
    type: String,
    default: () => [],
  },
});

const page = usePage();

// Dialog control
const cancelDialog = ref(false);

// Show cancel confirmation dialog
const showCancelDialog = () => {
  cancelDialog.value = true;
};

// Confirm cancellation and navigate back to documents list
const confirmCancel = () => {
  router.visit("/documents");
};

const qrCodeUrl = ref(null);
const generating = ref(false);
const printFrame = ref(null);
const displayControlNumber = ref("");
// Use document codes directly from props
const documentCodes = computed(() => props.documentCodes || []);

// Origin office autocomplete functionality
const originSearch = ref("");
const loadingOriginOffices = ref(false);
const filteredOriginOffices = ref([]);

// Signatory autocomplete functionality
const signatorySearch = ref("");
const loadingSignatories = ref(false);
const filteredSignatories = ref([]);

// Initialize the filtered data with the provided data
onMounted(() => {
  filteredOriginOffices.value = props.originOffices || [];
  filteredSignatories.value = props.signatories || [];
});


// Update the filtered origin offices based on search input
const updateOriginSearch = (val) => {
  originSearch.value = val;

  if (!val) {
    // If search is empty, show all available origin offices
    filteredOriginOffices.value = props.originOffices || [];
    return;
  }

  // Filter origin offices based on search input
  const searchLower = val.toLowerCase();
  filteredOriginOffices.value = props.originOffices.filter(office =>
    office.toLowerCase().includes(searchLower)
  );

  // If the exact search term isn't in the list, add it as an option
  if (!filteredOriginOffices.value.some(office => office.toLowerCase() === searchLower)) {
    filteredOriginOffices.value.push(val);
  }
};

// Update the filtered signatories based on search input
const updateSignatorySearch = (val) => {
  signatorySearch.value = val;

  if (!val) {
    // If search is empty, show all available signatories
    filteredSignatories.value = props.signatories || [];
    return;
  }

  // Filter signatories based on search input
  const searchLower = val.toLowerCase();
  filteredSignatories.value = props.signatories.filter(signatory =>
    signatory.toLowerCase().includes(searchLower)
  );

  // If the exact search term isn't in the list, add it as an option
  if (!filteredSignatories.value.some(signatory => signatory.toLowerCase() === searchLower)) {
    filteredSignatories.value.push(val);
  }
};

const referenceDocuments = ["F01", "F02", "F03", "F04"];

const currentDate = computed(() => {
  const now = new Date();
  return now.toLocaleDateString();
});

// Computed property to determine if Document Code field should be visible
const showDocumentCodeField = computed(() => {
  return form.reference_document === "F02" || form.reference_document === "F04";
});

// Custom validation errors
const validationErrors = ref({
  reference_document: '',
  document_code: '',
  origin: '',
  subject_matter: '',
  referred_to: '',
  date_received: '',
  signatory: ''
});

const form = useForm({
  signatory: "",
  date_received: "",
  origin: "",
  origin_unit_id: "",
  subject_matter: "",
  referred_to: "",
  document_code: "",
  reference_document: "", // No default value
  control_number: "",
});
onMounted(() => {
  const today = new Date();
  form.date_received = today.toISOString().split('T')[0]; // format: YYYY-MM-DD
});

// alert(page.props.auth.user.id);
watch(() => page.props.auth.user.id,
  (newUserId) => {
    if (newUserId) {
      form.origin_unit_id = newUserId
    }
  },
  { immediate: true } // Run the watcher immediately on setup
);


watch(displayControlNumber, (newVal) => {
  form.control_number = newVal
});

// Watch for changes in form fields to clear validation errors
watch(() => form.reference_document, (newValue) => {
  validationErrors.value.reference_document = '';
  if (newValue !== "F02" && newValue !== "F04") {
    // Clear document_code when not F02 or F04
    form.document_code = "";
  }
});

watch(() => form.document_code, () => {
  validationErrors.value.document_code = '';
});
watch(() => form.date_received, () => {
  validationErrors.value.date_received = '';
  updateControlNumber();
});

watch(() => form.origin, () => {
  validationErrors.value.origin = '';
});

watch(() => form.subject_matter, () => {
  validationErrors.value.subject_matter = '';
});

watch(() => form.referred_to, () => {
  validationErrors.value.referred_to = '';
});

watch(() => form.signatory, () => {
  validationErrors.value.signatory = '';
});



const updateControlNumber = () => {
  const userUnitId = page.props.auth.user.operating_unit.id;

  if (!form.reference_document || !userUnitId) {
    displayControlNumber.value = "";
    return;
  }

  // Use the selected date from the form
  const inputDate = new Date(form.date_received);
  if (isNaN(inputDate.getTime())) {
    displayControlNumber.value = "";
    return; // Invalid date
  }

  const day = String(inputDate.getDate()).padStart(2, '0');
  const month = String(inputDate.getMonth() + 1).padStart(2, '0');
  const year = inputDate.getFullYear();
  const dateString = `${day}${month}${year}`;

  const displayPrefix = `${form.reference_document}-${dateString}`;

  const rawDocuments = Object.values(props.documents.data);

  const matchingDocs = rawDocuments.filter(doc => {
    const parts = doc.control_number.split("-");
    if (
      doc.origin_unit_id === userUnitId &&
      parts.length === 3 &&
      parts[0] === form.reference_document
    ) {
      const docYear = parts[1].slice(-4); // get YYYY from DDMMYYYY
      return docYear === String(year);
    }
    return false;
  });

  let maxSequence = 0;
  matchingDocs.forEach(doc => {
    const parts = doc.control_number.split("-");
    if (parts.length === 3) {
      const serial = parseInt(parts[2], 10);
      if (!isNaN(serial)) {
        maxSequence = Math.max(maxSequence, serial);
      }
    }
  });

  const nextSequence = maxSequence + 1;
  const paddedSeq = String(nextSequence).padStart(3, '0');
  displayControlNumber.value = `${displayPrefix}-${paddedSeq}`;
};




// Validate form before submission
const validateForm = () => {
  let isValid = true;

  // Reset all validation errors
  Object.keys(validationErrors.value).forEach(key => {
    validationErrors.value[key] = '';
  });

  // Validate reference document
  if (!form.reference_document) {
    validationErrors.value.reference_document = 'Please select a reference document';
    isValid = false;
  }

  // Validate document code (only for F02 and F04)
  if ((form.reference_document === 'F02' || form.reference_document === 'F04') && !form.document_code) {
    validationErrors.value.document_code = 'Please select a document code';
    isValid = false;
  }

  // Validate origin
  if (!form.origin) {
    validationErrors.value.origin = 'Please enter the origin office';
    isValid = false;
  }

  // Validate subject matter
  if (!form.subject_matter) {
    validationErrors.value.subject_matter = 'Please enter the subject matter';
    isValid = false;
  }

  // Validate referred to
  if (!form.referred_to) {
    validationErrors.value.referred_to = 'Please enter who this document is referred to';
    isValid = false;
  }

  // Validate signatory
  if (!form.signatory) {
    validationErrors.value.signatory = 'Please enter the signatory';
    isValid = false;
  }

  return isValid;
};

const submit = () => {
  // Perform client-side validation first
  if (!validateForm()) {
    return;
  }

  // If validation passes, submit the form
  form.post("/documents", {
    onSuccess: (response) => {
      form.reset();
      // Let the controller handle the redirect
    },
    onError: (errors) => {
      // Map backend errors to our validation errors if needed
      Object.keys(errors).forEach(key => {
        if (validationErrors.value.hasOwnProperty(key)) {
          validationErrors.value[key] = errors[key];
        }
      });
    }
  });
};

const printUrl = computed(() => {
  if (!qrCodeUrl.value) return "";
  return `/print-qr-code?url=${encodeURIComponent(qrCodeUrl.value)}`;
});

const generateQrCode = async () => {
  generating.value = true;
  try {
    const response = await axios.post(
      `/documents/${props.document.id}/generate-qr`
    );
    qrCodeUrl.value = response.data.qr_code_url;
  } catch (error) {
    console.error("Error generating QR code:", error);
    alert(
      "Error generating QR code: " +
      (error.response?.data?.error || "Unknown error")
    );
  } finally {
    generating.value = false;
  }
};

const printQrCode = () => {
  if (printFrame.value) {
    printFrame.value.contentWindow.print();
  } else {
    // Fallback if iframe is not available
    const printWindow = window.open(printUrl.value, "_blank");
    printWindow.addEventListener("load", () => {
      printWindow.print();
    });
  }
};

</script>

<style scoped>
.form-grid {
  display: grid;
  grid-gap: 24px;
  /* Increased spacing between rows */
  width: 100%;
}

.form-row {
  display: grid;
  grid-template-columns: 180px 1fr;
  /* Wider label column */
  align-items: center;
  gap: 20px;
  /* Increased gap between label and field */
}

/* Special case for the reference document and control number row */
.form-row:nth-child(3) {
  grid-template-columns: 180px 1fr 180px 1fr;
}

.form-label {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
  font-size: 1.1rem;
}

.form-value {
  font-weight: 500;
  font-size: 1.1rem;
}

.form-field {
  width: 100%;
}

.form-field-full {
  width: 100%;
  grid-column: 2 / -1;
}

/* Increase text size in all form inputs */
:deep(.v-field__input),
:deep(.v-select__selection),
:deep(.v-textarea__input) {
  font-size: 1.1rem !important;
}

/* Increase height of form inputs for better touch targets */
:deep(.v-input--density-compact .v-field__input) {
  min-height: 44px !important;
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

/* Larger text for submit button */
.text-button {
  font-size: 1.1rem !important;
  font-weight: 500 !important;
  letter-spacing: 0.5px !important;
}

/* Error message styling */
:deep(.v-messages__message) {
  color: rgb(var(--v-theme-error)) !important;
  font-size: 0.875rem !important;
  margin-top: 4px !important;
  font-weight: 500 !important;
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .form-row:nth-child(3) {
    grid-template-columns: 180px 1fr;
  }

  .form-row:nth-child(3) .form-label:nth-child(3),
  .form-row:nth-child(3) .form-field:nth-child(4) {
    margin-top: 20px;
  }
}

@media (max-width: 600px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .form-row:nth-child(3) {
    grid-template-columns: 1fr;
  }

  .form-field-full {
    grid-column: 1;
  }

  .form-label {
    margin-bottom: 5px;
  }
}
</style>
