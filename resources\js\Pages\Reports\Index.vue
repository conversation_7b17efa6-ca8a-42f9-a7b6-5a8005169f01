<template>
  <Head title="Reports" />
  <AppSidebarLayout>
    <v-container>
      <v-row>
        <v-col cols="4">
          <!-- Generation Panel -->
          <v-card>
            <v-card-title class="text-white bg-primary">
              Report Generation
            </v-card-title>
            <v-card-text class="mt-4">
              <v-form>
                <!-- <v-text-field
                  v-model="reportName"
                  label="Report Name"
                  variant="outlined"
                  density="compact"
                  bg-color="white"
                /> -->
                <v-row class="mb-1 ma-0">
                  <v-col>
                    <v-select
                      v-model="form.frequency"
                      :items="frequencyChoices"
                      item-title="text"
                      item-value="value"
                      label="Select Frequency"
                      variant="outlined"
                      density="compact"
                      bg-color="white"
                      :error="!!form.errors.frequency"
                      :error-messages="form.errors.frequency"
                    />
                  </v-col>
                </v-row>

                <!-- Year input shown for both 'yearly' and 'monthly' -->
                <v-row
                  v-if="
                    form.frequency === 'yearly' || form.frequency === 'monthly'
                  "
                  class="mb-1 ma-0"
                >
                  <v-col>
                    <v-select
                      v-model="form.year"
                      :items="yearChoices"
                      item-title="text"
                      item-value="value"
                      label="Select Year"
                      variant="outlined"
                      density="compact"
                      bg-color="white"
                      :error="!!form.errors.year"
                      :error-messages="form.errors.year"
                    />
                  </v-col>
                  <v-col v-if="form.frequency === 'monthly'">
                    <v-select
                      v-model="form.month"
                      :items="monthChoices"
                      item-title="text"
                      item-value="value"
                      :disabled="form.frequency === 'yearly'"
                      label="Select Month"
                      variant="outlined"
                      density="compact"
                      bg-color="white"
                      :error="!!form.errors.month"
                      :error-messages="form.errors.month"
                    />
                  </v-col>
                </v-row>
                <v-row
                  v-if="
                    form.frequency === 'yearly' || form.frequency === 'monthly'
                  "
                  class="mb-1 ma-0"
                >
                  <v-col>
                    <label class="text-subtitle-2 mb-2 d-block"
                      >Select Reference Documents</label
                    >

                    <v-checkbox
                      v-model="selectAll"
                      label="All"
                      @change="toggleAllReferenceDocs"
                      density="compact"
                      hide-details
                    />

                    <v-checkbox
                      v-for="(item, index) in referenceDocumentChoices"
                      :key="index"
                      :label="item.label"
                      v-model="item.checked"
                      density="compact"
                      hide-details
                    />
                  </v-col>
                </v-row>

                <v-row v-if="form.frequency" class="mb-1 ma-0">
                  <v-col>
                    <v-btn
                      :disabled="!form.frequency"
                      @click="generateReport"
                      color="blue-lighten-1"
                      block
                    >
                      Generate Report
                    </v-btn>
                  </v-col>
                </v-row>
                <!-- <v-row>
                  <v-col>
                    <v-container>
                      <v-btn color="primary" @click="openPdf"
                        >Open Hello PDF</v-btn
                      >
                    </v-container>
                  </v-col>
                </v-row> -->
              </v-form>
            </v-card-text>
          </v-card>
        </v-col>
        <v-col>
          <v-card v-if="reportCard">
            <v-card-title class="text-white bg-primary">
              <div v-if="form.frequency === 'yearly'">
                Reports within {{ form.year }}
              </div>
              <div v-else-if="form.frequency === 'monthly'">
                Reports within
                {{ form.monthText }}
                {{ form.year }}
              </div>
            </v-card-title>
            <v-data-table
              :headers="headers"
              :items="tableData"
              hide-default-footer
            >
              <template v-slot:item.actions="{ item }">
                <div class="d-flex gap-3">
                  <v-btn
                    size="small"
                    color="blue-lighten-1"
                    icon
                    @click="viewDocument(item)"
                  >
                    <v-icon>mdi-eye</v-icon>
                    <!-- View icon -->
                  </v-btn>
                </div>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- Modal Dialog boxes -->

    <v-dialog v-model="reportModal" max-width="90%">
      <v-card>
        <v-card-title class="text-white bg-primary">
          <div v-if="form.frequency === 'yearly'">
            Reference Document ({{ selectedReference }}) as of {{ form.year }}
          </div>
          <div v-else-if="form.frequency === 'monthly'">
            Reference Document ({{ selectedReference }}) as of
            {{ form.monthText }} {{ form.year }}
          </div>
        </v-card-title>
        <v-card-text>
          <v-row>
            <v-col
              class="border-2 border-black d-flex justify-center text-center pa-1"
              cols="2"
            >
              <img src="/dmmmsu.png" alt="" width="125px" />
            </v-col>
            <v-col
              class="border-2 border-black uppercase d-flex text-center justify-center pa-7"
            >
              <h1 class="report-name">
                Collection and Identification Form For Externally<br />Generated
                Documents
              </h1>
            </v-col>
          </v-row>
          <v-row>
            <v-col class="pa-0 py-3">
              <table
                class="min-w-full border-2 border-black text-sm"
                v-if="filteredDocuments.length"
              >
                <thead class="bg-white">
                  <tr>
                    <!-- Row Number Header -->
                    <th class="border-2 border-black px-4 py-2 text-left">#</th>

                    <!-- Dynamic Headers -->
                    <th
                      v-for="key in reportColumns"
                      :key="key"
                      class="border-2 border-black px-4 py-2 text-left"
                    >
                      {{ formatHeader(key) }}
                    </th>
                  </tr>
                </thead>

                <tbody>
                  <tr v-for="(doc, index) in filteredDocuments" :key="doc.id">
                    <!-- Incremental Row Number -->
                    <td class="border-2 border-black px-4 py-2">
                      {{ index + 1 }}
                    </td>

                    <!-- Dynamic Row Values -->
                    <td
                      v-for="key in reportColumns"
                      :key="key"
                      class="border-2 border-black px-4 py-2"
                    >
                      {{ formatValue(key, doc[key]) }}
                    </td>
                  </tr>
                </tbody>
              </table>

              <!-- Fallback -->
              <div v-else class="text-gray-500 text-sm mt-2">
                No documents created at this date.
              </div>
            </v-col>
          </v-row>
          <v-row>
            <v-col class="pa-0">
              <div class="revision">
                <p class="">DMMMSU-REC-02-F004</p>
                <p>Rev No. 00 (07.15.2020)</p>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions
          class="bg-white"
          style="
            position: sticky;
            bottom: 0;
            z-index: 10;
            border-top: 1px solid #ccc;
          "
        >
          <v-spacer />
          <v-btn color="primary" @click="reportModal = false">Close</v-btn>
          <v-btn @click="reportsPDF" class="bg-blue-lighten-1 px-6"
            >Print Report</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="isGenerating" persistent width="300">
      <v-card class="pa-4 d-flex flex-column align-center">
        <v-progress-circular indeterminate color="primary" class="mb-3" />
        <div>Generating PDF... Please wait</div>
      </v-card>
    </v-dialog>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, useForm, usePage, router } from "@inertiajs/vue3";
import { ref, watch, reactive, computed } from 'vue';  
import axios from 'axios';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

// Props
const props = defineProps({
	documents: Object,
});

// Form Handling
const form = useForm({
	frequency: null,
  year: null,
  month: null,
  reference_document: []
});

// Error Handling
const validateFields =  () => {
  form.clearErrors();

  const errors  = {};

  if (!form.frequency) {
    errors.frequency = 'Frequency Required';
  }

  if ((form.frequency === 'yearly' || form.frequency === 'monthly') && !form.year) {
    errors.year = 'Year is required.';
  }

  if (form.frequency === 'monthly' && !form.month) {
    errors.month = 'Month is required.';
  }

  if (Object.keys(errors).length) {
    form.setError(errors);
    return false;
  }

  return true;
};

// Reactive Variables
const isGenerating = ref(false);
const reportCard = ref(false);
const selectAll = ref(false)
const reportModal = ref(false);
const selectedReference = ref('');
const reportColumns = [
  'date_received',
  'signatory',
  'origin',
  'subject_matter',
  'referred_to',
  'document_code',
  'reference_document',
  'control_number',
  'received_by',
  'remarks',
];

// =====> Table Headers
const headers = [
	{ title: 'Reference Document', value: 'reference_document' },
	{ title: 'Actions', value: 'actions', width:"200px" },
];

// =====> Table Sample Data 
const tableData = ref([]);

// =====> Frequency Choices
const frequencyChoices = [
  { text: 'Monthly', value: 'monthly' },
  { text: 'Yearly', value: 'yearly' },
];

// =====> Year
const yearChoices = Array.from({ length: 50 }, (_, i) => {
	const year = new Date().getFullYear() - i;
	return { text: year.toString(), value: year };
});

// =====> Month
const monthChoices = [
	{ text: 'January', value: 1 },
	{ text: 'February', value: 2 },
	{ text: 'March', value: 3 },
	{ text: 'April', value: 4 },
	{ text: 'May', value: 5 },
	{ text: 'June', value: 6 },
	{ text: 'July', value: 7 },
	{ text: 'August', value: 8 },
	{ text: 'September', value: 9 },
	{ text: 'October', value: 10 },
	{ text: 'November', value: 11 },
	{ text: 'December', value: 12 },
];

// =====> Reference Document
const referenceDocumentChoices = reactive([
  { label:"F01", checked:false }, 
  { label:"F02", checked:false }, 
  { label:"F03", checked:false }, 
  { label:"F04", checked:false },
]);


// Watches
watch(() => form.frequency, (newValue) => {
  if (newValue === 'yearly') {
    form.month = null;
  }
});

// Watch individual checkboxes to update Select All
watch(
  () => referenceDocumentChoices.map(c => c.checked),
  (values) => {
    selectAll.value = values.every(Boolean)
  },
  { deep: true }
);

// Watch year and month changes to reset report card
watch(
  () => [form.frequency ,form.year, form.month],
  () => {
    reportCard.value = false;
  }
);

// Watch dropdowns clear of error when filled up
watch(
  () => [form.frequency, form.year, form.month],
  () => {
    form.clearErrors();
  }
);

// Functions

// =====> Arrow Functions
const toggleAllReferenceDocs = () => {
  referenceDocumentChoices.forEach(item => {
    item.checked = selectAll.value
  })
};

// =====> Computed Properties
const filteredDocuments = computed(() => {
  return props.documents.data
    .filter((doc) => {
      const date = new Date(doc.created_at);
      const matchesReference =
        selectedReference.value === "all" ||
        doc.reference_document === selectedReference.value;
      const matchesYear = date.getFullYear() === Number(form.year);
      const matchesMonth = form.month
        ? date.getMonth() + 1 === Number(form.month)
        : true;

      return matchesReference && matchesYear && matchesMonth;
    })
    .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
});



// =====> Traditional Functions
function viewDocument(item) {
  const referenceDocument = item.reference_document || item.label;

  reportModal.value = true; // Show modal

  // Check if it's "All Reference Documents"
  if (referenceDocument === "All Reference Documents") {
    selectedReference.value = "all"; // This triggers showing all documents
  } else {
    selectedReference.value = referenceDocument;
  }
}


function downloadDocument(item) {
  console.log('Downloading:', item.reference_document);
}

// =====> Helper Functions
function formatHeader(key) {
    if (key === 'date_received') return 'Date of Document';
  return key
    .replace(/_/g, ' ')
    .replace(/\b\w/g, (char) => char.toUpperCase()); // e.g., "control_number" → "Control Number"
}

function formatValue(key, value) {
  if (key === 'created_at' || key === 'date_received') {
    const date = new Date(value);
    const year = date.getFullYear();
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // JS months are 0-based
    return `${year}-${day}-${month}`;
  }

  if (key === 'received_by') {
    return value?.name ?? '—';
  }
  return value;
}


// testing only
function openPdf() {
  window.open('/hello-pdf', '_blank');
}

// Generate Report
const generateReport = () => {
  if(!validateFields()) return;

  reportCard.value = true;

  const checkedItems = referenceDocumentChoices.filter(item => item.checked);
  const labels = checkedItems.map(item => item.label);

  const selectedMonth = monthChoices.find(m => m.value === form.month)?.text || '';
  form.monthText = selectedMonth;

  // Base rows: map checked labels
  const rows = labels.map(label => ({
    reference_document: label
  }));

  // Conditionally add "All Reference Documents" row at the top
  if (labels.length > 1) {
    rows.unshift({ reference_document: 'All Reference Documents' });
  }

  tableData.value = rows;
  form.reference_document = labels;
};

const reportsPDF = () => {
  isGenerating.value = true; // Show loader

  axios.post('/reports/print', {
    data: filteredDocuments.value
  }, {
    responseType: 'blob' // Important for PDF
  }).then((response) => {
    const blob = new Blob([response.data], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    window.open(url, '_blank');
  }).catch(error => {
    console.error('Error generating PDF:', error);
  }).finally(() => {
    isGenerating.value = false; // Hide loader
  });
}

// const reportsPDF = async () => {
//   const query = new URLSearchParams({
//     year: form.year,
//     month: form.month || '',
//     reference: selectedReference.value
//   });

//   const response = await fetch(`/reports/print?${query.toString()}`, {
//     method: 'GET',
//     headers: { Accept: 'application/pdf' }
//   });

//   const blob = await response.blob();
//   const url = URL.createObjectURL(blob);
//   window.open(url, '_blank');;
// };
</script>

<style scope>
.form-label {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
  font-size: 1.1rem;
}

.form-field {
  width: 100%;
}

.form-field-full {
  width: 100%;
  grid-column: 2 / -1;
}

.report-name,
.revision {
  font-family: "Trebuchet MS", "Lucida Sans Unicode", "Lucida Grande",
    "Lucida Sans", Arial, sans-serif;
  font-weight: bolder;
}
.report-name {
  font-size: 18pt;
}

.revision {
  font-size: 12pt;
}
</style>