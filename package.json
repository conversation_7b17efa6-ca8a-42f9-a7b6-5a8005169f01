{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@inertiajs/vue3": "^2.0.0", "@mdi/font": "^7.4.47", "@mdi/js": "^7.4.47", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.12", "axios": "^1.6.4", "laravel-echo": "^2.1.0", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.31", "pusher-js": "^8.4.0", "tailwindcss": "^3.2.1", "vite": "^6.3.5", "vue": "^3.4.0"}, "dependencies": {"vuedraggable": "^4.1.0", "vuetify": "^3.7.19", "xlsx": "^0.18.5"}}