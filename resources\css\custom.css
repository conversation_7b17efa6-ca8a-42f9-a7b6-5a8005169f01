/* Remove blue focus outline from all form elements */
input:focus,
textarea:focus,
select:focus,
.v-field__input:focus,
.v-field--focused,
.v-field--active,
.v-text-field--focused,
.v-select--focused,
.v-input--focused,
.v-btn:focus,
.v-btn--active,
.v-btn--focused {
  outline: none !important;
  box-shadow: none !important;
}

/* Override Vuetify's focus styles */
.v-field--focused .v-field__outline,
.v-field--active .v-field__outline {
  --v-field-border-opacity: 0.5 !important;
}

/* Simple checkbox visibility for list views */
.folder-list-checkbox,
.file-list-checkbox {
  flex-shrink: 0;
}

/* Custom checkbox styling - black border, green fill when checked */
.v-checkbox .v-selection-control__wrapper {
  color: black !important;
}

.v-checkbox .v-selection-control__wrapper .v-icon {
  color: black !important;
}

.v-checkbox input:checked + .v-selection-control__wrapper {
  color: #4CAF50 !important;
}

.v-checkbox input:checked + .v-selection-control__wrapper .v-icon {
  color: #4CAF50 !important;
}

/* Ensure unchecked state has black border */
.v-checkbox:not(.v-input--is-dirty) .v-selection-control__wrapper {
  color: black !important;
}

.v-checkbox:not(.v-input--is-dirty) .v-selection-control__wrapper .v-icon {
  color: black !important;
}

/* Remove focus outline from checkboxes but keep them functional */
.v-checkbox .v-selection-control__input:focus-visible {
  outline: none !important;
}
