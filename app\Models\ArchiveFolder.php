<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ArchiveFolder extends Model
{
    protected $fillable = [
        'name',
        'description',
        'operating_unit_id',
        'created_by',
        'file_count',
    ];

    protected $casts = [
        'file_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the operating unit that owns this folder.
     */
    public function operatingUnit(): BelongsTo
    {
        return $this->belongsTo(OperatingUnit::class);
    }

    /**
     * Get the user who created this folder.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all files in this folder.
     */
    public function files(): HasMany
    {
        return $this->hasMany(ArchiveFile::class, 'folder_id');
    }

    /**
     * Update the file count for this folder.
     */
    public function updateFileCount(): void
    {
        $this->update(['file_count' => $this->files()->count()]);
    }

    /**
     * Scope to filter folders by operating unit.
     */
    public function scopeForOperatingUnit($query, $operatingUnitId)
    {
        return $query->where('operating_unit_id', $operatingUnitId);
    }
}
