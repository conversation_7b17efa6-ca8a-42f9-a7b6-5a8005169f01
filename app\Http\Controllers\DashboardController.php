<?php

namespace App\Http\Controllers;

use App\Models\DepartmentUnit;
use App\Models\Document;
use App\Models\DocumentTracking;
use App\Models\DocumentTrackingHistory;
use App\Models\OperatingUnit;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * Display the dashboard with analytics data based on user role.
     */
    public function index()
    {
        // Get the authenticated user with role relationship
        $user = Auth::user()->load('role', 'operatingUnit');

        // Initialize data arrays
        $analyticsData = [];
        $operatingUnitBreakdown = [];
        $departmentUnitBreakdown = [];

        // Get role IDs for comparison
        $superAdminRoleId = Role::where('slug', 'super_admin')->value('id'); // Should be 1
        $adminRoleId = Role::where('slug', 'admin')->value('id'); // Should be 2
        $staffRoleId = Role::where('slug', 'staff')->value('id'); // Should be 3

        // Get analytics data based on user role
        if ($user->role_id === $superAdminRoleId) {
            // For Super Admin/Central Administration users
            $analyticsData = $this->getSuperAdminAnalytics();
            $operatingUnitBreakdown = $this->getOperatingUnitBreakdown();
        } else {
            // For Admin and Staff users
            $analyticsData = $this->getUserAnalytics($user);
            $departmentUnitBreakdown = $this->getDepartmentUnitBreakdown($user);
        }

        // Get recent activities
        $recentActivities = $this->getRecentActivities($user);

        // Get status updates
        $statusUpdates = $this->getStatusUpdates($user);

        return Inertia::render('Dashboard', [
            'analyticsData' => $analyticsData,
            'operatingUnitBreakdown' => $operatingUnitBreakdown,
            'departmentUnitBreakdown' => $departmentUnitBreakdown,
            'recentActivities' => $recentActivities,
            'statusUpdates' => $statusUpdates,
            'userRoleId' => $user->role_id,
            'userRole' => $user->role->slug, // Keep this for backward compatibility
        ]);
    }

    /**
     * Get analytics data for Super Admin users.
     */
    private function getSuperAdminAnalytics()
    {
        // Get total documents
        $totalDocuments = Document::count();

        // Get forwarded documents (documents with 'forwarded' status in tracking history)
        $forwardedDocuments = Document::whereHas('trackingHistory', function ($query) {
            $query->where('status', 'forwarded');
        })->count();

        // Get received documents (documents with a received_by_id)
        $receivedDocuments = Document::whereNotNull('received_by_id')->count();

        // Get unforwarded documents (documents without 'forwarded' status in tracking history)
        $unforwardedDocuments = Document::whereDoesntHave('trackingHistory', function ($query) {
            $query->where('status', 'forwarded');
        })->count();

        return [
            'totalDocuments' => $totalDocuments,
            'forwardedDocuments' => $forwardedDocuments,
            'receivedDocuments' => $receivedDocuments,
            'unforwardedDocuments' => $unforwardedDocuments,
        ];
    }

    /**
     * Get analytics data for Admin and Staff users.
     */
    private function getUserAnalytics($user)
    {
        // Get operating unit ID
        $operatingUnitId = $user->operating_unit_id;

        // Get total documents in the user's operating unit
        $totalDocuments = Document::where('origin_unit_id', $operatingUnitId)
            ->orWhereHas('receivedBy', function ($query) use ($operatingUnitId) {
                $query->where('origin_unit_id', $operatingUnitId);
            })
            ->count();

        // Get forwarded documents from the user's operating unit
        $forwardedDocuments = Document::whereHas('trackingHistory', function ($query) {
            $query->where('status', 'forwarded');
        })
            ->where(function ($query) use ($operatingUnitId) {
                $query->where('origin_unit_id', $operatingUnitId)
                    ->orWhereHas('receivedBy', function ($subQuery) use ($operatingUnitId) {
                        $subQuery->where('origin_unit_id', $operatingUnitId);
                    });
            })
            ->count();

        // Get received documents for the user's operating unit
        $receivedDocuments = Document::whereHas('receivedBy', function ($query) use ($operatingUnitId) {
            $query->where('origin_unit_id', $operatingUnitId);
        })->count();

        // Get unforwarded documents for the user's operating unit
        $unforwardedDocuments = Document::whereDoesntHave('trackingHistory', function ($query) {
            $query->where('status', 'forwarded');
        })
            ->where(function ($query) use ($operatingUnitId) {
                $query->where('origin_unit_id', $operatingUnitId)
                    ->orWhereHas('receivedBy', function ($subQuery) use ($operatingUnitId) {
                        $subQuery->where('origin_unit_id', $operatingUnitId);
                    });
            })
            ->count();

        return [
            'totalDocuments' => $totalDocuments,
            'forwardedDocuments' => $forwardedDocuments,
            'receivedDocuments' => $receivedDocuments,
            'unforwardedDocuments' => $unforwardedDocuments,
        ];
    }

    /**
     * Get document count breakdown per operating unit with detailed metrics.
     */
    private function getOperatingUnitBreakdown()
    {
        $operatingUnits = OperatingUnit::all();
        $result = [];

        foreach ($operatingUnits as $unit) {
            // Get registered documents (documents created by this operating unit)
            $registeredCount = Document::where('origin_unit_id', $unit->id)->count();

            // Get received documents (documents received by users in this operating unit)
            $receivedCount = Document::whereHas('receivedBy', function ($query) use ($unit) {
                $query->where('origin_unit_id', $unit->id);
            })->count();

            // Get forwarded documents (documents with 'forwarded' status in tracking history from this unit)
            $forwardedCount = Document::whereHas('trackingHistory', function ($query) {
                $query->where('status', 'forwarded');
            })
                ->where('origin_unit_id', $unit->id)
                ->count();

            $result[] = [
                'name' => $unit->name,
                'code' => $unit->code,
                'receivedCount' => $receivedCount,
                'forwardedCount' => $forwardedCount,
                'registeredCount' => $registeredCount,
                // Keep the original count for backward compatibility
                'count' => $registeredCount,
            ];
        }

        // Sort by total document count (sum of all three counts) in descending order
        usort($result, function ($a, $b) {
            $totalA = $a['receivedCount'] + $a['forwardedCount'] + $a['registeredCount'];
            $totalB = $b['receivedCount'] + $b['forwardedCount'] + $b['registeredCount'];
            return $totalB - $totalA;
        });

        return $result;
    }

    /**
     * Get document count breakdown per department unit for a specific operating unit.
     */
    private function getDepartmentUnitBreakdown($user)
    {
        // Get the user's operating unit ID
        $operatingUnitId = $user->operating_unit_id;

        if (!$operatingUnitId) {
            return [];
        }

        // Get all department units belonging to the user's operating unit
        $departmentUnits = DepartmentUnit::where('operating_unit_id', $operatingUnitId)
            ->orderBy('name')
            ->get();

        if ($departmentUnits->isEmpty()) {
            return [];
        }

        // For each department unit, count documents
        // Since there's no direct relationship between documents and department units yet,
        // we'll use a placeholder approach for now
        $result = [];

        foreach ($departmentUnits as $department) {
            // In a real implementation, you would count documents associated with this department
            // For now, we'll use a random count for demonstration purposes
            $documentCount = rand(5, 50); // Replace this with actual document count logic

            $result[] = [
                'name' => $department->name,
                'code' => $department->code,
                'count' => $documentCount,
            ];
        }

        // Sort by document count in descending order
        usort($result, function ($a, $b) {
            return $b['count'] - $a['count'];
        });

        return $result;
    }

    /**
     * Get recent activities based on document tracking history.
     */
    private function getRecentActivities($user)
    {
        $query = DocumentTrackingHistory::with(['document', 'handledBy'])
            ->orderBy('tracked_at', 'desc')
            ->limit(5);

        // Get super admin role ID
        $superAdminRoleId = Role::where('slug', 'super_admin')->value('id');

        // Filter activities based on user role
        if ($user->role_id !== $superAdminRoleId) {
            $operatingUnitId = $user->operating_unit_id;

            $query->whereHas('handledBy', function ($subQuery) use ($operatingUnitId) {
                $subQuery->where('operating_unit_id', $operatingUnitId);
            });
        }

        $activities = $query->get();

        return $activities->map(function ($activity) {
            $statusColors = [
                'forwarded' => 'primary',
                'received' => 'success',
                'pending' => 'warning',
                'completed' => 'info',
                'rejected' => 'error',
            ];

            $statusIcons = [
                'forwarded' => 'mdi-send',
                'received' => 'mdi-inbox-arrow-down',
                'pending' => 'mdi-clock-outline',
                'completed' => 'mdi-check-circle',
                'rejected' => 'mdi-close-circle',
            ];

            // Get handler name safely
            $handlerName = $activity->handledBy ? $activity->handledBy->name : 'Unknown user';

            return [
                'title' => ucfirst($activity->status),
                'description' => $activity->document ?
                    "{$handlerName} {$activity->status} document #{$activity->document->control_number}" :
                    "{$handlerName} performed an action",
                'time' => $activity->tracked_at->diffForHumans(),
                'color' => $statusColors[$activity->status] ?? 'secondary',
                'icon' => $statusIcons[$activity->status] ?? 'mdi-file-document',
            ];
        });
    }

    /**
     * Get status updates based on document statuses.
     */
    private function getStatusUpdates($user)
    {
        $operatingUnitId = $user->operating_unit_id;
        $query = Document::select('status', DB::raw('count(*) as count'))
            ->groupBy('status');

        // Get super admin role ID
        $superAdminRoleId = Role::where('slug', 'super_admin')->value('id');

        // Filter by operating unit for non-super admin users
        if ($user->role_id !== $superAdminRoleId) {
            $query->where(function ($subQuery) use ($operatingUnitId) {
                $subQuery->where('origin_unit_id', $operatingUnitId)
                    ->orWhereHas('receivedBy', function ($q) use ($operatingUnitId) {
                        $q->where('origin_unit_id', $operatingUnitId);
                    });
            });
        }

        $statuses = $query->get();

        $statusMappings = [
            'pending' => [
                'title' => 'Pending Documents',
                'description' => '{count} documents waiting for action',
                'status' => 'Pending',
                'statusColor' => 'warning',
                'icon' => 'mdi-clock-alert',
            ],
            'in progress' => [
                'title' => 'In Progress Documents',
                'description' => '{count} documents currently being processed',
                'status' => 'In Progress',
                'statusColor' => 'info',
                'icon' => 'mdi-progress-clock',
            ],
            'completed' => [
                'title' => 'Completed Documents',
                'description' => '{count} documents completed this month',
                'status' => 'Completed',
                'statusColor' => 'success',
                'icon' => 'mdi-check-circle',
            ],
            'rejected' => [
                'title' => 'Rejected Documents',
                'description' => '{count} documents were rejected',
                'status' => 'Rejected',
                'statusColor' => 'error',
                'icon' => 'mdi-close-circle',
            ],
        ];

        $updates = [];

        foreach ($statuses as $statusData) {
            $status = $statusData->status;
            $count = $statusData->count;

            if (isset($statusMappings[$status])) {
                $update = $statusMappings[$status];
                $update['description'] = str_replace('{count}', $count, $update['description']);
                $updates[] = $update;
            }
        }

        return $updates;
    }
}
