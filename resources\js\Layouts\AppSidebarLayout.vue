<template>
  <v-app id="inspire">
    <v-navigation-drawer
      v-model="drawer"
      color="sidebar"
      width="300"
      elevation="3"
      class="overflow-hidden rounded-r-2xl"
    >
      <v-list class="py-4">
        <v-list-item class="mb-4">
          <template v-slot:prepend>
            <div
              class="mr-2 flex items-center justify-center w-[50px] h-[50px]"
            >
              <v-img
                src="/dmmmsu.png"
                alt="Logo"
                class="object-contain max-w-full max-h-full bg-transparent"
                width="50"
                height="50"
                contain
              ></v-img>
            </div>
          </template>
          <div class="w-full overflow-hidden">
            <v-list-item-title
              class="text-white text-h6 font-weight-bold whitespace-normal leading-tight text-[1.25rem]"
              >RECORDS</v-list-item-title
            >
            <v-list-item-subtitle class="text-white">RMTS</v-list-item-subtitle>
          </div>
        </v-list-item>

        <!-- Operating Unit Summary -->
        <!-- <v-list-item class="px-3 py-2 mx-2 my-2 border-2 rounded-lg bg-amber-400 border-amber-500">
                        <template v-slot:prepend>
                            <div class="flex items-center justify-center w-10 h-10 bg-green-800 rounded-full">
                                <v-icon color="white" size="24">mdi-office-building</v-icon>
                            </div>
                        </template>
                        <div>
                            <v-list-item-title class="font-medium text-green-900 truncate">
                                {{
                                    $page.props.auth.user.operating_unit?.name ||
                                    "No Operating Unit"
                                }}
                            </v-list-item-title>
                            <v-list-item-subtitle class="text-sm font-bold text-green-900">
                                {{
                                    $page.props.auth.user.operating_unit?.code || "Operating Unit"
                                }}
                            </v-list-item-subtitle>
                        </div>
                    </v-list-item> -->
      </v-list>

      <!-- Main Navigation -->
      <v-list density="compact" nav class="py-1 mb-2">
        <v-list-item
          prepend-icon="mdi-view-dashboard"
          title="Dashboard"
          active-color="yellow"
          class="h-12 mx-2 my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1"
          @click="() => router.visit('/dashboard', { preserveState: true })"
        >
          <template v-slot:append>
            <v-chip
              size="x-small"
              color="primary"
              class="ml-2"
              v-if="isCurrentRoute('/dashboard')"
              >Active</v-chip
            >
          </template>
        </v-list-item>

        <v-list-item
          prepend-icon="mdi-email"
          title="Inbox"
          active-color="yellow"
          class="h-12 mx-2 my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1"
          @click="
            () => router.visit(route('inbox.index'), { preserveState: true })
          "
        >
          <!-- <template v-slot:append>
                            <v-badge content="3" color="error" offset-x="3" offset-y="3">
                                <v-icon icon="mdi-circle" size="small" color="transparent"></v-icon>
                            </v-badge>
                        </template> -->
        </v-list-item>

        <v-list-item
          prepend-icon="mdi-send"
          title="Forwarded Documents"
          active-color="yellow"
          class="h-12 mx-2 my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1"
          @click="
            () =>
              router.visit(route('forwarded-documents.index'), {
                preserveState: true,
              })
          "
        >
          <template v-slot:append>
            <v-chip
              size="x-small"
              color="primary"
              class="ml-2"
              v-if="isCurrentRoute('/forwarded-documents')"
              >Active</v-chip
            >
          </template>
        </v-list-item>
      </v-list>
      <!-- Document Change m2 -->
      <!-- Document Management Group -->
      <v-list density="compact" nav class="py-1">
        <v-list-group v-model="openGroups.documentManagement">
          <template v-slot:activator="{ props }">
            <v-list-item
              v-bind="props"
              prepend-icon="mdi-file-document-multiple"
              title="Document Management"
              active-color="yellow"
              class="my-1 transition-all duration-300 ease-in-out rounded-lg"
              @click="toggleGroup('documentManagement')"
            >
              <template v-slot:append>
                <v-icon
                  icon="mdi-chevron-down"
                  size="small"
                  class="transition-transform duration-300 group-icon"
                ></v-icon>
              </template>
            </v-list-item>
          </template>

          <v-list-item
            prepend-icon="mdi-folder-multiple"
            title="Records Management"
            subtitle="Document management system"
            active-color="yellow"
            class="my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1"
            @click="
              () => {
                ensureGroupOpen('documentManagement');
                router.visit('/documents', { preserveState: true });
              }
            "
          >
            <template v-slot:append>
              <v-chip
                size="x-small"
                color="info"
                class="ml-2"
                v-if="isCurrentRoute('/documents')"
                >Active</v-chip
              >
            </template>
          </v-list-item>

          <v-list-item
            prepend-icon="mdi-archive"
            title="Records Archive"
            subtitle="Excel archive management"
            active-color="yellow"
            class="my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1"
            @click="
              () => {
                ensureGroupOpen('documentManagement');
                router.visit('/records-archive', { preserveState: true });
              }
            "
          >
          </v-list-item>

          <v-list-item
            prepend-icon="mdi-map-marker"
            title="Record Tracking"
            subtitle="Track document movement"
            class="my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1"
            @click="
              () => {
                ensureGroupOpen('documentManagement');
                router.visit('/tracking', { preserveState: true });
              }
            "
            active-color="yellow"
          >
          </v-list-item>
        </v-list-group>
      </v-list>

      <!-- User Management Group -->
      <v-list density="compact" nav class="py-1">
        <v-list-group v-model="openGroups.userManagement">
          <template v-slot:activator="{ props }">
            <v-list-item
              v-bind="props"
              prepend-icon="mdi-account-group"
              title="User Management"
              active-color="yellow"
              class="my-1 transition-all duration-300 ease-in-out rounded-lg"
              @click="toggleGroup('userManagement')"
            >
              <template v-slot:append>
                <v-icon
                  icon="mdi-chevron-down"
                  size="small"
                  class="transition-transform duration-300 group-icon"
                ></v-icon>
              </template>
            </v-list-item>
          </template>

          <v-list-item
            prepend-icon="mdi-account-multiple"
            title="Users & Permissions"
            subtitle="Manage user access"
            active-color="yellow"
            class="my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1"
              @click="
                () => {
                  ensureGroupOpen('userManagement');
                  router.visit(route('users.index'), { preserveState: true });
                }
              "
          >
            <template v-slot:append>
              <v-chip
                size="x-small"
                color="info"
                class="ml-2"
                v-if="isCurrentRoute('/users')"
                >Active</v-chip
              >
            </template>
          </v-list-item>

          <!-- <v-list-item prepend-icon="mdi-bell" title="Notifications & Alerts"
                            subtitle="System notifications" @click="
                                () => {
                                    ensureGroupOpen('userManagement');
                                    goToNotifications();
                                }
                            " active-color="yellow"
                            class="my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1">
                            <template v-slot:append>
                                <v-badge content="5" color="error" offset-x="3" offset-y="3">
                                    <v-icon icon="mdi-circle" size="small" color="transparent"></v-icon>
                                </v-badge>
                            </template>
                        </v-list-item> -->
        </v-list-group>
      </v-list>

      <!-- Administration Group -->
      <v-list density="compact" nav class="py-1">
        <v-list-group v-model="openGroups.administration">
          <template v-slot:activator="{ props }">
            <v-list-item
              v-bind="props"
              prepend-icon="mdi-domain"
              title="Administration"
              active-color="yellow"
              class="my-1 transition-all duration-300 ease-in-out rounded-lg"
              @click="toggleGroup('administration')"
            >
              <template v-slot:append>
                <v-icon
                  icon="mdi-chevron-down"
                  size="small"
                  class="transition-transform duration-300 group-icon"
                ></v-icon>
              </template>
            </v-list-item>
          </template>

          <v-list-item
            prepend-icon="mdi-chart-bar"
            title="Reports & Analytics"
            subtitle="View system reports"
            @click="
              () => {
                ensureGroupOpen('administration');
                router.visit(route('reports.index'), {
                  preserveState: true,
                });
              }
            "
            active-color="yellow"
            class="my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1"
          >
            <!-- <template v-slot:append>
                                <v-chip size="x-small" color="warning" class="ml-2">Updated</v-chip>
                            </template> -->
          </v-list-item>

          <v-list-item
            prepend-icon="mdi-office-building"
            title="Operating Units"
            subtitle="Operating Units Management"
            active-color="yellow"
            class="my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1"
            @click="
              () => {
                ensureGroupOpen('administration');
                router.visit(route('operating-units.index'), {
                  preserveState: true,
                });
              }
            "
          >
            <template v-slot:append>
              <v-chip
                size="x-small"
                color="info"
                class="ml-2"
                v-if="isCurrentRoute('/operating-units')"
                >Active</v-chip
              >
            </template>
          </v-list-item>

          <v-list-item
            prepend-icon="mdi-office-building-outline"
            title="Department Units"
            subtitle="Department Units Management"
            active-color="yellow"
            class="my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1"
            @click="
              () => {
                ensureGroupOpen('administration');
                goToDepartmentUnits();
              }
            "
          >
            <template v-slot:append>
              <v-chip
                size="x-small"
                color="info"
                class="ml-2"
                v-if="isCurrentRoute('/department-units')"
                >Active</v-chip
              >
            </template>
          </v-list-item>

          <v-list-item
            prepend-icon="mdi-file-document-outline"
            title="Document Codes"
            subtitle="Manage Document Codes"
            active-color="yellow"
            class="my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1"
            @click="
              () => {
                ensureGroupOpen('administration');
                router.visit(route('document-codes.index'), {
                  preserveState: true,
                });
              }
            "
          >
            <template v-slot:append>
              <v-chip
                size="x-small"
                color="info"
                class="ml-2"
                v-if="isCurrentRoute('/document-codes')"
                >Active</v-chip
              >
            </template>
          </v-list-item>

          <!-- <v-list-item prepend-icon="mdi-cog" title="Integration & Settings"
                            subtitle="System configuration" @click="
                                () => {
                                    ensureGroupOpen('administration');
                                    goToSettings();
                                }
                            " active-color="yellow"
                            class="my-1 transition-all duration-300 ease-in-out rounded-lg hover:bg-white/10 hover:translate-x-1">
                        </v-list-item> -->
        </v-list-group>
      </v-list>

      <!-- Help & Support Section -->
      <!-- <v-list density="compact" nav class="navigation-list">
                    <v-list-item prepend-icon="mdi-help-circle" title="Help & Support" subtitle="Get assistance"
                        @click="goToHelp" color="yellow" class="nav-item help-item">
                    </v-list-item>
                </v-list> -->

      <!-- Version Info -->
      <div
        class="absolute left-0 right-0 p-2 text-xs text-center bottom-4 text-white/50"
      >
        <div class="font-medium mb-0.5">RMTS v1.0.2</div>
        <div class="text-[0.7rem] opacity-80">
          Last updated: {{ getCurrentDate() }}
        </div>
      </div>
    </v-navigation-drawer>

    <!-- App Bar based on Vuetify Snips -->
    <v-app-bar
      color="white"
      elevation="1"
      class="rounded-b-2xl w-[calc(100%-32px)] z-10 relative transition-all duration-300 ease-in-out"
      :class="{
        'w-[calc(100%-300px)] rounded-br-2xl rounded-bl-0 pr-[120px]': drawer,
      }"
      :style="{ left: drawer ? '300px' : '0' }"
    >
      <v-app-bar-nav-icon
        @click="drawer = !drawer"
        color="primary"
        class="mr-2"
      ></v-app-bar-nav-icon>

      <v-app-bar-title
        class="text-primary font-bold overflow-hidden text-ellipsis whitespace-nowrap max-w-[80%] transition-all duration-300 ease-in-out pl-0"
        :class="{ 'max-w-[40%]': drawer }"
      >
        <div class="flex items-center">
          <span
            class="overflow-hidden font-semibold text-ellipsis whitespace-nowrap"
          >
            {{ currentPageTitle }}
          </span>
        </div>
      </v-app-bar-title>

      <v-spacer></v-spacer>

      <div class="z-  10 flex items-center mr-10">
        <!-- Notifications Button -->
        <!-- <v-btn icon color="primary" class="mr-2" variant="text">
                        <v-badge dot color="error" offset-x="3" offset-y="3">
                            <v-icon icon="mdi-bell"></v-icon>
                        </v-badge>
                        <v-tooltip activator="parent" location="bottom">Notifications</v-tooltip>
                    </v-btn> -->

        <!-- User Menu -->
        <v-menu
          v-model="accountMenu"
          location="bottom"
          transition="slide-y-transition"
        >
          <template v-slot:activator="{ props }">
            <v-btn
              class="ml-2 rounded-full px-3 h-10 transition-transform duration-200 ease-in-out hover:-translate-y-0.5 bg-green-50 shadow-sm flex-shrink-0 z-10 relative whitespace-nowrap"
              v-bind="props"
              variant="tonal"
              color="primary"
            >
              <v-avatar size="32" class="mr-2">
                <v-icon icon="mdi-account" size="small"></v-icon>
              </v-avatar>
              <span
                class="hidden-sm-and-down max-w-[150px] overflow-hidden text-ellipsis whitespace-nowrap inline-block"
              >
                {{ $page.props.auth.user.operating_unit?.code || "OU" }}
              </span>
              <v-icon
                icon="mdi-chevron-down"
                size="small"
                class="ml-1 hidden-sm-and-down"
              ></v-icon>
            </v-btn>
          </template>

          <div class="bg-white rounded-lg w-[220px] p-3 shadow-md">
            <div class="flex items-center mb-3">
              <v-avatar color="primary" size="40">
                <v-icon color="white" size="24">mdi-account</v-icon>
              </v-avatar>
              <div class="ml-3 overflow-hidden">
                <div
                  class="font-medium text-[0.9rem] text-gray-900 whitespace-nowrap overflow-hidden text-ellipsis"
                >
                  {{ $page.props.auth.user.name }}
                </div>
                <div
                  class="text-[0.75rem] text-gray-600 whitespace-nowrap overflow-hidden text-ellipsis"
                >
                  {{
                    $page.props.auth.user.operating_unit?.name ||
                    "No Operating Unit"
                  }}
                </div>
              </div>
            </div>

            <v-divider class="my-2"></v-divider>

            <div class="mt-3">
              <Link :href="route('profile.edit')" class="text-decoration-none">
                <div
                  class="flex items-center p-2 mb-1 transition-colors rounded cursor-pointer hover:bg-green-50"
                >
                  <v-icon size="small" color="primary" class="mr-3"
                    >mdi-account-cog</v-icon
                  >
                  <span class="text-sm text-gray-900">Account Settings</span>
                </div>
              </Link>

              <v-divider class="my-2"></v-divider>

              <div
                class="flex items-center p-2 transition-colors rounded cursor-pointer hover:bg-green-50"
                @click="showLogoutConfirmation"
              >
                <v-icon size="small" color="error" class="mr-3"
                  >mdi-logout</v-icon
                >
                <span class="text-sm text-gray-900">Logout</span>
              </div>
            </div>
          </div>
        </v-menu>
      </div>
    </v-app-bar>

    <v-main class="bg-gray-100">
      <slot />
    </v-main>

    <!-- Logout Confirmation Dialog -->
    <v-dialog v-model="logoutDialog" max-width="400" persistent>
      <v-card>
        <v-card-title class="p-4 text-white text-h5 bg-error">
          Confirm Logout
        </v-card-title>
        <v-card-text class="pt-4 pb-2">
          <p class="text-gray-800">
            Are you sure you want to log out of the system?
          </p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="cancelLogout"
            class="transition-all duration-200 hover:opacity-80"
          >
            Cancel
          </v-btn>
          <v-btn
            color="error"
            variant="text"
            @click="confirmLogout"
            :loading="loggingOut"
            class="transition-all duration-200 hover:opacity-80"
          >
            Logout
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-app>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { Link, router, usePage } from '@inertiajs/vue3';

// Component state
const drawer = ref(true);
const accountMenu = ref(false);
const logoutDialog = ref(false);
const loggingOut = ref(false);

// Get the current page component name from Inertia
const page = usePage();

// Map route names to friendly page titles
const pageTitleMap = {
    'dashboard': 'Dashboard',
    'inbox.index': 'Inbox',
    'inbox.show': 'Message Details',
    'inbox.edit': 'Edit Message',
    'forwarded-documents.index': 'Forwarded Documents',
    'forwarded-documents.show-forwarded': 'Forwarded Document Details',
    'documents': 'Document Records',
    'documents.create': 'Register New Document',
    'documents.show': 'Document Details',
    'documents.edit': 'Edit Document',
    'tracking': 'Record Tracking',
    'users.index': 'Users',
    'users.create': 'Add New User',
    'users.show': 'User Details',
    'users.edit': 'Edit User',
    'operating-units.index': 'Operating Units',
    'operating-units.create': 'Add Operating Unit',
    'operating-units.show': 'Operating Unit Details',
    'operating-units.edit': 'Edit Operating Unit',
    'department-units.index': 'Department Units',
    'department-units.create': 'Add Department Unit',
    'department-units.show': 'Department Unit Details',
    'department-units.edit': 'Edit Department Unit',
    'document-codes.index': 'Document Codes',
    'document-codes.edit': 'Edit Document Code',
    'records-archive': 'Records Archive',
    'records-archive.folders.show': 'Folder Contents',
    'records-archive.show': 'Excel Archive Viewer',
    'profile.edit': 'Profile Settings',
};

// Compute the current page title based on the current route
const currentPageTitle = computed(() => {
    // Get the current route name
    const currentRoute = page.props.ziggy?.route;

    // Get the current component name for fallback
    const componentPath = page.component;
    const componentName = componentPath.split('/').pop().replace('.vue', '');

    // Try to get the title from the map using the route name
    if (currentRoute && pageTitleMap[currentRoute]) {
        return pageTitleMap[currentRoute];
    }

    // If the route isn't in our map, try to match by the URL path
    const urlParts = window.location.pathname.split('/').filter(part => part);
    const path = urlParts[0] || 'dashboard';

    // If we have a specific page type in the URL (create, edit, show)
    if (urlParts.length > 1) {
        const action = urlParts[urlParts.length - 1];
        const resourcePath = urlParts[0];

        // Check for common patterns
        if (action === 'create') {
            return `Add New ${resourcePath.replace(/-/g, ' ').replace(/^\w/, c => c.toUpperCase())}`;
        } else if (action === 'edit') {
            return `Edit ${resourcePath.replace(/-/g, ' ').replace(/^\w/, c => c.toUpperCase())}`;
        } else if (!isNaN(action) || action.match(/^[a-f0-9-]+$/i)) {
            // Likely an ID, so this is a show/details page
            return `${resourcePath.replace(/-/g, ' ').replace(/^\w/, c => c.toUpperCase())} Details`;
        }
    }

    // Check if we have a mapping for this path
    if (pageTitleMap[path]) {
        return pageTitleMap[path];
    }

    // Format the component name as a readable title
    // Convert camelCase or PascalCase to Title Case with spaces
    if (componentName === 'Index') {
        // For Index components, use the directory name instead
        const directory = componentPath.split('/').slice(-2, -1)[0];
        return directory.replace(/([A-Z])/g, ' $1').trim();
    } else {
        return componentName.replace(/([A-Z])/g, ' $1').trim();
    }
});

// Function to check if current route matches a given path
const isCurrentRoute = (path) => {
    return window.location.pathname.startsWith(path);
};

// Function to get current date in a formatted string
const getCurrentDate = () => {
    const date = new Date();
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

// Initialize sidebar state from localStorage or use defaults
const getSavedState = () => {
    if (typeof window !== 'undefined') {
        const saved = localStorage.getItem('sidebarState');
        return saved ? JSON.parse(saved) : {
            documentManagement: true,
            userManagement: true,
            administration: true
        };
    }
    return {
        documentManagement: true,
        userManagement: true,
        administration: true
    };
};

// Track open/closed state of sidebar groups
const openGroups = ref(getSavedState());

// Toggle a specific group
const toggleGroup = (groupName) => {
    openGroups.value[groupName] = !openGroups.value[groupName];
    saveState();
};

// Save sidebar state to localStorage
const saveState = () => {
    if (typeof window !== 'undefined') {
        localStorage.setItem('sidebarState', JSON.stringify(openGroups.value));
    }
};

// Ensure a group stays open
const ensureGroupOpen = (groupName) => {
    openGroups.value[groupName] = true;
    saveState();
};

// Watch for changes to openGroups and save to localStorage
watch(openGroups, () => {
    saveState();
}, { deep: true });

// Initialize sidebar state on component mount
onMounted(() => {
    // Restore saved state
    openGroups.value = getSavedState();
});

// Show logout confirmation dialog
const showLogoutConfirmation = () => {
    accountMenu.value = false; // Close the account menu
    logoutDialog.value = true; // Show the confirmation dialog
};

// Cancel logout
const cancelLogout = () => {
    logoutDialog.value = false;
};

// Confirm logout
const confirmLogout = () => {
    loggingOut.value = true;
    router.post(route('logout'), {}, {
        onSuccess: () => {
            logoutDialog.value = false;
            loggingOut.value = false;
        },
        onError: () => {
            loggingOut.value = false;
        }
    });
};

// Navigation functions for sections that don't have direct Inertia Links yet

// function goToRecordTracking() {
//     // For now, show an alert, but in the future this will use router.visit('/record-tracking')
//     alert('Record Tracking feature coming soon!');
// }

// Users & Permissions section now uses Link component

function goToNotifications() {
    // For now, show an alert, but in the future this will     use router.visit('/notifications')
    alert('Notifications & Alerts feature coming soon!');
}

function goToReports() {
    // For now, show an alert, but in the future this will use router.visit('/reports')
    alert('Reports & Analytics feature coming soon!');
}

function goToSettings() {
    // For now, show an alert, but in the future this will use router.visit('/settings')
    alert('Integration & Settings feature coming soon!');
}

function goToDepartmentUnits() {
    alert('Department Units feature coming soon!');
}

// Help & Support section is commented out in the template
// function goToHelp() {
//     // For now, show an alert, but in the future this will use router.visit('/help')
//     alert('Help & Support feature coming soon!');
// }
</script>

<style scoped>
/* Only keeping styles that can't be easily achieved with Tailwind */
:deep(.v-navigation-drawer__content) {
  background-image: linear-gradient(
    to bottom,
    rgba(46, 125, 50, 1),
    rgba(27, 94, 32, 1)
  );
}

/* App bar title styling */
:deep(.v-app-bar-title) {
  padding-left: 0 !important;
  margin-left: 0 !important;
}

:deep(.v-toolbar__content) {
  padding-left: 16px !important;
  gap: 8px !important;
}

/* Active item styling that requires deep selectors */
:deep(.v-list-item--active) {
  @apply bg-yellow-500/30 border-l-[6px] border-yellow-400 text-white font-bold relative;
}

:deep(.v-list-item--active)::after {
  content: "";
  @apply absolute top-0 right-0 h-full w-1 bg-yellow-400;
}

/* Sidebar list item base styles */
:deep(.v-list-item) {
  @apply border-l-[6px] border-transparent text-white;
}

/* Sidebar list item subtitle color */
:deep(.v-list-item__subtitle) {
  @apply text-white/70;
}

/* Group icon rotation on open - using content because it's cleaner than with Tailwind */
:deep(.v-list-group--open .group-icon) {
  transform: rotate(180deg);
}

/* Add subtle indentation to submenu items */
:deep(.v-list-group__items .v-list-item) {
  padding-left: 6px !important;
  margin-left: 0 !important;
}

/* Target Vuetify's internal indentation structure */
:deep(.v-list-group__items) {
  padding-left: 0 !important;
}

:deep(.v-list-group--child) {
  padding-left: 0 !important;
}

:deep(.v-list-item__content) {
  padding-left: 0 !important;
}
</style>
