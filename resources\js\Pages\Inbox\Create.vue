<template>

    <Head title="Compose Message" />

    <AppSidebarLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">Compose New Message</h2>
        </template>

        <v-container>
            <v-row>
                <v-col cols="12" class="justify-end d-flex">
                    <Link href="/inbox" class="text-decoration-none">
                    <v-btn color="primary" variant="outlined" prepend-icon="mdi-arrow-left">
                        Back to Inbox
                    </v-btn>
                    </Link>
                </v-col>
            </v-row>

            <v-row>
                <v-col cols="12">
                    <v-card elevation="2">
                        <v-card-title class="text-white bg-primary">
                            Compose New Message
                        </v-card-title>

                        <v-card-text>
                            <form @submit.prevent="submit">
                                <v-row class="mt-4">
                                    <v-col cols="12">
                                        <v-select v-model="form.recipient_id" :items="users" item-title="name"
                                            item-value="id" label="Recipient" required
                                            :error-messages="form.errors.recipient_id" variant="outlined"
                                            bg-color="white">
                                            <template v-slot:item="{ item, props }">
                                                <v-list-item v-bind="props">
                                                    <template v-slot:prepend>
                                                        <v-avatar color="primary" size="32">
                                                            <v-icon color="white" icon="mdi-account"></v-icon>
                                                        </v-avatar>
                                                    </template>
                                                    <v-list-item-title>{{ item.raw.name }}</v-list-item-title>
                                                    <v-list-item-subtitle>{{ item.raw.email }}</v-list-item-subtitle>
                                                </v-list-item>
                                            </template>
                                        </v-select>
                                    </v-col>

                                    <v-col cols="12">
                                        <v-text-field v-model="form.subject" label="Subject" required
                                            :error-messages="form.errors.subject" variant="outlined"
                                            bg-color="white"></v-text-field>
                                    </v-col>

                                    <v-col cols="12">
                                        <v-select v-model="form.priority" :items="priorities" label="Priority" required
                                            :error-messages="form.errors.priority" variant="outlined" bg-color="white">
                                            <template v-slot:item="{ item, props }">
                                                <v-list-item v-bind="props">
                                                    <template v-slot:prepend>
                                                        <v-icon :color="getPriorityColor(item.raw.value)">
                                                            {{ getPriorityIcon(item.raw.value) }}
                                                        </v-icon>
                                                    </template>
                                                    <v-list-item-title>{{ item.raw.title }}</v-list-item-title>
                                                </v-list-item>
                                            </template>
                                        </v-select>
                                    </v-col>

                                    <v-col cols="12">
                                        <v-select v-model="form.document_id" :items="documents"
                                            item-title="reference_number" item-value="id"
                                            label="Attach Document (Optional)" :error-messages="form.errors.document_id"
                                            variant="outlined" bg-color="white" clearable
                                            @update:model-value="updateReference">
                                            <template v-slot:item="{ item, props }">
                                                <v-list-item v-bind="props">
                                                    <template v-slot:prepend>
                                                        <v-icon color="primary"
                                                            icon="mdi-file-document-outline"></v-icon>
                                                    </template>
                                                    <v-list-item-title>{{ item.raw.reference_number
                                                        }}</v-list-item-title>
                                                    <v-list-item-subtitle>{{ item.raw.subject }}</v-list-item-subtitle>
                                                </v-list-item>
                                            </template>
                                        </v-select>
                                    </v-col>

                                    <v-col cols="12">
                                        <v-select v-model="form.reference" :items="referenceOptions" item-title="title"
                                            item-value="value" label="Reference Category"
                                            :error-messages="form.errors.reference" variant="outlined" bg-color="white">
                                            <template v-slot:item="{ item, props }">
                                                <v-list-item v-bind="props">
                                                    <template v-slot:prepend>
                                                        <v-icon color="primary"
                                                            icon="mdi-file-document-outline"></v-icon>
                                                    </template>
                                                    <v-list-item-title>{{ item.raw.title }}</v-list-item-title>
                                                </v-list-item>
                                            </template>
                                        </v-select>
                                    </v-col>

                                    <v-col cols="12">
                                        <v-textarea v-model="form.message" label="Message" required
                                            :error-messages="form.errors.message" variant="outlined" bg-color="white"
                                            rows="8"></v-textarea>
                                    </v-col>

                                    <v-col cols="12" class="justify-end d-flex">
                                        <v-btn type="submit" color="primary" size="large" :loading="form.processing"
                                            prepend-icon="mdi-send">
                                            Send Message
                                        </v-btn>
                                    </v-col>
                                </v-row>
                            </form>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </AppSidebarLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
    users: Array,
    documents: Array,
    errors: Object,
});

const priorities = [
    { title: 'High Priority', value: 'high' },
    { title: 'Medium Priority', value: 'medium' },
    { title: 'Low Priority', value: 'low' },
];

const referenceOptions = [
    { title: 'F01 - Administrative Documents', value: 'F01' },
    { title: 'F02 - Financial Documents', value: 'F02' },
    { title: 'F03 - Legal Documents', value: 'F03' },
    { title: 'F04 - Miscellaneous', value: 'F04' },
];

const form = useForm({
    recipient_id: '',
    subject: '',
    message: '',
    document_id: null,
    reference: 'F04',
    priority: 'medium',
});



const getPriorityColor = (priority) => {
    switch (priority) {
        case 'high':
            return 'error';
        case 'medium':
            return 'warning';
        case 'low':
            return 'success';
        default:
            return 'grey';
    }
};

const getPriorityIcon = (priority) => {
    switch (priority) {
        case 'high':
            return 'mdi-alert-circle';
        case 'medium':
            return 'mdi-alert';
        case 'low':
            return 'mdi-information';
        default:
            return 'mdi-help-circle';
    }
};

const submit = () => {
    form.post('/inbox');
};
</script>

<style scoped>
:deep(.v-field__input) {
    color: rgba(0, 0, 0, 0.87) !important;
}
</style>
