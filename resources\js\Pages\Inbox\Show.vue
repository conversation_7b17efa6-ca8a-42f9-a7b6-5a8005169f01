<template>
  <Head :title="`Message: ${inbox.subject}`" />

  <AppSidebarLayout>
    <template #header>
      <h2 class="text-xl font-semibold leading-tight text-gray-800">
        View Message
      </h2>
    </template>

    <v-container>
      <v-row>
        <v-col cols="12" class="justify-end d-flex">
          <Link href="/inbox" class="text-decoration-none">
            <v-btn
              color="primary"
              variant="outlined"
              prepend-icon="mdi-arrow-left"
            >
              Back to Inbox
            </v-btn>
          </Link>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12">
          <v-card elevation="2">
            <v-card-title
              class="text-white bg-primary d-flex justify-space-between align-center"
            >
              <div v-if="!editing">
                <span>{{ inbox.subject }}</span>
              </div>
              <div v-else class="mr-2 w-100">
                <v-text-field
                  v-model="form.subject"
                  label="Subject"
                  variant="outlined"
                  density="compact"
                  bg-color="white"
                  hide-details
                  class="mt-n2"
                  :error-messages="errors.subject"
                ></v-text-field>
              </div>
              <div class="d-flex align-center">
                <v-chip
                  v-if="!editing"
                  :color="getPriorityColor(inbox.priority)"
                  size="small"
                  class="mr-2 text-white"
                >
                  {{ inbox.priority }}
                </v-chip>
                <v-select
                  v-else
                  v-model="form.priority"
                  :items="priorityOptions"
                  label="Priority"
                  variant="outlined"
                  density="compact"
                  bg-color="white"
                  hide-details
                  class="max-w-xs mr-2"
                  :error-messages="errors.priority"
                ></v-select>
                <v-btn
                  v-if="
                    inbox.sender_id === $page.props.auth.user.id && !editing
                  "
                  icon="mdi-pencil"
                  size="small"
                  color="white"
                  @click="startEditing"
                  title="Edit Message"
                ></v-btn>
              </div>
            </v-card-title>

            <v-card-text class="pa-4">
              <v-row>
                <v-col cols="12" md="6">
                  <div class="message-info">
                    <div class="info-item">
                      <div class="info-label">From:</div>
                      <div class="info-value">
                        <v-avatar color="primary" size="24" class="mr-2">
                          <v-icon color="white" size="small"
                            >mdi-account</v-icon
                          >
                        </v-avatar>
                        {{ inbox.sender.name }} ({{ inbox.sender.email }})
                      </div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">To:</div>
                      <div class="info-value">
                        <v-avatar color="primary" size="24" class="mr-2">
                          <v-icon color="white" size="small"
                            >mdi-account</v-icon
                          >
                        </v-avatar>
                        {{ inbox.recipient.name }} ({{ inbox.recipient.email }})
                      </div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Date:</div>
                      <div class="info-value">
                        <v-icon color="grey" size="small" class="mr-2"
                          >mdi-calendar</v-icon
                        >
                        {{ formatDate(inbox.created_at) }}
                      </div>
                    </div>

                    <div class="info-item" v-if="inbox.document">
                      <div class="info-label">Attached Document:</div>
                      <div class="info-value">
                        <v-icon color="primary" size="small" class="mr-2"
                          >mdi-file-document</v-icon
                        >
                        <Link
                          :href="`/documents/${inbox.document.id}`"
                          class="document-link"
                        >
                          {{ inbox.document.reference_number }} -
                          {{ inbox.document.subject }}
                        </Link>
                      </div>
                    </div>
                  </div>
                </v-col>
              </v-row>

              <v-divider class="my-4"></v-divider>

              <!-- Document Preview Section -->
              <div
                v-if="inbox.document && inbox.document.file_path"
                class="mb-4 document-preview-section"
              >
                <h3 class="mb-2 text-subtitle-1 font-weight-bold">
                  Document Preview
                </h3>
                <div class="document-preview-container">
                  <img
                    v-if="isImageFile(inbox.document.file_path)"
                    :src="getFileUrl(inbox.document.file_path)"
                    alt="Document Preview"
                    class="document-preview-image"
                  />
                  <div v-else class="pdf-preview">
                    <v-icon size="large" color="primary"
                      >mdi-file-document</v-icon
                    >
                    <span>Document</span>
                    <v-btn
                      color="primary"
                      size="small"
                      variant="text"
                      :href="getFileUrl(inbox.document.file_path)"
                      target="_blank"
                    >
                      View Document
                    </v-btn>
                  </div>
                </div>
              </div>

              <!-- Message Content Section (less prominent) -->
              <div
                class="message-content"
                :class="{ 'mt-4': inbox.document && inbox.document.file_path }"
              >
                <h3
                  v-if="inbox.document && inbox.document.file_path"
                  class="mb-2 text-subtitle-1 font-weight-bold"
                >
                  Message
                </h3>
                <p v-if="!editing" class="text-body-1 message-text">
                  {{ inbox.message }}
                </p>
                <v-textarea
                  v-else
                  v-model="form.message"
                  label="Message"
                  variant="outlined"
                  rows="8"
                  :error-messages="errors.message"
                  class="mt-2"
                ></v-textarea>
              </div>
            </v-card-text>

            <v-card-actions class="pt-0 pa-4">
              <v-spacer></v-spacer>

              <!-- Non-editing mode buttons -->
              <template v-if="!editing">
                <v-btn
                  v-if="
                    inbox.document &&
                    inbox.recipient_id === $page.props.auth.user.id
                  "
                  color="success"
                  variant="text"
                  prepend-icon="mdi-check-circle"
                  @click="acknowledgeDocument"
                  :loading="acknowledging"
                >
                  Acknowledge
                </v-btn>
              </template>

              <!-- Editing mode buttons -->
              <template v-else>
                <v-btn
                  color="grey-darken-1"
                  variant="text"
                  @click="cancelEditing"
                >
                  Cancel
                </v-btn>
                <v-btn
                  color="success"
                  variant="text"
                  prepend-icon="mdi-content-save"
                  @click="saveChanges"
                  :loading="saving"
                >
                  Save Changes
                </v-btn>
              </template>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="deleteDialog" max-width="500">
      <v-card>
        <v-card-title class="text-white text-h5 bg-error"
          >Confirm Delete</v-card-title
        >
        <v-card-text class="pt-4">
          <p>Are you sure you want to delete this message?</p>
          <p class="mt-2 font-weight-medium">Subject: {{ inbox.subject }}</p>
          <p class="mt-4 text-error font-weight-bold">
            This action cannot be undone.
          </p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="deleteDialog = false"
          >
            Cancel
          </v-btn>
          <v-btn
            color="error"
            variant="text"
            @click="deleteMessage"
            :loading="deleting"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Acknowledgment Success Dialog -->
    <v-dialog v-model="acknowledgmentSuccessDialog" max-width="500">
      <v-card>
        <v-card-title class="text-white text-h5 bg-success"
          >Document Acknowledged</v-card-title
        >
        <v-card-text class="pt-4">
          <div class="mb-4 d-flex align-center">
            <v-icon color="success" size="large" class="mr-2"
              >mdi-check-circle</v-icon
            >
            <span class="text-h6">Document successfully acknowledged!</span>
          </div>
          <p class="mt-2">
            The document has been added to your Records Management table with
            status "Pending" and categorized as an F01 (incoming) document.
          </p>
          <p class="mt-2">
            <v-icon color="info" size="small" class="mr-1"
              >mdi-information-outline</v-icon
            >
            The original document has been downloaded to your computer.
          </p>
          <p class="mt-2">
            <v-icon color="warning" size="small" class="mr-1"
              >mdi-alert-circle-outline</v-icon
            >
            You need to generate a new QR code and upload your own document
            version.
          </p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" variant="text" @click="closeSuccessDialog">
            View Documents
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import { router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';
import axios from 'axios';

const props = defineProps({
    inbox: Object,
    errors: Object,
});

const deleteDialog = ref(false);
const deleting = ref(false);
const editing = ref(false);
const saving = ref(false);
const acknowledging = ref(false);
const acknowledgmentSuccessDialog = ref(false);

// Priority options for the dropdown
const priorityOptions = [
    { title: 'High', value: 'high' },
    { title: 'Medium', value: 'medium' },
    { title: 'Low', value: 'low' },
];

// Initialize the form with the inbox data
const form = useForm({
    subject: props.inbox.subject,
    message: props.inbox.message,
    recipient_id: props.inbox.recipient_id,
    document_id: props.inbox.document_id,
    priority: props.inbox.priority,
});

// Start editing mode
const startEditing = () => {
    editing.value = true;
};

// Cancel editing and reset form
const cancelEditing = () => {
    form.reset();
    form.clearErrors();
    editing.value = false;
};

// Save changes
const saveChanges = () => {
    saving.value = true;
    form.put(route('inbox.update', props.inbox.id), {
        preserveScroll: true,
        onSuccess: () => {
            editing.value = false;
            saving.value = false;
        },
        onError: () => {
            saving.value = false;
        }
    });
};

const getPriorityColor = (priority) => {
    switch (priority) {
        case 'high':
            return 'error';
        case 'medium':
            return 'warning';
        case 'low':
            return 'success';
        default:
            return 'grey';
    }
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

// Helper function to check if a file is an image
const isImageFile = (filePath) => {
    if (!filePath) return false;
    const extension = filePath.split('.').pop().toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension);
};

// Helper function to get the file URL
const getFileUrl = (filePath) => {
    if (!filePath) return '';
    return `/storage/${filePath}`;
};

const markAsRead = () => {
    router.post(route('inbox.mark-as-read', props.inbox.id), {}, {
        preserveScroll: true,
        onSuccess: () => {
            // Success notification could be added here
        }
    });
};

const markAsUnread = () => {
    router.post(route('inbox.mark-as-unread', props.inbox.id), {}, {
        preserveScroll: true,
        onSuccess: () => {
            // Success notification could be added here
        }
    });
};

const confirmDelete = () => {
    deleteDialog.value = true;
};

const deleteMessage = () => {
    deleting.value = true;
    router.delete(`/inbox/${props.inbox.id}`, {
        onSuccess: () => {
            router.visit('/inbox');
        },
        onError: () => {
            deleting.value = false;
        }
    });
};

const replyToMessage = () => {
    router.visit('/inbox/create', {
        data: {
            recipient_id: props.inbox.sender_id,
            subject: `Re: ${props.inbox.subject}`,
            document_id: props.inbox.document_id,
        }
    });
};

/**
 * Handle document acknowledgment
 * This function will:
 * 1. Mark the message as read
 * 2. Update the original document status to "completed"
 * 3. Create a new document record with status "pending" for the recipient
 * 4. Automatically download the document attachment
 * 5. Show success dialog
 */
const acknowledgeDocument = () => {
    acknowledging.value = true;

    // Use axios for direct access to response data
    axios.post(route('inbox.acknowledge', props.inbox.id))
        .then(response => {
            acknowledging.value = false;

            // Check if we have a file path to download
            if (response.data.success && response.data.file_path) {
                // Create a temporary anchor element to trigger download
                const downloadLink = document.createElement('a');
                downloadLink.href = response.data.file_path;

                // Extract filename from path
                const fileName = response.data.file_path.split('/').pop();
                downloadLink.download = fileName || 'document';

                // Append to body, click, and remove
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            }

            // Show success dialog
            acknowledgmentSuccessDialog.value = true;
        })
        .catch(error => {
            acknowledging.value = false;
            console.error('Error acknowledging document:', error);
            alert('Error acknowledging document: ' + (error.response?.data?.message || 'Unknown error'));
        });
};

/**
 * Close the success dialog and redirect to documents page
 */
const closeSuccessDialog = () => {
    acknowledgmentSuccessDialog.value = false;
    router.visit('/documents');
};
</script>

<style scoped>
.message-info {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-label {
  width: 120px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.6);
}

.info-value {
  flex: 1;
  display: flex;
  align-items: center;
}

.message-content {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  min-height: 200px;
}

.message-text {
  white-space: pre-line;
  line-height: 1.6;
}

.document-link {
  color: #2e7d32;
  text-decoration: none;
}

.document-link:hover {
  text-decoration: underline;
}

/* Document preview styling */
.document-preview-section {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
}

.document-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  min-height: 300px;
  overflow: hidden;
}

.document-preview-image {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
}

.pdf-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
}
</style>
