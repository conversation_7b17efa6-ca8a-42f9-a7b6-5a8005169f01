<template>
    <v-container>
        <div class="justify-center d-flex align-center">
            <v-btn color="blue-darken-1" variant="flat" class="text-center cursor-pointer" @dragover.prevent
                @drop="handleDrop" @click="triggerFileSelect"
                :prepend-icon="hasExistingDocument ? 'mdi-file-replace' : 'mdi-file-plus'">
                {{ hasExistingDocument ? 'Change Document' : 'Upload Document' }}
            </v-btn>

            <input ref="fileInput" type="file" hidden multiple @change="handleFileSelect" />
        </div>
    </v-container>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
    hasExistingDocument: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(["upload", "autoSubmit"]);
const fileInput = ref(null);

const triggerFileSelect = () => {
    fileInput.value?.click();
};

const handleFileSelect = (event) => {
    const files = event.target.files;
    if (files && files.length > 0) {
        handleFiles(files);
        // Auto-submit the file immediately after selection
        emit("autoSubmit", Array.from(files));
    }
    event.target.value = null; // Reset the input value to allow re-uploading the same file
    // This is necessary because the input does not trigger change event if the same file is selected again
};

const handleDrop = (event) => {
    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
        handleFiles(files);
        // Auto-submit the file immediately after drop
        emit("autoSubmit", Array.from(files));
    }
    fileInput.value.value = null; // Reset the input value to allow re-uploading the same file
    // This is necessary because the input does not trigger change event if the same file is selected again
};

const handleFiles = (files) => {
    emit("upload", Array.from(files));
};
</script>
