<template>

    <Head :title="'Edit Operating Unit - ' + operatingUnit.name" />
    <AppSidebarLayout>
        <v-container>
            <v-row>
                <v-col cols="12" class="d-flex justify-space-between align-center">
                    <h1 class="text-h4">Edit Operating Unit</h1>
                    <Link :href="route('operating-units.index')" class="text-decoration-none">
                    <v-btn color="primary" variant="outlined" prepend-icon="mdi-arrow-left">
                        Back to List
                    </v-btn>
                    </Link>
                </v-col>
            </v-row>

            <v-row>
                <v-col cols="12">
                    <v-card elevation="2">
                        <v-card-title class="mb-5 text-white bg-primary">Operating Unit Details</v-card-title>
                        <v-card-text>
                            <v-form @submit.prevent="submit">
                                <v-row>
                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.name" label="Name" :error-messages="errors.name"
                                            required variant="outlined" bg-color="white" color="primary"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.code" label="Code" :error-messages="errors.code"
                                            required hint="Unique identifier for the operating unit" persistent-hint
                                            variant="outlined" bg-color="white" color="primary"></v-text-field>
                                    </v-col>



                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.email" label="Email" :error-messages="errors.email"
                                            type="email" variant="outlined" bg-color="white"
                                            color="primary"></v-text-field>
                                    </v-col>



                                    <v-col cols="12" class="justify-end d-flex">
                                        <v-btn type="submit" color="primary" size="large" :loading="form.processing">
                                            Update Operating Unit
                                        </v-btn>
                                    </v-col>
                                </v-row>
                            </v-form>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </AppSidebarLayout>
</template>

<script setup>
import { Head, useForm, Link } from '@inertiajs/vue3';
const route = window.route;

import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
    operatingUnit: Object,
    errors: Object,
});

const form = useForm({
    name: props.operatingUnit.name,
    code: props.operatingUnit.code,
    email: props.operatingUnit.email,
});



const submit = () => {
    form.put(route('operating-units.update', props.operatingUnit.id), {
        onSuccess: () => {
            // Redirect handled by controller
        },
    });
};
</script>
