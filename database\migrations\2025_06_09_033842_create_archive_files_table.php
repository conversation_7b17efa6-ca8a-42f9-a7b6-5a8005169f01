<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('archive_files', function (Blueprint $table) {
            $table->id();
            $table->string('original_name');
            $table->string('stored_name');
            $table->string('file_path');
            $table->string('mime_type');
            $table->unsignedBigInteger('size');
            $table->string('extension', 10);
            $table->unsignedBigInteger('folder_id');
            $table->unsignedBigInteger('operating_unit_id');
            $table->unsignedBigInteger('uploaded_by');
            $table->json('metadata')->nullable(); // For storing additional file metadata
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('folder_id')->references('id')->on('archive_folders')->onDelete('cascade');
            $table->foreign('operating_unit_id')->references('id')->on('operating_units')->onDelete('cascade');
            $table->foreign('uploaded_by')->references('id')->on('users')->onDelete('cascade');

            // Indexes
            $table->index(['folder_id', 'original_name']);
            $table->index('operating_unit_id');
            $table->index('uploaded_by');
            $table->index('extension');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('archive_files');
    }
};
