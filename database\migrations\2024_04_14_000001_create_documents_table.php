<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->string('signatory');
            $table->string('origin');
            $table->text('subject_matter');
            $table->string('referred_to');
            $table->string('document_code');
            $table->enum('reference_document', ['F01', 'F02', 'F03', 'F04']);
            $table->string('control_number')->unique();
            $table->foreignId('received_by_id')->constrained('users');
            $table->timestamp('date_received');
            $table->string('file_path')->nullable();
            $table->string('qr_code_path')->nullable();
            $table->string('status')->default('pending');
            $table->text('remarks')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
