const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"ignition.healthCheck":{"uri":"_ignition\/health-check","methods":["GET","HEAD"]},"ignition.executeSolution":{"uri":"_ignition\/execute-solution","methods":["POST"]},"ignition.updateConfig":{"uri":"_ignition\/update-config","methods":["POST"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"profile.edit":{"uri":"profile","methods":["GET","HEAD"]},"profile.update":{"uri":"profile","methods":["PATCH"]},"profile.destroy":{"uri":"profile","methods":["DELETE"]},"documents.index":{"uri":"documents","methods":["GET","HEAD"]},"documents.create":{"uri":"documents\/create","methods":["GET","HEAD"]},"documents.store":{"uri":"documents","methods":["POST"]},"documents.show":{"uri":"documents\/{document}","methods":["GET","HEAD"],"parameters":["document"],"bindings":{"document":"id"}},"documents.edit":{"uri":"documents\/{document}\/edit","methods":["GET","HEAD"],"parameters":["document"],"bindings":{"document":"id"}},"documents.update":{"uri":"documents\/{document}","methods":["PUT","PATCH"],"parameters":["document"],"bindings":{"document":"id"}},"documents.destroy":{"uri":"documents\/{document}","methods":["DELETE"],"parameters":["document"],"bindings":{"document":"id"}},"documents.generate-qr":{"uri":"documents\/{document}\/generate-qr","methods":["GET","HEAD"],"parameters":["document"],"bindings":{"document":"id"}},"documents.generate-qr.store":{"uri":"documents\/{document}\/generate-qr","methods":["POST"],"parameters":["document"],"bindings":{"document":"id"}},"documents.upload":{"uri":"documents\/{document}\/upload","methods":["GET","HEAD"],"parameters":["document"],"bindings":{"document":"id"}},"documents.upload.store":{"uri":"documents\/{document}\/upload","methods":["POST"],"parameters":["document"],"bindings":{"document":"id"}},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"password.update":{"uri":"password","methods":["PUT"]},"logout":{"uri":"logout","methods":["POST"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
