<template>

    <Head :title="`${folder.name} - Records Archive`" />
    <AppSidebarLayout>
        <template #header>
            <div class="d-flex align-center justify-space-between">
                <div>
                    <h1 class="text-white text-h4 font-weight-bold">{{ folder.name }}</h1>
                    <p class="text-white-darken-1 ma-0">{{ filteredFiles.length }} files • Created {{
                        formatDate(folder.created_at) }}</p>
                </div>
                <div class="gap-3 d-flex align-center">
                    <!-- Back Button -->
                    <v-btn color="white" variant="outlined" prepend-icon="mdi-arrow-left" @click="goBack">
                        Back to Archive
                    </v-btn>
                    <!-- Upload Button -->
                    <v-btn color="white" variant="flat" prepend-icon="mdi-upload" @click="triggerFileUpload"
                        :loading="uploading" :disabled="uploading">
                        Upload Files
                    </v-btn>
                </div>
            </div>
        </template>

        <v-container fluid class="pa-6">
            <!-- Hidden File Input -->
            <input ref="fileInput" type="file" multiple accept=".xlsx,.xls,.csv" @change="handleFileSelect"
                style="display: none;" />

            <!-- Breadcrumb Navigation -->
            <v-breadcrumbs class="mb-4 pa-0">
                <v-breadcrumbs-item>
                    <v-btn variant="text" prepend-icon="mdi-arrow-left" @click="goBack" color="primary">
                        Records Archive
                    </v-btn>
                </v-breadcrumbs-item>
                <v-breadcrumbs-divider>/</v-breadcrumbs-divider>
                <v-breadcrumbs-item>{{ folder.name }}</v-breadcrumbs-item>
            </v-breadcrumbs>

            <!-- Main Content Area -->
            <v-row>
                <v-col cols="12">

                    <!-- Files Section -->
                    <v-card elevation="0" class="border-thin" v-if="filteredFiles.length > 0">
                        <!-- Header with Actions -->
                        <v-card-title class="pa-6 bg-grey-lighten-5 border-b-thin">
                            <div class="d-flex align-center justify-space-between w-100">
                                <div class="d-flex align-center">
                                    <v-icon icon="mdi-file-multiple" class="mr-3" color="primary"></v-icon>
                                    <div>
                                        <h3 class="text-h6 font-weight-medium">Files</h3>
                                        <p class="text-caption text-grey-darken-1 ma-0">
                                            <span v-if="searchQuery">{{ filteredFiles.length }} files found</span>
                                            <span v-else>{{ files.length }} files in this folder</span>
                                        </p>
                                    </div>
                                </div>

                                <!-- Action Bar -->
                                <div class="gap-3 d-flex align-center">
                                    <!-- Bulk Actions -->
                                    <div v-if="selectedFiles.length > 0" class="gap-2 d-flex align-center">
                                        <v-chip color="primary" variant="flat" size="small">
                                            <v-icon start icon="mdi-check-circle"></v-icon>
                                            {{ selectedFiles.length }} selected
                                        </v-chip>
                                        <v-btn color="primary" variant="outlined" size="small"
                                            prepend-icon="mdi-folder-move" @click="bulkMoveFiles">
                                            Move
                                        </v-btn>
                                        <v-btn color="error" variant="outlined" size="small" prepend-icon="mdi-delete"
                                            @click="bulkDeleteFiles">
                                            Delete
                                        </v-btn>
                                        <v-btn color="grey" variant="text" size="small" @click="clearSelection">
                                            Clear
                                        </v-btn>
                                    </div>

                                    <!-- Search Input -->
                                    <v-text-field v-model="searchQuery" placeholder="Search files..." variant="outlined"
                                        density="compact" hide-details prepend-inner-icon="mdi-magnify" clearable
                                        style="min-width: 250px;" class="search-field"></v-text-field>

                                    <!-- Sort Options -->
                                    <v-select v-model="sortBy" :items="sortOptions" item-title="text" item-value="value"
                                        variant="outlined" density="compact" hide-details prepend-inner-icon="mdi-sort"
                                        style="min-width: 150px;"></v-select>

                                    <!-- Upload Button -->
                                    <v-btn v-if="selectedFiles.length === 0" color="primary" variant="flat"
                                        prepend-icon="mdi-upload" @click="triggerFileUpload" :loading="uploading"
                                        :disabled="uploading">
                                        Upload Files
                                    </v-btn>
                                </div>
                            </div>
                        </v-card-title>

                        <v-card-text class="pa-6">
                            <!-- Select All Checkbox -->
                            <div v-if="filteredFiles.length > 0" class="mb-4">
                                <v-checkbox v-model="selectAll" @change="toggleSelectAll"
                                    :label="`Select All ${searchQuery ? 'Filtered' : ''} Files`" density="compact"
                                    hide-details></v-checkbox>
                            </div>

                            <!-- Grid View -->
                            <v-row>
                                <v-col cols="12" sm="6" md="4" lg="3" v-for="file in sortedFiles" :key="file.id">
                                    <v-card elevation="2" class="file-card position-relative hover-card"
                                        @click="viewFile(file)"
                                        :class="{ 'selected-card': selectedFiles.includes(file.id) }">
                                        <!-- Selection Checkbox -->
                                        <v-checkbox v-model="selectedFiles" :value="file.id" class="file-checkbox"
                                            density="compact" hide-details color="primary" @click.stop></v-checkbox>

                                        <v-card-text class="text-center pa-6">
                                            <div class="position-relative">
                                                <!-- File Icon with Animation -->
                                                <div class="mb-4 file-icon-container">
                                                    <v-icon size="72" :color="getFileIconColor(file.original_name)"
                                                        class="file-icon">
                                                        {{ getFileIcon(file.original_name) }}
                                                    </v-icon>
                                                </div>

                                                <!-- Context Menu -->
                                                <v-menu location="bottom end">
                                                    <template v-slot:activator="{ props }">
                                                        <v-btn v-bind="props" icon="mdi-dots-vertical" size="small"
                                                            variant="text" class="file-menu-btn" @click.stop></v-btn>
                                                    </template>
                                                    <v-list density="compact">
                                                        <v-list-item @click="moveFile(file)">
                                                            <v-list-item-title>
                                                                <v-icon icon="mdi-folder-move" size="small"
                                                                    class="mr-2"></v-icon>
                                                                Move
                                                            </v-list-item-title>
                                                        </v-list-item>
                                                        <v-list-item @click="renameFile(file)">
                                                            <v-list-item-title>
                                                                <v-icon icon="mdi-pencil" size="small"
                                                                    class="mr-2"></v-icon>
                                                                Rename
                                                            </v-list-item-title>
                                                        </v-list-item>
                                                        <v-list-item @click="deleteFile(file)" class="text-error">
                                                            <v-list-item-title>
                                                                <v-icon icon="mdi-delete" size="small"
                                                                    class="mr-2"></v-icon>
                                                                Delete
                                                            </v-list-item-title>
                                                        </v-list-item>
                                                    </v-list>
                                                </v-menu>
                                            </div>

                                            <!-- File Info -->
                                            <div class="file-info">
                                                <div v-if="editingFile === file.id" class="inline-edit">
                                                    <v-text-field v-model="editingName" variant="outlined"
                                                        density="compact" hide-details
                                                        @keyup.enter="confirmFileRename(file)"
                                                        @keyup.escape="cancelFileRename" @blur="confirmFileRename(file)"
                                                        autofocus class="inline-edit-field" />
                                                </div>
                                                <div v-else>
                                                    <h4 class="mb-2 text-h6 font-weight-medium text-truncate">{{
                                                        file.original_name }}
                                                    </h4>
                                                    <div
                                                        class="justify-center gap-4 d-flex align-center text-caption text-grey-darken-1">
                                                        <div class="d-flex align-center">
                                                            <v-icon size="14" class="mr-1">mdi-file-document</v-icon>
                                                            {{ formatFileSize(file.size) }}
                                                        </div>
                                                        <div class="d-flex align-center">
                                                            <v-icon size="14" class="mr-1">mdi-calendar</v-icon>
                                                            {{ formatDate(file.created_at) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </v-card-text>
                                    </v-card>
                                </v-col>
                            </v-row>


                        </v-card-text>
                    </v-card>

                    <!-- Enhanced Empty State -->
                    <v-card v-else elevation="0" class="text-center pa-12 border-thin no-watermark">
                        <div class="empty-state no-background">
                            <v-icon size="120" color="grey-lighten-2" class="mb-6">
                                <span v-if="searchQuery">mdi-file-search-outline</span>
                                <span v-else>mdi-file-plus-outline</span>
                            </v-icon>
                            <h3 class="mb-4 text-h4 font-weight-light">
                                <span v-if="searchQuery">No Files Found</span>
                                <span v-else>No Files in This Folder</span>
                            </h3>
                            <p class="mb-6 text-h6 text-grey-darken-1 font-weight-light">
                                <span v-if="searchQuery">No files match your search criteria "{{ searchQuery }}"</span>
                                <span v-else>Upload Excel files to start organizing your documents in this folder</span>
                            </p>
                            <div class="justify-center gap-3 d-flex">
                                <v-btn v-if="!searchQuery" color="primary" variant="flat" size="large"
                                    prepend-icon="mdi-upload" @click="triggerFileUpload" :loading="uploading"
                                    :disabled="uploading">
                                    Upload Your First File
                                </v-btn>
                                <v-btn v-else color="primary" variant="outlined" size="large"
                                    prepend-icon="mdi-close-circle-outline" @click="searchQuery = ''">
                                    Clear Search
                                </v-btn>
                            </div>
                        </div>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>

        <!-- Move File Dialog -->
        <v-dialog v-model="moveDialog" max-width="500" persistent>
            <v-card>
                <v-card-title class="text-white bg-primary">
                    <v-icon icon="mdi-folder-move" class="mr-2"></v-icon>
                    Move File
                </v-card-title>

                <v-card-text class="pt-4">
                    <div class="mb-4">
                        <h3 class="mb-2 text-h6">
                            {{ selectedFiles.length > 0 && !fileToMove ? 'Selected Files' : 'File Details' }}
                        </h3>
                        <v-card variant="outlined" class="pa-3">
                            <!-- Single file move -->
                            <div v-if="fileToMove" class="d-flex align-center">
                                <v-icon icon="mdi-file-document" class="mr-3" color="primary"></v-icon>
                                <div>
                                    <div class="font-weight-medium">{{ fileToMove.original_name }}</div>
                                    <div class="text-caption text-medium-emphasis">
                                        Current location: {{ folder.name }}
                                    </div>
                                </div>
                            </div>

                            <!-- Bulk file move -->
                            <div v-else-if="selectedFiles.length > 0" class="d-flex align-center">
                                <v-icon icon="mdi-file-multiple" class="mr-3" color="primary"></v-icon>
                                <div>
                                    <div class="font-weight-medium">{{ selectedFiles.length }} files selected</div>
                                    <div class="text-caption text-medium-emphasis">
                                        Current location: {{ folder.name }}
                                    </div>
                                </div>
                            </div>
                        </v-card>
                    </div>

                    <div class="mb-4">
                        <h3 class="mb-2 text-h6">Select Destination Folder</h3>
                        <v-select v-model="selectedDestinationFolder" :items="availableFolders" item-title="name"
                            item-value="id" label="Choose destination folder" variant="outlined"
                            :error-messages="moveError" :disabled="moving">
                            <template v-slot:item="{ props, item }">
                                <v-list-item v-bind="props" :disabled="item.raw.id === folder.id">
                                    <template v-slot:prepend>
                                        <v-icon icon="mdi-folder" class="mr-2"></v-icon>
                                    </template>
                                    <v-list-item-title>{{ item.raw.name }}</v-list-item-title>
                                    <v-list-item-subtitle>{{ item.raw.file_count }} files</v-list-item-subtitle>
                                </v-list-item>
                            </template>
                        </v-select>
                    </div>

                    <v-alert v-if="moveError" type="error" variant="tonal" class="mb-3">
                        {{ moveError }}
                    </v-alert>
                </v-card-text>

                <v-card-actions class="px-4 pb-4">
                    <v-spacer></v-spacer>
                    <v-btn color="grey-darken-1" variant="text" @click="cancelMove" :disabled="moving">
                        Cancel
                    </v-btn>
                    <v-btn color="primary" variant="flat" @click="confirmMove" :loading="moving"
                        :disabled="!selectedDestinationFolder">
                        Move File
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Delete Confirmation Dialog -->
        <v-dialog v-model="deleteDialog" max-width="400" persistent>
            <v-card>
                <v-card-title class="text-white bg-error">
                    <v-icon icon="mdi-delete" class="mr-2"></v-icon>
                    Confirm Deletion
                </v-card-title>
                <v-card-text class="pt-4">
                    <!-- Single file delete -->
                    <div v-if="fileToDelete">
                        <p>Are you sure you want to delete the file <strong>"{{ fileToDelete.original_name }}"</strong>?
                        </p>
                    </div>

                    <!-- Bulk file delete -->
                    <div v-else-if="selectedFiles.length > 0">
                        <p>Are you sure you want to delete <strong>{{ selectedFiles.length }} selected file(s)</strong>?
                        </p>
                        <v-alert type="info" variant="tonal" class="mt-3">
                            <div class="text-body-2">
                                This will permanently delete all selected files from the folder.
                            </div>
                        </v-alert>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="grey-darken-1" variant="text" @click="deleteDialog = false">
                        Cancel
                    </v-btn>
                    <v-btn color="error" variant="text" @click="confirmDelete" :loading="deleting">
                        Delete
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Error Dialog -->
        <v-dialog v-model="errorDialog" max-width="400" persistent>
            <v-card>
                <v-card-title class="text-white bg-error">
                    <v-icon icon="mdi-alert-circle" class="mr-2"></v-icon>
                    Error
                </v-card-title>
                <v-card-text class="pt-4">
                    <p>{{ errorMessage }}</p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="error" variant="text" @click="errorDialog = false">
                        OK
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Duplicate Files Dialog -->
        <v-dialog v-model="duplicateDialog" max-width="600" persistent>
            <v-card>
                <v-card-title class="text-white bg-warning">
                    <v-icon icon="mdi-file-multiple" class="mr-2"></v-icon>
                    Duplicate Files Found
                </v-card-title>
                <v-card-text class="pt-4">
                    <p class="mb-4">The following files have the same name as existing files in this folder. Please
                        choose how
                        to handle each duplicate:</p>

                    <div v-for="(duplicate, index) in duplicateResolutions" :key="index"
                        class="mb-4 border rounded pa-3">
                        <div class="mb-3 d-flex align-center">
                            <v-icon icon="mdi-file-document" class="mr-2" color="primary"></v-icon>
                            <div>
                                <div class="font-weight-medium">{{ duplicate.original_name }}</div>
                                <div class="text-caption text-medium-emphasis">
                                    {{ formatFileSize(duplicate.size) }}
                                </div>
                            </div>
                        </div>

                        <v-radio-group v-model="duplicate.action" density="compact" hide-details>
                            <v-radio value="proceed" color="primary">
                                <template v-slot:label>
                                    <div>
                                        <div class="font-weight-medium">Keep both files</div>
                                        <div class="text-caption text-medium-emphasis">
                                            The new file will be renamed with a number (e.g., "{{
                                                getNumberedFileName(duplicate.original_name) }}")
                                        </div>
                                    </div>
                                </template>
                            </v-radio>
                            <v-radio value="overwrite" color="error">
                                <template v-slot:label>
                                    <div>
                                        <div class="font-weight-medium">Replace existing file</div>
                                        <div class="text-caption text-medium-emphasis">
                                            The existing file will be permanently replaced with the new one
                                        </div>
                                    </div>
                                </template>
                            </v-radio>
                        </v-radio-group>
                    </div>
                </v-card-text>
                <v-card-actions class="px-4 pb-4">
                    <v-spacer></v-spacer>
                    <v-btn color="grey-darken-1" variant="text" @click="cancelDuplicateResolution">
                        Cancel
                    </v-btn>
                    <v-btn color="primary" variant="flat" @click="resolveDuplicates">
                        Proceed
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Success Dialog -->
        <v-dialog v-model="successDialog" max-width="400" persistent>
            <v-card>
                <v-card-title class="text-white bg-success">
                    <v-icon icon="mdi-check-circle" class="mr-2"></v-icon>
                    Success
                </v-card-title>
                <v-card-text class="pt-4">
                    <p>{{ successMessage }}</p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="green-darken-1" variant="flat" @click="successDialog = false">
                        OK
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </AppSidebarLayout>
</template>

<script setup>
import { Head } from "@inertiajs/vue3";
import { ref, computed, watch } from "vue";
import { router } from "@inertiajs/vue3";
import AppSidebarLayout from "@/Layouts/AppSidebarLayout.vue";

const props = defineProps({
    folder: Object,
    files: Array,
    operatingUnit: Object,
    availableFolders: Array,
});

// Reactive data
const fileInput = ref(null);
const uploading = ref(false);
const editingFile = ref(null);
const editingName = ref('');
const selectedFiles = ref([]);
const selectAll = ref(false);
const sortBy = ref('name');
const searchQuery = ref('');
const moveDialog = ref(false);
const fileToMove = ref(null);
const selectedDestinationFolder = ref(null);
const availableFolders = ref([]);
const moving = ref(false);
const moveError = ref('');
const deleteDialog = ref(false);
const fileToDelete = ref(null);
const deleting = ref(false);
const errorDialog = ref(false);
const errorMessage = ref('');
const successDialog = ref(false);
const successMessage = ref('');
const duplicateDialog = ref(false);
const duplicateFiles = ref([]);
const duplicateResolutions = ref([]);

// Sort options
const sortOptions = [
    { text: 'Name (A-Z)', value: 'name' },
    { text: 'Name (Z-A)', value: 'name_desc' },
    { text: 'Date Created (Newest)', value: 'created_desc' },
    { text: 'Date Created (Oldest)', value: 'created_asc' },
    { text: 'Size (Largest)', value: 'size_desc' },
    { text: 'Size (Smallest)', value: 'size_asc' },
];

// Computed properties
const filteredFiles = computed(() => {
    if (!searchQuery.value) return props.files;

    const query = searchQuery.value.toLowerCase().trim();
    return props.files.filter(file => {
        const nameMatch = file.original_name.toLowerCase().includes(query);
        // You can add more search criteria here if needed
        return nameMatch;
    });
});

const sortedFiles = computed(() => {
    const files = [...filteredFiles.value];

    switch (sortBy.value) {
        case 'name':
            return files.sort((a, b) => a.original_name.localeCompare(b.original_name));
        case 'name_desc':
            return files.sort((a, b) => b.original_name.localeCompare(a.original_name));
        case 'created_desc':
            return files.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        case 'created_asc':
            return files.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
        case 'size_desc':
            return files.sort((a, b) => b.size - a.size);
        case 'size_asc':
            return files.sort((a, b) => a.size - b.size);
        default:
            return files;
    }
});

// Methods
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
    });
};

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const goBack = () => {
    router.visit('/records-archive');
};



const triggerFileUpload = () => {
    console.log('triggerFileUpload called');
    console.log('fileInput.value:', fileInput.value);

    if (fileInput.value) {
        console.log('Clicking file input...');
        fileInput.value.click();
    } else {
        console.error('File input ref is null or undefined');
        // Fallback: try to find the file input by other means
        const fileInputElement = document.querySelector('input[type="file"]');
        if (fileInputElement) {
            console.log('Found file input via querySelector, clicking...');
            fileInputElement.click();
        } else {
            console.error('No file input found at all');
        }
    }
};

// Helper function to refresh CSRF token
const refreshCsrfToken = async () => {
    try {
        const response = await fetch('/csrf-token', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            },
        });

        if (response.ok) {
            const data = await response.json();
            const metaTag = document.querySelector('meta[name="csrf-token"]');
            if (metaTag && data.csrf_token) {
                metaTag.setAttribute('content', data.csrf_token);
                return data.csrf_token;
            }
        }
    } catch (error) {
        console.error('Failed to refresh CSRF token:', error);
    }
    return null;
};

const handleFileSelect = async (event) => {
    const files = event.target.files;
    console.log('Files selected:', files);

    if (!files || files.length === 0) {
        console.log('No files selected');
        return;
    }

    uploading.value = true;
    console.log('Starting upload process...');

    try {
        const formData = new FormData();
        Array.from(files).forEach(file => {
            console.log('Adding file to FormData:', file.name, file.size, file.type);
            formData.append('files[]', file);
        });
        formData.append('folder_id', props.folder.id);

        // Get CSRF token with better error handling
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        console.log('CSRF Token:', csrfToken ? `Found: ${csrfToken.substring(0, 10)}...` : 'Not found');
        console.log('CSRF Token length:', csrfToken ? csrfToken.length : 0);

        if (!csrfToken) {
            throw new Error('CSRF token not found. Please refresh the page and try again.');
        }

        const uploadUrl = `/records-archive/folders/${props.folder.id}/upload`;
        console.log('Upload URL:', uploadUrl);

        // Add CSRF token to FormData as well (fallback method)
        formData.append('_token', csrfToken);

        // Debug FormData contents
        console.log('FormData contents:');
        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                console.log(`${key}: File - ${value.name} (${value.size} bytes)`);
            } else {
                console.log(`${key}: ${value}`);
            }
        }

        const response = await fetch(uploadUrl, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': csrfToken,
            },
            body: formData,
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        const result = await response.json();
        console.log('Response data:', result);

        if (response.ok) {
            if (result.duplicates_found) {
                // Handle duplicates
                duplicateFiles.value = result.duplicates;
                duplicateResolutions.value = result.duplicates.map(duplicate => ({
                    ...duplicate,
                    action: 'proceed' // Default action
                }));
                duplicateDialog.value = true;
            } else {
                successMessage.value = 'Files uploaded successfully!';
                successDialog.value = true;
                router.reload();
            }
        } else {
            // Handle specific CSRF token mismatch error with retry
            if (response.status === 419) {
                console.log('CSRF token mismatch detected, attempting to refresh token and retry...');
                const newToken = await refreshCsrfToken();

                if (newToken) {
                    console.log('CSRF token refreshed, retrying upload...');
                    // Retry the upload with the new token
                    formData.set('_token', newToken);

                    const retryResponse = await fetch(uploadUrl, {
                        method: 'POST',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': newToken,
                        },
                        body: formData,
                    });

                    const retryResult = await retryResponse.json();

                    if (retryResponse.ok) {
                        if (retryResult.duplicates_found) {
                            duplicateFiles.value = retryResult.duplicates;
                            duplicateResolutions.value = retryResult.duplicates.map(duplicate => ({
                                ...duplicate,
                                action: 'proceed'
                            }));
                            duplicateDialog.value = true;
                        } else {
                            successMessage.value = 'Files uploaded successfully!';
                            successDialog.value = true;
                            router.reload();
                        }
                        return; // Exit successfully
                    } else {
                        throw new Error(retryResult.message || retryResult.error || 'Failed to upload files after token refresh');
                    }
                } else {
                    throw new Error('CSRF token mismatch. Please refresh the page and try again.');
                }
            }
            throw new Error(result.message || result.error || 'Failed to upload files');
        }
    } catch (error) {
        console.error('Upload error:', error);
        errorMessage.value = `Failed to upload files: ${error.message}`;
        errorDialog.value = true;
    } finally {
        uploading.value = false;
        // Reset the file input
        event.target.value = '';
    }
};

// Selection methods
const toggleSelectAll = () => {
    if (selectAll.value) {
        selectedFiles.value = filteredFiles.value.map(file => file.id);
    } else {
        selectedFiles.value = [];
    }
};

const clearSelection = () => {
    selectedFiles.value = [];
    selectAll.value = false;
};

// Watch for changes in selectedFiles to update selectAll state
watch(selectedFiles, (newSelection) => {
    if (newSelection.length === 0) {
        selectAll.value = false;
    } else if (newSelection.length === filteredFiles.value.length) {
        selectAll.value = true;
    } else {
        selectAll.value = false;
    }
}, { deep: true });

// Duplicate handling methods
const resolveDuplicates = async () => {
    try {
        const response = await fetch(`/records-archive/folders/${props.folder.id}/resolve-duplicates`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
            },
            body: JSON.stringify({
                duplicates: duplicateResolutions.value
            }),
        });

        const result = await response.json();

        if (response.ok) {
            duplicateDialog.value = false;
            successMessage.value = 'Files processed successfully!';
            successDialog.value = true;
            router.reload();
        } else {
            throw new Error(result.error || 'Failed to resolve duplicates');
        }
    } catch (error) {
        console.error('Duplicate resolution error:', error);
        errorMessage.value = `Failed to resolve duplicates: ${error.message}`;
        errorDialog.value = true;
    }
};

const cancelDuplicateResolution = () => {
    duplicateDialog.value = false;
    duplicateFiles.value = [];
    duplicateResolutions.value = [];
};

const getNumberedFileName = (originalName) => {
    const lastDotIndex = originalName.lastIndexOf('.');
    if (lastDotIndex === -1) {
        return originalName + ' (1)';
    }
    const baseName = originalName.substring(0, lastDotIndex);
    const extension = originalName.substring(lastDotIndex);
    return baseName + ' (1)' + extension;
};

// Bulk operations
const bulkMoveFiles = () => {
    if (selectedFiles.value.length === 0) return;

    fileToMove.value = null; // Clear single file move
    selectedDestinationFolder.value = null;
    moveError.value = '';

    // Use folders from props
    availableFolders.value = props.availableFolders || [];

    moveDialog.value = true;
};

const bulkDeleteFiles = () => {
    if (selectedFiles.value.length === 0) return;

    fileToDelete.value = null; // Clear single file delete
    deleteDialog.value = true;
};

const viewFile = (file) => {
    router.visit(`/records-archive/files/${file.id}`);
};

const moveFile = (file) => {
    fileToMove.value = file;
    selectedDestinationFolder.value = null;
    moveError.value = '';

    // Use folders from props
    availableFolders.value = props.availableFolders || [];

    moveDialog.value = true;
};



const cancelMove = () => {
    moveDialog.value = false;
    fileToMove.value = null;
    selectedDestinationFolder.value = null;
    moveError.value = '';
    moving.value = false;
};

const confirmMove = async () => {
    if (!selectedDestinationFolder.value) return;

    // Check if this is a bulk operation or single file operation
    const isBulkOperation = selectedFiles.value.length > 0 && !fileToMove.value;

    if (!isBulkOperation && !fileToMove.value) return;

    moving.value = true;
    moveError.value = '';

    try {
        let response, result;

        if (isBulkOperation) {
            // Bulk move operation
            response = await fetch('/records-archive/files/bulk-move', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify({
                    file_ids: selectedFiles.value,
                    destination_folder_id: selectedDestinationFolder.value,
                }),
            });
        } else {
            // Single file move operation
            response = await fetch(`/records-archive/files/${fileToMove.value.id}/move`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify({
                    destination_folder_id: selectedDestinationFolder.value,
                }),
            });
        }

        result = await response.json();

        if (response.ok) {
            moveDialog.value = false;
            successMessage.value = result.message || 'File(s) moved successfully!';
            successDialog.value = true;

            // Clear selection after successful bulk operation
            if (isBulkOperation) {
                clearSelection();
            }

            router.reload();
        } else {
            moveError.value = result.error || 'Failed to move file(s)';
        }
    } catch (error) {
        console.error('Move file error:', error);
        moveError.value = 'Failed to move file(s). Please try again.';
    } finally {
        moving.value = false;
    }
};

const renameFile = (file) => {
    editingFile.value = file.id;
    editingName.value = file.original_name;
};

const confirmFileRename = async (file) => {
    if (!editingName.value || editingName.value.trim() === file.original_name) {
        cancelFileRename();
        return;
    }

    try {
        const response = await fetch(`/records-archive/files/${file.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
            body: JSON.stringify({
                name: editingName.value.trim(),
            }),
        });

        const result = await response.json();

        if (response.ok) {
            cancelFileRename();
            router.reload();
        } else {
            throw new Error(result.error || 'Failed to rename file');
        }
    } catch (error) {
        console.error('Rename file error:', error);
        errorMessage.value = 'Failed to rename file. Please try again.';
        errorDialog.value = true;
        cancelFileRename();
    }
};

const cancelFileRename = () => {
    editingFile.value = null;
    editingName.value = '';
};

const deleteFile = (file) => {
    fileToDelete.value = file;
    deleteDialog.value = true;
};

const confirmDelete = async () => {
    // Check if this is a bulk operation or single file operation
    const isBulkOperation = selectedFiles.value.length > 0 && !fileToDelete.value;

    if (!isBulkOperation && !fileToDelete.value) return;

    deleting.value = true;

    try {
        let response, result;

        if (isBulkOperation) {
            // Bulk delete operation
            response = await fetch('/records-archive/files/bulk-delete', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify({
                    file_ids: selectedFiles.value,
                }),
            });
        } else {
            // Single file delete operation
            response = await fetch(`/records-archive/files/${fileToDelete.value.id}`, {
                method: 'DELETE',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
            });
        }

        result = await response.json();

        if (response.ok) {
            deleteDialog.value = false;
            successMessage.value = result.message || 'File(s) deleted successfully!';
            successDialog.value = true;

            // Clear selection after successful bulk operation
            if (isBulkOperation) {
                clearSelection();
            }

            router.reload();
        } else {
            deleteDialog.value = false;
            errorMessage.value = result.error || 'Failed to delete file(s)';
            errorDialog.value = true;
        }
    } catch (error) {
        console.error('Delete file error:', error);
        deleteDialog.value = false;
        errorMessage.value = 'Failed to delete file(s). Please try again.';
        errorDialog.value = true;
    } finally {
        deleting.value = false;
        fileToDelete.value = null;
    }
};

const getFileIcon = (filename) => {
    const extension = filename.split('.').pop().toLowerCase();
    switch (extension) {
        case 'xlsx':
        case 'xls':
            return 'mdi-file-excel';
        case 'csv':
            return 'mdi-file-delimited';
        default:
            return 'mdi-file';
    }
};

const getFileIconColor = (filename) => {
    const extension = filename.split('.').pop().toLowerCase();
    switch (extension) {
        case 'xlsx':
        case 'xls':
            return 'green-darken-2';
        case 'csv':
            return 'blue-darken-2';
        default:
            return 'grey-darken-2';
    }
};
</script>

<style scoped>
.file-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.file-menu-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(4px);
}

.file-card:hover .file-menu-btn {
    opacity: 1;
}

.file-checkbox {
    position: absolute;
    top: 4px;
    left: 4px;
    z-index: 2;
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(4px);
    border-radius: 4px;
    padding: 2px;
}

.inline-edit {
    width: 100%;
}

.inline-edit-field {
    background-color: white;
    border: 2px solid #1976d2;
    border-radius: 4px;
}

.inline-edit-field .v-field {
    background-color: white !important;
}

.file-list-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.file-list-item:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

.selected-list-item {
    background-color: rgba(46, 125, 50, 0.08) !important;
}

/* Enhanced text readability for file list */
.file-list-item .v-list-item-title {
    color: rgba(0, 0, 0, 0.87) !important;
}

.file-list-item .v-list-item-subtitle {
    color: rgba(0, 0, 0, 0.6) !important;
}

/* Border utilities */
.border-thin {
    border: 1px solid rgba(0, 0, 0, 0.12);
}

.border-b-thin {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

/* Empty state styling */
.empty-state {
    max-width: 500px;
    margin: 0 auto;
    position: relative;
    z-index: 10;
}

/* Prevent any background watermarks or pseudo-elements */
.no-watermark,
.no-watermark::before,
.no-watermark::after,
.no-background,
.no-background::before,
.no-background::after {
    background-image: none !important;
    background-color: transparent !important;
    content: none !important;
    display: block !important;
}

/* Ensure clean background for empty state */
.no-watermark {
    background: white !important;
    overflow: hidden !important;
    position: relative !important;
}

/* Debug: Add a solid background to identify the issue */
.no-watermark * {
    background-image: none !important;
    text-shadow: none !important;
}

/* Force clean container */
.v-container,
.v-row,
.v-col {
    background-image: none !important;
    position: relative !important;
}

/* File card hover effects */
.file-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Selected card styling */
.selected-card {
    border-color: #2E7D32 !important;
    background: rgba(46, 125, 50, 0.05);
}

/* File icon animations */
.file-icon-container {
    position: relative;
    display: inline-block;
}

.file-icon {
    transition: all 0.3s ease;
}

.hover-card:hover .file-icon {
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* File menu button */
.file-menu-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(4px);
}

.file-card:hover .file-menu-btn {
    opacity: 1;
}

/* File checkbox */
.file-checkbox {
    position: absolute;
    top: 4px;
    left: 4px;
    z-index: 2;
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(4px);
    border-radius: 4px;
    padding: 2px;
}

/* Breadcrumbs styling */
.v-breadcrumbs {
    padding: 0;
}

.v-breadcrumbs .v-btn {
    text-transform: none;
    letter-spacing: normal;
}

.v-breadcrumbs-divider {
    margin: 0 8px;
    opacity: 0.6;
}

/* Search field styling */
.search-field {
    transition: all 0.3s ease;
}

.search-field:focus-within {
    transform: scale(1.02);
}

.search-field .v-field {
    background-color: rgba(255, 255, 255, 0.9);
}

.search-field .v-field--focused {
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
