<script setup>
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';
import DeleteUserForm from './Partials/DeleteUserForm.vue';
import UpdatePasswordForm from './Partials/UpdatePasswordForm.vue';
import UpdateProfileInformationForm from './Partials/UpdateProfileInformationForm.vue';
import { Head } from '@inertiajs/vue3';

defineProps({
    mustVerifyEmail: {
        type: Boolean,
    },
    status: {
        type: String,
    },
});
</script>

<template>

    <Head title="Account Settings" />

    <AppSidebarLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">
                Account Settings
            </h2>
        </template>

        <v-container>
            <v-row>
                <v-col cols="12">
                    <v-card elevation="2" class="mb-6">
                        <v-card-title class="text-white bg-primary">
                            Profile Information
                        </v-card-title>
                        <v-card-text>
                            <UpdateProfileInformationForm :must-verify-email="mustVerifyEmail" :status="status" />
                        </v-card-text>
                    </v-card>

                    <v-card elevation="2" class="mb-6">
                        <v-card-title class="text-white bg-primary">
                            Update Password
                        </v-card-title>
                        <v-card-text>
                            <UpdatePasswordForm />
                        </v-card-text>
                    </v-card>

                    <v-card elevation="2">
                        <v-card-title class="text-white bg-primary">
                            Delete Account
                        </v-card-title>
                        <v-card-text>
                            <DeleteUserForm />
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </AppSidebarLayout>
</template>
