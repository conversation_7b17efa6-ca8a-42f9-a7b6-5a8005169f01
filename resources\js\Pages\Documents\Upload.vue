<template>

  <Head title="Upload Document" />
  <AppSidebarLayout>
    <v-container>
      <v-row>
        <v-col cols="12">
          <h1 class="mb-4 text-h4">Upload Document</h1>
          <p class="mb-6 text-subtitle-1">
            Upload the scanned document with QR code for: <strong>{{ document.control_number }}</strong>
          </p>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" md="6">
          <v-card elevation="2">
            <v-card-title class="text-white bg-primary">Document Information</v-card-title>
            <v-card-text>
              <v-list>
                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-file-document" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">Control Number</div>
                    <div class="text-body-2">{{ document.control_number }}</div>
                  </div>
                </v-list-item>

                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-account" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">Signatory</div>
                    <div class="text-body-2">{{ document.signatory }}</div>
                  </div>
                </v-list-item>

                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-office-building" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">Origin</div>
                    <div class="text-body-2">{{ document.origin }}</div>
                  </div>
                </v-list-item>

                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-information" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">Subject Matter</div>
                    <div class="text-body-2">{{ document.subject_matter }}</div>
                  </div>
                </v-list-item>
              </v-list>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="6">
          <v-card elevation="2">
            <v-card-title class="mb-4 text-white bg-primary">Upload Document</v-card-title>
            <v-card-text>
              <v-form @submit.prevent="submit">
                <v-file-input v-model="form.file" label="Select Document File" accept=".pdf,.jpg,.jpeg,.png"
                  :error-messages="errors.file" prepend-icon="mdi-file-upload" show-size counter required
                  :loading="form.processing"></v-file-input>

                <div class="justify-end mt-4 d-flex">
                  <v-btn type="submit" color="blue-darken-1" variant="flat" size="large" :loading="form.processing">
                    Upload Document
                  </v-btn>
                </div>
              </v-form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <v-row class="mt-6">
        <v-col cols="12">
          <v-card elevation="2">
            <v-card-title class="mb-4 text-white bg-primary">Instructions</v-card-title>
            <v-card-text>
              <ol class="ml-4">
                <li class="mb-2">Scan the document with the QR code.</li>
                <li class="mb-2">Save the scanned document as a PDF, JPG, or PNG file.</li>
                <li class="mb-2">Click the "Select Document File" button above to choose the file.</li>
                <li class="mb-2">Click "Upload Document" to complete the process.</li>
              </ol>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, useForm, router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
  document: Object,
  errors: Object,
});

const form = useForm({
  file: null,
});

const submit = () => {
  form.post(`/documents/${props.document.id}/upload`, {
    forceFormData: true,
    onSuccess: () => {
      form.reset();
      // Show success message
      alert('Document uploaded successfully!');
      // Redirect to documents list
      router.visit('/documents');
    },
  });
};
</script>

<style scoped>
.v-list-item {
  margin-bottom: 8px;
}

.text-subtitle-1.font-weight-medium {
  color: rgba(0, 0, 0, 0.87);
  font-weight: 500;
}

.text-body-2 {
  color: rgba(0, 0, 0, 0.6);
}
</style>
