<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Inertia\Inertia;

class ReportsController extends Controller
{
    public function index(Request $request)
    {
        $user      = Auth::user();
        $documents = $this->getUserDocuments($request, $user);

        // Fetch the authenticated user's preferences
        $preferences = Auth::user()->preferences()
            ->get()
            ->mapWithKeys(function ($pref) {
                return [$pref->preference_key => $pref->preference_value];
            });

        return Inertia::render('Reports/Index', [
            'documents' => $documents,
            'preferences' => $preferences, // ✅ send to Vue
        ]);
    }

    private function getUserDocuments(Request $request, User $user)
    {
        // dd($request);
        $perPage = $request->input('per_page', -1);
        $getAllRecords = $perPage == -1;
        $page = $request->input('page', 1);

        if ($user->isSuperAdmin()) {
            $query = Document::with('receivedBy')->where('received_by_id', $user->id);
            if ($getAllRecords) {
                $allDocuments = $query->orderBy('created_at', 'desc')->get();
                return [
                    'data' => $allDocuments,
                    'total' => $allDocuments->count(),
                    'per_page' => -1,
                    'current_page' => 1,
                    'last_page' => 1,
                ];
            }
            return $query->orderBy('created_at', 'desc')->paginate($perPage);
        }

        if ($user->isAdmin() && $user->operating_unit_id) {
            $query = Document::with('receivedBy')->where('received_by_id', $user->id);

            if ($getAllRecords) {
                $allDocuments = $query->orderBy('created_at', 'desc')->get();
                return [
                    'data' => $allDocuments,
                    'total' => $allDocuments->count(),
                    'per_page' => -1,
                    'current_page' => 1,
                    'last_page' => 1,
                ];
            }

            return $query->orderBy('created_at', 'desc')->paginate($perPage);
        }

        // Default: normal user
        $query = Document::with('receivedBy')->where('received_by_id', $user->id);
        if ($getAllRecords) {
            $allDocuments = $query->orderBy('created_at', 'desc')->get();
            return [
                'data' => $allDocuments,
                'total' => $allDocuments->count(),
                'per_page' => -1,
                'current_page' => 1,
                'last_page' => 1,
            ];
        }
        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    public function showPrintableReport(Request $request)
    {
        $reference = $request->input('reference_document');
        $year = $request->input('year');
        $month = $request->input('month');

        $documents = Document::with('receivedBy')
            ->when($reference !== 'All Reference Documents', function ($query) use ($reference) {
                return $query->where('reference_document', $reference);
            })
            ->whereYear('created_at', $year)
            ->when($month, function ($query) use ($month) {
                return $query->whereMonth('created_at', $month);
            })
            ->orderBy('created_at', 'asc')
            ->get();

        return view('print-reports', compact('documents'));
    }

    public function show($id)
    {
        // Fetch the documents or data you want to show
        $documents = Document::with('receivedBy')
            ->where('reference_document', $id)
            ->orderBy('created_at', 'asc')
            ->get();

        return view('print-reports', compact('documents'));
    }

    public function print(Request $request)
    {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', '300');
        
        $documents = $request->input('data');

        $pdf = Pdf::loadView('report', [
            'documents' => $documents
        ])->setPaper([0, 0, 936, 612]); // 8.5 x 13 inches in landscape

        return $pdf->download('filtered-report.pdf');
    }
}
