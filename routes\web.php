<?php

use App\Http\Controllers\ArchiveController;
use App\Http\Controllers\DepartmentUnitController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\ForwardedDocumentsController;
use App\Http\Controllers\InboxController;
use App\Http\Controllers\OperatingUnitController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\DocumentTracking;
use App\Http\Controllers\DocumentCodeController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\UserPreferencesController;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Redirect the root URL to the login page
Route::get('/', function () {
    return redirect()->route('login');
});

Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::get('/print-qr-code', function () {
    $qrCodeUrl = request('url');
    $type = request('type'); // 'received' or 'released'
    // Log::info('Print QR code requested for URL: ' . $qrCodeUrl . ' | Type: ' . $type);
    return view('print-qr-code', ['qrCodeUrl' => $qrCodeUrl, 'type' => $type]);
});

Route::get('/hello-pdf', function () {
    $pdf = Pdf::loadView('report')
        ->setPaper('Legal', 'portrait'); // ✅ Correct way
    return $pdf->stream('report.pdf');
});



Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Document Tracking
    Route::get('/tracking', [DocumentTracking::class, 'index']);

    // Document Management Routes
    Route::resource('documents', DocumentController::class);
    Route::post('/documents/updateStatus', [DocumentController::class, 'updateStatus'])->name('documents.updateStatus');

    Route::post('/preferences', [UserPreferencesController::class, 'store'])->name('preferences.store');

    // User Preferences
    Route::resource('preferences', UserPreferencesController::class);

    // Document QR Code Generation
    Route::get('/documents/{document}/generate-qr', [DocumentController::class, 'showGenerateQr'])->name('documents.generate-qr');
    Route::post('/documents/{document}/generate-qr', [DocumentController::class, 'generateQr'])->name('documents.generate-qr.store');

    // Document Upload
    Route::get('/documents/{document}/upload', [DocumentController::class, 'showUpload'])->name('documents.upload');
    Route::post('/documents/{document}/upload', [DocumentController::class, 'upload'])->name('documents.upload.store');

    // Document File Removal
    Route::delete('/documents/{document}/remove-file', [DocumentController::class, 'removeFile'])->name('documents.remove-file');

    // Document Date Modification with Password Verification
    Route::post('/documents/{document}/verify-password', [DocumentController::class, 'verifyPassword'])->name('documents.verify-password');

    // Document Forwarding
    Route::get('/documents/{document}/forward', [DocumentController::class, 'showForward'])->name('documents.forward');
    Route::post('/documents/{document}/forward', [DocumentController::class, 'forward'])->name('documents.forward.store');

    // Operating Units Management
    Route::resource('operating-units', OperatingUnitController::class);

    //  Department Units Management
    Route::resource('department-units', DepartmentUnitController::class);

    // User Management
    Route::resource('users', UserController::class);

    // Inbox Management
    Route::resource('inbox', InboxController::class);
    Route::post('/inbox/{id}/mark-as-read', [InboxController::class, 'markAsRead'])->name('inbox.mark-as-read');
    Route::post('/inbox/{id}/mark-as-unread', [InboxController::class, 'markAsUnread'])->name('inbox.mark-as-unread');
    Route::post('/inbox/{id}/acknowledge', [InboxController::class, 'acknowledgeDocument'])->name('inbox.acknowledge');

    // Forwarded Documents Management
    Route::get('/forwarded-documents', [ForwardedDocumentsController::class, 'index'])->name('forwarded-documents.index');
    Route::get('/forwarded-documents/forwarded/{id}', [ForwardedDocumentsController::class, 'showForwarded'])->name('forwarded-documents.show-forwarded');
    Route::get('/forwarded-documents/{id}', [ForwardedDocumentsController::class, 'show'])->name('forwarded-documents.show');
    Route::delete('/forwarded-documents/{id}', [ForwardedDocumentsController::class, 'destroy'])->name('forwarded-documents.destroy');

    // Document Codes Management
    Route::resource('document-codes', DocumentCodeController::class);
    Route::delete('/document-codes', [DocumentCodeController::class, 'destroy'])->name('document-codes.destroy');
    Route::put('/document-codes/{document_code}', [DocumentCodeController::class, 'update'])->name('document-codes.update');

    // Reports
    Route::resource('reports', ReportsController::class);
    Route::post('/reports/print', [ReportsController::class, 'print']);

    // Records Archive Management
    Route::get('/records-archive', [ArchiveController::class, 'index'])->name('records-archive.index');
    Route::get('/records-archive/folders/{id}', [ArchiveController::class, 'showFolder'])->name('records-archive.folders.show');
    Route::get('/records-archive/files/{id}', [ArchiveController::class, 'show'])->name('records-archive.show');

    Route::put('/records-archive/files/{id}', [ArchiveController::class, 'updateFile'])->name('records-archive.files.update');
    Route::put('/records-archive/files/{id}/move', [ArchiveController::class, 'moveFile'])->name('records-archive.files.move');
    Route::post('/records-archive/files/bulk-move', [ArchiveController::class, 'bulkMoveFiles'])->name('records-archive.files.bulk-move');
    Route::delete('/records-archive/files/bulk-delete', [ArchiveController::class, 'bulkDeleteFiles'])->name('records-archive.files.bulk-delete');

    Route::delete('/records-archive/files/{id}', [ArchiveController::class, 'destroy'])->name('records-archive.destroy');
    Route::post('/records-archive/folders', [ArchiveController::class, 'createFolder'])->name('records-archive.folders.create');
    Route::put('/records-archive/folders/{id}', [ArchiveController::class, 'renameFolder'])->name('records-archive.folders.rename');
    Route::get('/records-archive/folders/{id}/details', [ArchiveController::class, 'getFolderDetails'])->name('records-archive.folders.details');
    Route::delete('/records-archive/folders/bulk-delete', [ArchiveController::class, 'bulkDeleteFolders'])->name('records-archive.folders.bulk-delete');
    Route::delete('/records-archive/folders/{id}', [ArchiveController::class, 'deleteFolder'])->name('records-archive.folders.destroy');
    Route::post('/records-archive/folders/{id}/upload', [ArchiveController::class, 'uploadFiles'])->name('records-archive.folders.upload');
    Route::post('/records-archive/folders/{id}/resolve-duplicates', [ArchiveController::class, 'resolveDuplicates'])->name('records-archive.folders.resolve-duplicates');

    // CSRF token refresh route
    Route::get('/csrf-token', function () {
        return response()->json(['csrf_token' => csrf_token()]);
    })->name('csrf-token');
});

require __DIR__ . '/auth.php';
