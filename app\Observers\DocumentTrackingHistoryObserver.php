<?php

namespace App\Observers;

use App\Models\DocumentTrackingHistory;
use App\Services\OperatingUnitSyncService;
use Illuminate\Support\Facades\Log;

class DocumentTrackingHistoryObserver
{
    /**
     * @var OperatingUnitSyncService
     */
    protected $syncService;

    /**
     * Constructor
     * 
     * @param OperatingUnitSyncService $syncService
     */
    public function __construct(OperatingUnitSyncService $syncService)
    {
        $this->syncService = $syncService;
    }

    /**
     * Handle the DocumentTrackingHistory "created" event.
     */
    public function created(DocumentTrackingHistory $tracking): void
    {
        $this->clearCacheForTracking($tracking);
        Log::info('Document tracking history created, cache cleared', ['tracking_id' => $tracking->id]);
    }

    /**
     * Handle the DocumentTrackingHistory "updated" event.
     */
    public function updated(DocumentTrackingHistory $tracking): void
    {
        $this->clearCacheForTracking($tracking);
        Log::info('Document tracking history updated, cache cleared', ['tracking_id' => $tracking->id]);
    }

    /**
     * Handle the DocumentTrackingHistory "deleted" event.
     */
    public function deleted(DocumentTrackingHistory $tracking): void
    {
        $this->clearCacheForTracking($tracking);
        Log::info('Document tracking history deleted, cache cleared', ['tracking_id' => $tracking->id]);
    }

    /**
     * Clear cache for the tracking's operating unit
     */
    protected function clearCacheForTracking(DocumentTrackingHistory $tracking): void
    {
        // Load relationships if not already loaded
        if (!$tracking->relationLoaded('handledBy')) {
            $tracking->load('handledBy');
        }

        // Clear cache for the handler's operating unit
        if ($tracking->handledBy && $tracking->handledBy->operating_unit_id) {
            $this->syncService->clearOperatingUnitCache($tracking->handledBy->operating_unit_id);
        }

        // If the tracking has a document, clear cache for the document's operating unit
        if ($tracking->document_id) {
            if (!$tracking->relationLoaded('document')) {
                $tracking->load('document');
            }

            if ($tracking->document && $tracking->document->origin_unit_id) {
                $this->syncService->clearOperatingUnitCache($tracking->document->origin_unit_id);
            }
        }
    }
}
