<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DepartmentUnit extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'operating_unit_id'
    ];

    /**
     * Get the operating unit that this department unit belongs to.
     */
    public function operatingUnit(): BelongsTo
    {
        return $this->belongsTo(OperatingUnit::class);
    }

    /**
     * Get the documents associated with this department unit.
     * This is a placeholder relationship that will need to be implemented
     * when department_unit_id is added to the documents table.
     */
    public function documents(): HasMany
    {
        // This is a placeholder. In the future, you might want to add a department_unit_id
        // to the documents table to track which department a document belongs to.
        return $this->hasMany(Document::class, 'department_unit_id');
    }
}
