<?php

namespace App\Observers;

use App\Events\NewInboxMessage;
use App\Models\Inbox;
use App\Services\OperatingUnitSyncService;
use Illuminate\Support\Facades\Log;

class InboxObserver
{
    /**
     * @var OperatingUnitSyncService
     */
    protected $syncService;

    /**
     * Constructor
     *
     * @param OperatingUnitSyncService $syncService
     */
    public function __construct(OperatingUnitSyncService $syncService)
    {
        $this->syncService = $syncService;
    }

    /**
     * Handle the Inbox "created" event.
     */
    public function created(Inbox $inbox): void
    {
        // Load the sender relationship to include in the broadcast
        $inbox->load('sender');

        // Broadcast the new message event
        event(new NewInboxMessage($inbox));

        // Clear cache for the operating units involved
        $this->clearCacheForInbox($inbox);

        // Log for debugging
        Log::info('New inbox message created and broadcasted, cache cleared', [
            'inbox_id' => $inbox->id,
            'recipient_id' => $inbox->recipient_id,
            'subject' => $inbox->subject
        ]);
    }

    /**
     * Handle the Inbox "updated" event.
     */
    public function updated(Inbox $inbox): void
    {
        // Clear cache for the operating units involved
        $this->clearCacheForInbox($inbox);

        Log::info('Inbox message updated, cache cleared', [
            'inbox_id' => $inbox->id
        ]);
    }

    /**
     * Handle the Inbox "deleted" event.
     */
    public function deleted(Inbox $inbox): void
    {
        // Clear cache for the operating units involved
        $this->clearCacheForInbox($inbox);

        Log::info('Inbox message deleted, cache cleared', [
            'inbox_id' => $inbox->id
        ]);
    }

    /**
     * Clear cache for the inbox's operating units
     */
    protected function clearCacheForInbox(Inbox $inbox): void
    {
        // Load relationships if not already loaded
        if (!$inbox->relationLoaded('sender')) {
            $inbox->load('sender');
        }

        if (!$inbox->relationLoaded('recipient')) {
            $inbox->load('recipient');
        }

        // Clear cache for the sender's operating unit
        if ($inbox->sender && $inbox->sender->operating_unit_id) {
            $this->syncService->clearOperatingUnitCache($inbox->sender->operating_unit_id);
        }

        // Clear cache for the recipient's operating unit
        if ($inbox->recipient && $inbox->recipient->operating_unit_id) {
            $this->syncService->clearOperatingUnitCache($inbox->recipient->operating_unit_id);
        }
    }
}
