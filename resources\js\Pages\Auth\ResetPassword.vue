<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps({
    email: {
        type: String,
        required: true,
    },
    token: {
        type: String,
        required: true,
    },
});

const form = useForm({
    token: props.token,
    email: props.email,
    password: '',
    password_confirmation: '',
});

const isLoading = ref(false);
const showPassword = ref(false);
const showPasswordConfirmation = ref(false);

const submit = () => {
    isLoading.value = true;
    form.post(route('password.store'), {
        onFinish: () => {
            form.reset('password', 'password_confirmation');
            isLoading.value = false;
        },
    });
};
</script>

<template>
    <div class="reset-password-page">

        <Head title="Reset Password - Records Management Tracking System" />

        <div class="reset-password-container">
            <div class="reset-password-left">
                <div class="login-header">
                    <div class="logo-container">
                        <img src="/dmmmsu.png" alt="DMMMSU Logo" class="logo" />
                    </div>
                    <h1 class="app-name">Records Management Tracking System</h1>
                    <p class="app-description">Document Tracking & Management</p>
                </div>

                <div class="login-features">
                    <div class="feature">
                        <div class="feature-icon">
                            <v-icon icon="mdi-file-document-outline" size="large" color="white"></v-icon>
                        </div>
                        <div class="feature-text">
                            <h3>Document Management</h3>
                            <p>Organize and manage all your documents in one place</p>
                        </div>
                    </div>

                    <div class="feature">
                        <div class="feature-icon">
                            <v-icon icon="mdi-map-marker-path" size="large" color="white"></v-icon>
                        </div>
                        <div class="feature-text">
                            <h3>Document Tracking</h3>
                            <p>Track the movement and status of your documents</p>
                        </div>
                    </div>

                    <div class="feature">
                        <div class="feature-icon">
                            <v-icon icon="mdi-qrcode-scan" size="large" color="white"></v-icon>
                        </div>
                        <div class="feature-text">
                            <h3>QR Code Integration</h3>
                            <p>Easily scan and identify documents with QR codes</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="reset-password-right">
                <div class="reset-password-form-container">
                    <h2 class="reset-password-title">Reset Password</h2>

                    <div class="reset-password-description">
                        Create a new secure password for your account.
                    </div>

                    <form @submit.prevent="submit" class="reset-password-form">
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <div class="input-with-icon">
                                <v-icon icon="mdi-email-outline" size="small" class="input-icon"></v-icon>
                                <input id="email" type="email" v-model="form.email" required autocomplete="username"
                                    placeholder="Enter your email" readonly />
                            </div>
                            <div v-if="form.errors.email" class="error-message">{{ form.errors.email }}</div>
                        </div>

                        <div class="form-group">
                            <label for="password">New Password</label>
                            <div class="input-with-icon">
                                <v-icon icon="mdi-lock-outline" size="small" class="input-icon"></v-icon>
                                <input id="password" :type="showPassword ? 'text' : 'password'" v-model="form.password"
                                    required autocomplete="new-password" placeholder="Enter new password" />
                                <v-icon :icon="showPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'" size="small"
                                    class="toggle-password" @click="showPassword = !showPassword"></v-icon>
                            </div>
                            <div v-if="form.errors.password" class="error-message">{{ form.errors.password }}</div>
                        </div>

                        <div class="form-group">
                            <label for="password_confirmation">Confirm Password</label>
                            <div class="input-with-icon">
                                <v-icon icon="mdi-lock-check-outline" size="small" class="input-icon"></v-icon>
                                <input id="password_confirmation" :type="showPasswordConfirmation ? 'text' : 'password'"
                                    v-model="form.password_confirmation" required autocomplete="new-password"
                                    placeholder="Confirm new password" />
                                <v-icon :icon="showPasswordConfirmation ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                                    size="small" class="toggle-password"
                                    @click="showPasswordConfirmation = !showPasswordConfirmation"></v-icon>
                            </div>
                            <div v-if="form.errors.password_confirmation" class="error-message">
                                {{ form.errors.password_confirmation }}
                            </div>
                        </div>

                        <div class="form-actions">
                            <Link :href="route('login')" class="back-to-login">
                            Back to Login
                            </Link>

                            <button type="submit" class="reset-button" :disabled="form.processing || isLoading">
                                <!-- <v-progress-circular v-if="isLoading" indeterminate size="20" width="2" color="white"
                                    class="mr-2"></v-progress-circular> -->
                                <span>{{ isLoading ? 'Resetting...' : 'Reset Password' }}</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.reset-password-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    padding: 20px;
}

.reset-password-container {
    display: flex;
    width: 100%;
    max-width: 1200px;
    min-height: 600px;
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.reset-password-left {
    flex: 1;
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    color: white;
    padding: 40px;
    display: flex;
    flex-direction: column;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.logo {
    width: 120px;
    height: 120px;
    object-fit: contain;
}

.app-name {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
}

.app-description {
    font-size: 16px;
    opacity: 0.9;
}

.login-features {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.feature {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.feature-icon {
    background-color: rgba(255, 255, 255, 0.25);
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.feature-icon :deep(.v-icon) {
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
}

.feature-text h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.feature-text p {
    font-size: 14px;
    opacity: 0.8;
}

.reset-password-right {
    flex: 1;
    padding: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.reset-password-form-container {
    width: 100%;
    max-width: 400px;
}

.reset-password-title {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

.reset-password-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 25px;
    line-height: 1.6;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #555;
    margin-bottom: 8px;
}

.input-with-icon {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 12px;
    color: #777;
}

.toggle-password {
    position: absolute;
    right: 12px;
    color: #777;
    cursor: pointer;
}

.input-with-icon input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.input-with-icon input:focus {
    outline: none;
    border-color: #2e7d32;
}

.input-with-icon input[readonly] {
    background-color: #f9f9f9;
    cursor: not-allowed;
}

.error-message {
    color: #d32f2f;
    font-size: 12px;
    margin-top: 5px;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 25px;
}

.back-to-login {
    font-size: 14px;
    color: #2e7d32;
    text-decoration: none;
}

.back-to-login:hover {
    text-decoration: underline;
}

.reset-button {
    padding: 10px 20px;
    background-color: #2e7d32;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.reset-button:hover {
    background-color: #1b5e20;
}

.reset-button:disabled {
    background-color: #9e9e9e;
    cursor: not-allowed;
}

@media (max-width: 900px) {
    .reset-password-container {
        flex-direction: column;
    }

    .reset-password-left {
        padding: 30px;
    }

    .login-features {
        display: none;
    }

    .reset-password-right {
        padding: 30px;
    }
}
</style>
