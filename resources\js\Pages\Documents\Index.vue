<template>
  <Head title="Documents" />
  <AppSidebarLayout>
    <v-container>
      <v-row>
        <v-col cols="12">
          <v-card elevation="2">
            <v-card-title class="">
              <!-- Search field (moved to leftmost position) -->
              <div class="d-flex align-center justify-space-between mt-4">
                <div style="width: 300px" class="mr-4">
                  <v-text-field
                    v-model="search"
                    append-icon="mdi-magnify"
                    label="Search documents..."
                    single-line
                    hide-details
                    variant="outlined"
                    density="compact"
                  ></v-text-field>
                </div>

                <v-spacer></v-spacer>

                <!-- Register New Document button (moved to middle-right) -->
                <Link
                  href="/documents/create"
                  class="mr-4 text-decoration-none"
                >
                  <v-btn color="blue-lighten-1" prepend-icon="mdi-plus">
                    Register New Document
                  </v-btn>
                </Link>

                <!-- Column visibility menu (moved to rightmost position) -->
                <div>
                  <v-menu
                    offset-y
                    v-model="columnMenuOpen"
                    :close-on-content-click="false"
                  >
                    <template v-slot:activator="{ props }">
                      <v-btn
                        color="primary"
                        v-bind="props"
                        prepend-icon="mdi-eye-settings"
                        variant="outlined"
                      >
                        <v-tooltip activator="parent" location="top">
                          Toggle column visibility
                        </v-tooltip>
                        Columns
                      </v-btn>
                    </template>
                    <v-card min-width="250" class="pa-2">
                      <v-card-title class="pb-1 text-subtitle-1"
                        >Toggle Columns</v-card-title
                      >
                      <v-divider></v-divider>
                      <v-card-text class="pt-2 pb-0 column-selector-menu">
                        <!-- Select All checkbox -->
                        <v-checkbox
                          v-model="allColumnsSelected"
                          hide-details
                          density="compact"
                          class="mb-2 column-checkbox font-weight-bold"
                          @click.stop
                        >
                          <template v-slot:label>
                            <div class="d-flex align-center">
                              <span>Select All</span>
                              <v-tooltip location="right">
                                <template v-slot:activator="{ props }">
                                  <v-icon
                                    v-bind="props"
                                    size="small"
                                    class="ml-1"
                                    >mdi-information-outline</v-icon
                                  >
                                </template>
                                Toggle all columns at once
                              </v-tooltip>
                            </div>
                          </template>
                        </v-checkbox>

                        <v-divider class="mb-2"></v-divider>

                        <!-- Individual column checkboxes (only for regular columns) -->
                        <v-checkbox
                          v-for="header in regularHeaders"
                          :key="header.key"
                          v-model="visibleColumns"
                          :value="header.key"
                          :label="header.title"
                          hide-details
                          density="compact"
                          @click.stop
                          class="my-1 column-checkbox"
                        ></v-checkbox>

                        <v-divider class="my-2"></v-divider>

                        <!-- Note about fixed columns -->
                        <div class="px-2 text-caption text-grey-darken-1">
                          <v-icon size="x-small" color="info" class="mr-1"
                            >mdi-information-outline</v-icon
                          >
                          QR Code, File, and Actions columns are always visible
                          at the end of the table.
                        </div>
                      </v-card-text>
                      <v-card-actions>
                        <v-btn
                          color="grey-darken-1"
                          variant="text"
                          size="small"
                          @click="resetColumns"
                        >
                          Reset All
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                          color="primary"
                          variant="text"
                          size="small"
                          @click="saveColumnPreferences"
                        >
                          <v-tooltip activator="parent" location="top">
                            Save column preferences for this session
                          </v-tooltip>
                          Save
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-menu>
                </div>
              </div>
              <div>
                <v-tabs v-model="selectedStatus" class="mr-4">
                  <v-tab
                    v-for="tab in statusTabs"
                    :key="tab.key"
                    :value="tab.key"
                  >
                    {{ tab.title }}
                  </v-tab>
                </v-tabs>
              </div>
            </v-card-title>
            <v-data-table
              :headers="visibleHeaders"
              :items="filteredItems"
              :search="search"
              :loading="loading"
              :sort-by="[{ key: sortBy, order: sortDesc ? 'desc' : 'asc' }]"
              :items-per-page-options="itemsPerPageOptions"
              :items-per-page="itemsPerPage"
              :page="currentPage"
              :server-items-length="props.documents.total"
              @update:options="handleTableOptions"
            >
              <!-- Custom header template for draggable columns -->
              <template v-slot:headers>
                <tr>
                  <!-- Regular columns (draggable) -->
                  <template
                    v-for="header in visibleHeaders.filter((h) => !h.fixed)"
                    :key="header.key"
                  >
                    <th
                      :class="[
                        'v-data-table__th',
                        header.sortable ? 'sortable' : '',
                        'text-start',
                      ]"
                      :style="{ width: header.width || 'auto' }"
                      @click="handleColumnSort(header)"
                    >
                      <div class="d-flex align-center">
                        <v-icon
                          size="small"
                          class="mr-2 cursor-move header-drag-handle"
                          color="grey-darken-1"
                          @mousedown.stop="startDrag"
                        >
                          mdi-drag
                        </v-icon>
                        <div class="column-title">{{ header.title }}</div>
                        <v-icon
                          v-if="sortBy === header.key"
                          size="small"
                          class="ml-1"
                        >
                          {{ sortDesc ? "mdi-arrow-down" : "mdi-arrow-up" }}
                        </v-icon>
                      </div>
                    </th>
                  </template>

                  <!-- Fixed columns (not draggable) -->
                  <th
                    v-for="header in visibleHeaders.filter((h) => h.fixed)"
                    :key="header.key"
                    :class="[
                      'v-data-table__th',
                      header.sortable ? 'sortable' : '',
                      'text-start',
                    ]"
                    :style="{ width: header.width || 'auto' }"
                    @click="header.sortable ? handleColumnSort(header) : null"
                  >
                    <div class="d-flex align-center">
                      {{ header.title }}
                      <v-icon
                        v-if="header.sortable && sortBy === header.key"
                        size="small"
                        class="ml-1"
                      >
                        {{ sortDesc ? "mdi-arrow-down" : "mdi-arrow-up" }}
                      </v-icon>
                    </div>
                  </th>
                </tr>
              </template>
              <template v-slot:item.status="{ item }">
                <v-chip :color="getStatusColor(item.status)" size="small">
                  {{ item.status }}
                </v-chip>
              </template>
              <template #item.date_received="{ item }">
                {{ new Date(item.date_received).toISOString().split("T")[0] }}
              </template>
              <template #item.time_received="{ item }">
                {{
                  new Date(item.date_received).toLocaleTimeString([], {
                    hour: "numeric",
                    minute: "2-digit",
                    hour12: true,
                  })
                }}
              </template>
              <template v-slot:item.qr_code_path="{ item }">
                <div class="justify-center d-flex">
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <div
                        v-if="item.qr_code_path"
                        class="status-indicator success"
                        v-bind="props"
                      >
                        <v-icon size="small" class="status-icon"
                          >mdi-check-bold</v-icon
                        >
                      </div>
                      <div v-else class="status-indicator error" v-bind="props">
                        <v-icon size="small" class="status-icon"
                          >mdi-close-thick</v-icon
                        >
                      </div>
                    </template>
                    {{ item.qr_code_path ? "QR Code Generated" : "No QR Code" }}
                  </v-tooltip>
                </div>
              </template>
              <template v-slot:item.file_path="{ item }">
                <div class="justify-center d-flex">
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <div
                        v-if="item.file_path"
                        class="status-indicator success"
                        v-bind="props"
                      >
                        <v-icon size="small" class="status-icon"
                          >mdi-check-bold</v-icon
                        >
                      </div>
                      <div v-else class="status-indicator error" v-bind="props">
                        <v-icon size="small" class="status-icon"
                          >mdi-close-thick</v-icon
                        >
                      </div>
                    </template>
                    {{
                      item.file_path ? "File Available" : "No File Available"
                    }}
                  </v-tooltip>
                </div>
              </template>
              <template v-slot:item.actions="{ item }">
                <div class="justify-center d-flex">
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <Link
                        :href="`/documents/${item.id}`"
                        class="mr-2 text-decoration-none"
                      >
                        <v-btn
                          icon
                          size="small"
                          color="blue-lighten-1"
                          v-bind="props"
                        >
                          <v-icon>mdi-eye</v-icon>
                        </v-btn>
                      </Link>
                    </template>
                    View
                  </v-tooltip>

                  <!-- <v-tooltip location="top">
                                        <template v-slot:activator="{ props }">
                                            <v-btn icon size="small" color="error" v-bind="props"
                                                @click="confirmDelete(item)">
                                                <v-icon>mdi-delete</v-icon>
                                            </v-btn>
                                        </template>
                                        Delete
                                    </v-tooltip> -->
                </div>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="deleteDialog" max-width="500">
      <v-card>
        <v-card-title class="text-white text-h5 bg-error"
          >Confirm Delete</v-card-title
        >
        <v-card-text class="pt-4">
          <p>Are you sure you want to delete this document?</p>
          <p v-if="documentToDelete" class="mt-2 font-weight-medium">
            Document: {{ documentToDelete.control_number }} -
            {{ documentToDelete.document_code }}
          </p>
          <p class="mt-4 text-error font-weight-bold">
            This action cannot be undone.
          </p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="deleteDialog = false"
          >
            Cancel
          </v-btn>
          <v-btn
            color="error"
            variant="text"
            @click="deleteDocument"
            :loading="deleting"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Column Saved Snackbar
        <v-snackbar v-model="showColumnSavedSnackbar" :timeout="3000" color="success">
            Column preferences saved successfully
            <template v-slot:actions>
                <v-btn variant="text" icon="mdi-close" @click="showColumnSavedSnackbar = false"></v-btn>
            </template>
        </v-snackbar> -->
  </AppSidebarLayout>
</template>

<script setup>
import { Head, Link, useForm } from "@inertiajs/vue3";
import { ref, computed, onMounted, watch } from "vue";
import { router } from "@inertiajs/vue3";
import AppSidebarLayout from "@/Layouts/AppSidebarLayout.vue";

const props = defineProps({
  documents: Object,
  preferences: Object,
});

// Computed property to ensure documents.data is always an array
const documentItems = computed(() => {
  if (!props.documents || !props.documents.data) {
    return [];
  }

  // Ensure we're working with an array
  return Array.isArray(props.documents.data)
    ? props.documents.data
    : Object.values(props.documents.data);
});

const search = ref("");
const loading = ref(false);
const deleting = ref(false);
const deleteDialog = ref(false);
const documentToDelete = ref(null);
const showColumnSavedSnackbar = ref(false);
const columnMenuOpen = ref(false);
const sortBy = ref("control_number"); // Default sort column
const sortDesc = ref(true); // Default sort direction
const currentPage = ref(1);
const itemsPerPage = ref(10);
const itemsPerPageOptions = [
  { title: "5", value: 5 },
  { title: "10", value: 10 },
  { title: "15", value: 15 },
  { title: "20", value: 20 },
  { title: "All", value: -1 },
];

// Define regular headers (can be reordered and toggled)
const regularHeaders = [
  {
    title: "Control Number",
    key: "control_number",
    width: "200px",
    sortable: true,
  },
  {
    title: "Reference",
    key: "reference_document",
    width: "100px",
    sortable: true,
  },
  { title: "Signatory", key: "signatory", sortable: true },
  {
    title: "Received By",
    key: "received_by.name",
    width: "150px",
    sortable: true,
  },
  { title: "Origin Office", key: "origin", sortable: true },
  {
    title: "Document Code",
    key: "document_code",
    width: "175px",
    sortable: true,
  },
  { title: "Subject Matter", key: "subject_matter", sortable: true },
  { title: "Referred To", key: "referred_to", sortable: true },
  {
    title: "Document Date",
    key: "date_received",
    width: "150px",
    sortable: true,
  },
  {
    title: "Time Received",
    key: "time_received",
    width: "150px",
    sortable: true,
  },
  { title: "Status", key: "status", width: "120px", sortable: true },
];

// Define fixed headers (always visible and in fixed order at the end)
const fixedHeaders = [
  {
    title: "QR Code",
    key: "qr_code_path",
    width: "120px",
    fixed: true,
    sortable: true,
  },
  {
    title: "File",
    key: "file_path",
    width: "120px",
    fixed: true,
    sortable: true,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    width: "150px",
    fixed: true,
  },
];

// We don't need to combine headers anymore as we're handling regular and fixed columns separately

// Default visible regular columns
const defaultRegularColumns = [
  "control_number",
  "origin",
  "reference_document",
  "received_by.name",
  "document_code",
  "date_received",
  "time_received",
  "status",
];

const statusTabs = [
  { title: "All", key: "all" },
  { title: "Pending", key: "pending" },
  { title: "In Progress", key: "in progress" },
  { title: "Completed", key: "completed" },
  { title: "Inactive", key: "inactive" },
];

const selectedStatus = ref("all");

const filteredItems = computed(() => {
  if (selectedStatus.value === "all") return documentItems.value;
  return documentItems.value.filter(
    (item) => item.status?.toLowerCase() === selectedStatus.value
  );
});

const jsonDecoder = (value) => {
  try {
    return JSON.parse(value);
  } catch (e) {
    console.error("Error parsing JSON:", e);
    return value; // Return original value if parsing fails
  }
};

//  For Debugging Only
// alert("Matching columns from DB:\n" + matchingKeys.join("\n"));

const regularHeaderKeys = regularHeaders.map((header) => header.key);
const preference_user = jsonDecoder(
  props.preferences?.document_column_arrangement || "[]"
);

// Filter matching keys
const matchingKeys = preference_user.filter((key) =>
  regularHeaderKeys.includes(key)
);

// Fixed column keys (always visible)
const fixedColumnKeys = fixedHeaders.map((header) => header.key);

// Track visible columns
const visibleColumns = ref([...matchingKeys]);

const form = useForm({
  key: "document_column_arrangement",
  value: JSON.stringify(visibleColumns.value), // stringified array
});

// Keep value updated when checkboxes change
watch(visibleColumns, (newVal) => {
  form.value = JSON.stringify(newVal);
});

const saveColumnPreferences = () => {
  form.post(route("preferences.store"), {
    onSuccess: () => {
      // Full page reload after saving
      window.location.reload();
    },
    onError: (err) => {
      console.error("Failed to save column preferences", err);
    },
  });
};
// Track column order
const columnOrder = ref([]);

// We don't need orderedHeaders anymore as we're using visibleHeaders directly

// Compute visible headers based on selection and order, with fixed columns always at the end
const visibleHeaders = computed(() => {
  // Get visible regular columns
  const visibleRegularColumns = regularHeaders.filter((header) =>
    visibleColumns.value.includes(header.key)
  );

  // Get visible fixed columns (should be all of them)
  const visibleFixedColumns = fixedHeaders;

  // If we have a saved order, use it to sort the regular headers
  if (columnOrder.value.length > 0) {
    const sortedRegularColumns = visibleRegularColumns.sort((a, b) => {
      const indexA = columnOrder.value.indexOf(a.key);
      const indexB = columnOrder.value.indexOf(b.key);

      // If a column is not in the order array, put it at the end of regular columns
      if (indexA === -1) return 1;
      if (indexB === -1) return -1;

      return indexA - indexB;
    });

    // Return sorted regular columns followed by fixed columns
    return [...sortedRegularColumns, ...visibleFixedColumns];
  }

  // If no saved order, return regular columns as is, followed by fixed columns
  return [...visibleRegularColumns, ...visibleFixedColumns];
});

// Compute whether all regular columns are selected
const allColumnsSelected = computed({
  get: () => {
    return regularHeaders.every((header) =>
      visibleColumns.value.includes(header.key)
    );
  },
  set: (value) => {
    if (value) {
      // Select all regular columns + fixed columns
      visibleColumns.value = [
        ...regularHeaders.map((header) => header.key),
        ...fixedColumnKeys,
      ];
    } else {
      // Deselect all regular columns but keep fixed columns
      visibleColumns.value = [...fixedColumnKeys];
    }
    // Keep the menu open
    columnMenuOpen.value = true;
  },
});

// Reset columns to default
const resetColumns = () => {
  // Reset to default regular columns + fixed columns
  visibleColumns.value = [...defaultRegularColumns, ...fixedColumnKeys];
  resetColumnOrder(); // Reset column order to match default visible columns
  columnMenuOpen.value = false; // Close the menu
  showColumnSavedSnackbar.value = true;
  setTimeout(() => {
    showColumnSavedSnackbar.value = false;
  }, 3000);
};

// Load saved column preferences from session storage if available
onMounted(() => {
  // Load visible columns
  const savedColumns = sessionStorage.getItem("documentTableColumns");
  if (savedColumns) {
    try {
      visibleColumns.value = JSON.parse(savedColumns);
    } catch (e) {
      console.error("Error loading saved columns", e);
      resetColumns();
    }
  }

  // Load column order
  const savedOrder = sessionStorage.getItem("documentTableOrder");
  if (savedOrder) {
    try {
      columnOrder.value = JSON.parse(savedOrder);
    } catch (e) {
      console.error("Error loading saved column order", e);
      resetColumnOrder();
    }
  } else {
    // Initialize column order with default visible columns
    resetColumnOrder();
  }
});

// Reset column order to match current visible columns with fixed columns at the end
const resetColumnOrder = () => {
  // Initialize with the current visible regular columns in their default order
  const orderedRegularColumns = [];

  // Add visible regular columns in the order they appear in regularHeaders
  regularHeaders.forEach((header) => {
    if (visibleColumns.value.includes(header.key)) {
      orderedRegularColumns.push(header.key);
    }
  });

  // Set column order with regular columns first, then fixed columns
  columnOrder.value = [...orderedRegularColumns, ...fixedColumnKeys];
};

/* ------------------------------------------------------------------------------------------------------------------------ */

// Handle column drag and drop
const startDrag = (e) => {
  // Get the index from the mousedown event target
  const dragHandle = e.target.closest(".header-drag-handle");
  if (!dragHandle) return;

  const headerElement = dragHandle.closest(".v-data-table__th");
  const allHeaders = Array.from(document.querySelectorAll(".v-data-table__th"));
  let index = allHeaders.indexOf(headerElement);

  // Get only the regular columns
  const regularColumns = visibleHeaders.value.filter((h) => !h.fixed);

  // Get the column being dragged
  const draggedColumn = regularColumns[index];

  // Get all header elements
  const elements = document.querySelectorAll(".v-data-table__th");

  // Get all table cells for column highlighting
  const table = document.querySelector(".v-data-table");
  const rows = table.querySelectorAll(".v-data-table__tr");

  // Get the source element
  const sourceElement = elements[index];

  // Create ghost element for preview
  const ghostElement = sourceElement.cloneNode(true);
  ghostElement.classList.add("ghost-header");
  ghostElement.style.width = `${sourceElement.offsetWidth}px`;
  ghostElement.style.height = `${sourceElement.offsetHeight}px`;
  document.body.appendChild(ghostElement);

  // Initial position of ghost element
  const initialRect = sourceElement.getBoundingClientRect();
  ghostElement.style.left = `${initialRect.left}px`;
  ghostElement.style.top = `${initialRect.top}px`;

  // Add dragging class to source element
  sourceElement.classList.add("dragging");

  // Highlight the entire column being dragged
  highlightColumn(index);

  // Track if we've made a change
  let hasChanged = false;
  let initialX = e.clientX;

  // Create a drag event handler
  const handleDrag = (e) => {
    // Update ghost element position
    ghostElement.style.left = `${e.clientX - ghostElement.offsetWidth / 2}px`;
    ghostElement.style.top = `${e.clientY - ghostElement.offsetHeight / 2}px`;

    // Find the column under the cursor
    let targetIndex = -1;
    let targetElement = null;
    let dragDirection = e.clientX > initialX ? "right" : "left";

    // Update initial X for next comparison
    initialX = e.clientX;

    elements.forEach((el) => {
      // Remove all drag-related classes
      el.classList.remove("drag-over", "drag-left", "drag-right");

      const rect = el.getBoundingClientRect();
      if (
        e.clientX >= rect.left &&
        e.clientX <= rect.right &&
        e.clientY >= rect.top &&
        e.clientY <= rect.bottom
      ) {
        // Check if this is a regular column (not a fixed column)
        const headerIndex = Array.from(elements).indexOf(el);
        if (headerIndex < regularColumns.length && headerIndex !== index) {
          targetIndex = headerIndex;
          targetElement = el;
        }
      }
    });

    // Add drag-over class to target element with direction indicator
    if (targetElement) {
      targetElement.classList.add("drag-over");
      targetElement.classList.add(`drag-${dragDirection}`);
    }

    // If we found a valid target and it's different from the source
    if (targetIndex !== -1 && targetIndex !== index) {
      // Get only the regular columns from the current order (exclude fixed columns)
      const regularColumnsOrder = columnOrder.value.filter(
        (col) => !fixedColumnKeys.includes(col)
      );

      // Get the key that was moved
      const movedKey = draggedColumn.key;

      // Get the current index in the order array (not the DOM index)
      const currentOrderIndex = regularColumnsOrder.indexOf(movedKey);

      // Remove the key from its old position
      regularColumnsOrder.splice(currentOrderIndex, 1);

      // Get the target key
      const targetKey = regularColumns[targetIndex].key;
      const targetOrderIndex = regularColumnsOrder.indexOf(targetKey);

      // Insert the key at its new position
      // If dragging right, insert after the target
      if (targetIndex > index) {
        regularColumnsOrder.splice(targetOrderIndex + 1, 0, movedKey);
      } else {
        // If dragging left, insert before the target
        regularColumnsOrder.splice(targetOrderIndex, 0, movedKey);
      }

      // Update the column order with regular columns first, then fixed columns
      columnOrder.value = [...regularColumnsOrder, ...fixedColumnKeys];

      // Update the index for the next drag event and highlight the new column
      removeColumnHighlight();
      index = targetIndex;
      highlightColumn(index);

      // Mark that we've made a change
      hasChanged = true;
    }
  };

  // Function to highlight the entire column
  function highlightColumn(colIndex) {
    // First, remove any existing highlights
    removeColumnHighlight();

    // Add highlight to the header
    if (elements[colIndex]) {
      elements[colIndex].classList.add("dragging");
    }

    // Add highlight to all cells in this column
    rows.forEach((row) => {
      const cells = row.querySelectorAll(".v-data-table__td");
      if (cells[colIndex]) {
        cells[colIndex].classList.add("column-highlight");
      }
    });
  }

  // Function to remove column highlighting
  function removeColumnHighlight() {
    // Remove highlight from all headers
    elements.forEach((el) => {
      el.classList.remove("dragging");
    });

    // Remove highlight from all cells
    document.querySelectorAll(".column-highlight").forEach((cell) => {
      cell.classList.remove("column-highlight");
    });
  }

  // Create a mouseup handler to clean up
  const handleMouseUp = () => {
    // Remove ghost element
    if (ghostElement && ghostElement.parentNode) {
      document.body.removeChild(ghostElement);
    }

    // Remove column highlighting
    removeColumnHighlight();

    // Remove drag-over classes
    elements.forEach((el) => {
      el.classList.remove("drag-over", "drag-left", "drag-right");
    });

    // If we made a change, save it with animation
    if (hasChanged) {
      // Add a transition class to animate the reordering
      table.classList.add("reordering");

      // Save the new order
      sessionStorage.setItem(
        "documentTableOrder",
        JSON.stringify(columnOrder.value)
      );

      // Show success message
      showColumnSavedSnackbar.value = true;
      setTimeout(() => {
        showColumnSavedSnackbar.value = false;
        // Remove the transition class after animation completes
        table.classList.remove("reordering");
      }, 3000);
    }

    // Remove event listeners
    document.removeEventListener("mousemove", handleDrag);
    document.removeEventListener("mouseup", handleMouseUp);
  };

  // Add event listeners
  document.addEventListener("mousemove", handleDrag);
  document.addEventListener("mouseup", handleMouseUp);
};

// We don't need to watch for changes to visible columns anymore
// as we're explicitly saving when the user clicks the Save button
// This prevents the menu from closing when selecting columns

// Handle column sorting
const handleColumnSort = (header) => {
  // Only sort if the column is sortable
  if (header.sortable !== false) {
    // If clicking the same column, toggle sort direction
    if (sortBy.value === header.key) {
      sortDesc.value = !sortDesc.value;
    } else {
      // If clicking a different column, sort ascending by that column
      sortBy.value = header.key;
      sortDesc.value = false;
    }
  }
};

const getStatusColor = (status) => {
  switch (status.toLowerCase()) {
    case "pending":
      return "warning";
    case "in progress":
      return "info";
    case "completed":
      return "success";
    case "rejected":
      return "error";
    case "inactive":
      return "black";
    default:
      return "grey";
  }
};

// Function to confirm deletion and open the dialog
const confirmDelete = (item) => {
  documentToDelete.value = item;
  deleteDialog.value = true;
};

// Function to delete the document
const deleteDocument = () => {
  if (documentToDelete.value) {
    deleting.value = true;
    router.delete(`/documents/${documentToDelete.value.id}`, {
      onSuccess: () => {
        deleteDialog.value = false;
        documentToDelete.value = null;
        deleting.value = false;
      },
      onError: () => {
        deleting.value = false;
      },
    });
  }
};

// Handle table options changes (pagination, sorting, etc.)
const handleTableOptions = async (options) => {
  const newPage = options.page || 1;
  const perPage = options.itemsPerPage || 10;
  const sortBy = options.sortBy?.[0]?.key || "created_at";
  const sortDesc = options.sortBy?.[0]?.order === "desc";

  currentPage.value = newPage;
  itemsPerPage.value = perPage;
  loading.value = true;

  try {
    const response = await axios.get("/documents", {
      params: {
        page: newPage,
        per_page: perPage,
        sort_by: sortBy,
        sort_desc: sortDesc,
      },
    });

    documentItems.value = response.data.data; // Laravel paginate() gives this
    totalItems.value = response.data.total; // Total count of records
  } catch (err) {
    console.error("Failed to load documents:", err);
  } finally {
    loading.value = false;
  }
};

// Function to change page
const changePage = (page) => {
  loading.value = true;

  // Build the URL with pagination parameters
  let url = `/documents?page=${page}`;
  if (itemsPerPage.value !== 10) {
    url += `&per_page=${itemsPerPage.value}`;
  }

  router.visit(url, {
    preserveState: true,
    onSuccess: () => {
      loading.value = false;
      currentPage.value = page;
    },
    onError: () => {
      loading.value = false;
    },
  });
};
</script>

<style scoped>
:deep(.v-data-table-header th) {
  font-weight: bold;
  color: rgba(0, 0, 0, 0.87) !important;
}

:deep(.v-data-table .v-data-table__td) {
  color: rgba(0, 0, 0, 0.87) !important;
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

.column-selector-menu {
  max-height: 400px;
  overflow-y: auto;
}

.column-checkbox {
  margin-bottom: 4px;
}

.font-weight-bold {
  font-weight: bold !important;
}

/* Remove blue focus outline from checkboxes */
:deep(.v-checkbox .v-selection-control__input:focus-visible) {
  outline: none !important;
}

/* Draggable column styles */
.header-drag-handle {
  cursor: grab;
  border-radius: 4px;
  padding: 2px;
  transition: all 0.2s ease;
}

.header-drag-handle:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.2);
}

.header-drag-handle:active {
  cursor: grabbing;
}

.ghost-header {
  opacity: 0.7;
  background: #c8ebfb;
  border: 2px dashed #2196f3;
  border-radius: 4px;
  pointer-events: none;
  z-index: 100;
  position: absolute;
}

.cursor-move {
  cursor: move;
}

.column-drag-handle {
  cursor: grab;
}

/* Styles for column dragging */
.v-data-table__th {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);
  position: relative;
  overflow: visible;
}

.v-data-table__th.dragging {
  background-color: #e3f2fd !important;
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
  opacity: 0.9;
  z-index: 10;
  transform: scale(1.02);
}

.v-data-table__th.drag-over {
  background-color: #bbdefb !important;
  position: relative;
}

.v-data-table__th.drag-over::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #2196f3;
  z-index: 5;
}

.v-data-table__th.drag-over.drag-right::before {
  right: 0;
}

.v-data-table__th.drag-over.drag-left::before {
  left: 0;
}

/* Highlight entire column when dragging */
:deep(.column-highlight) {
  background-color: rgba(33, 150, 243, 0.05) !important;
  transition: background-color 0.3s ease;
}

/* Column title styling */
.column-title {
  cursor: pointer;
}

/* Make sure the entire header is clickable for sorting */
.v-data-table__th.sortable {
  cursor: pointer;
}

/* Improve the appearance of the sort indicators */
.v-data-table__th .v-icon {
  transition: transform 0.2s;
}

/* Animation for column reordering */
:deep(.v-data-table.reordering .v-data-table__th),
:deep(.v-data-table.reordering .v-data-table__td) {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.5, 1) !important;
}

/* Cross-browser compatibility */
@supports (-webkit-appearance: none) {
  /* Safari and Chrome specific styles */
  .header-drag-handle {
    -webkit-user-drag: none;
    -webkit-user-select: none;
  }

  .ghost-header {
    -webkit-box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

@supports (-moz-appearance: none) {
  /* Firefox specific styles */
  .header-drag-handle {
    -moz-user-select: none;
  }
}

/* Responsive styles for different screen sizes */
@media (max-width: 768px) {
  .header-drag-handle {
    padding: 4px;
  }

  .header-drag-handle:hover {
    transform: scale(1.3);
    /* Slightly larger on mobile for easier targeting */
  }
}

/* Action button styling */
:deep(.v-btn.v-btn--icon) {
  transition: transform 0.2s ease, background-color 0.2s ease;
}

:deep(.v-btn.v-btn--icon:hover) {
  transform: translateY(-2px);
  background-color: rgba(0, 0, 0, 0.05);
}

:deep(.v-btn.v-btn--icon:active) {
  transform: translateY(0);
}

/* Ensure consistent vertical alignment in action cells */
:deep(.v-data-table__td .d-flex.justify-center) {
  min-height: 36px;
  align-items: center;
}

/* Ensure proper spacing between action buttons */
:deep(.v-btn.v-btn--icon.mr-2) {
  margin-right: 8px !important;
}

/* Ensure consistent icon sizing */
:deep(.v-btn.v-btn--icon .v-icon) {
  font-size: 18px;
}

/* Status indicator styling */
.status-indicator {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.status-indicator.success {
  background-color: rgba(76, 175, 80, 0.15);
  border: 2px solid #4caf50;
}

.status-indicator.error {
  background-color: rgba(244, 67, 54, 0.15);
  border: 2px solid #f44336;
}

.status-indicator:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Status icon styling */
:deep(.status-icon) {
  font-size: 16px;
  font-weight: bold;
}

.status-indicator.success :deep(.status-icon) {
  color: #4caf50;
}

.status-indicator.error :deep(.status-icon) {
  color: #f44336;
}
</style>
