<template>

    <Head title="Generate QR Code" />
    <AppSidebarLayout>
        <v-container>
            <v-row>
                <v-col cols="12">
                    <h1 class="mb-4 text-h4">Generate QR Code</h1>
                    <p class="mb-6 text-subtitle-1">
                        Generate a QR code for document: <strong>{{ document.control_number }}</strong>
                    </p>
                </v-col>
            </v-row>

            <v-row>
                <v-col cols="12" md="6">
                    <v-card elevation="2">
                        <v-card-title class="text-white bg-primary">Document Information</v-card-title>
                        <v-card-text>
                            <v-list>
                                <v-list-item>
                                    <template v-slot:prepend>
                                        <v-icon icon="mdi-file-document" color="primary"></v-icon>
                                    </template>
                                    <div>
                                        <div class="text-subtitle-1 font-weight-medium">Control Number</div>
                                        <div class="text-body-2">{{ document.control_number }}</div>
                                    </div>
                                </v-list-item>

                                <v-list-item>
                                    <template v-slot:prepend>
                                        <v-icon icon="mdi-account" color="primary"></v-icon>
                                    </template>
                                    <div>
                                        <div class="text-subtitle-1 font-weight-medium">Signatory</div>
                                        <div class="text-body-2">{{ document.signatory }}</div>
                                    </div>
                                </v-list-item>

                                <v-list-item>
                                    <template v-slot:prepend>
                                        <v-icon icon="mdi-office-building" color="primary"></v-icon>
                                    </template>
                                    <div>
                                        <div class="text-subtitle-1 font-weight-medium">Origin</div>
                                        <div class="text-body-2">{{ document.origin }}</div>
                                    </div>
                                </v-list-item>

                                <v-list-item>
                                    <template v-slot:prepend>
                                        <v-icon icon="mdi-information" color="primary"></v-icon>
                                    </template>
                                    <div>
                                        <div class="text-subtitle-1 font-weight-medium">Subject Matter</div>
                                        <div class="text-body-2">{{ document.subject_matter }}</div>
                                    </div>
                                </v-list-item>
                            </v-list>
                        </v-card-text>
                    </v-card>
                </v-col>

                <v-col cols="12" md="6">
                    <v-card elevation="2">
                        <v-card-title class="mb-4 text-white bg-primary">QR Code</v-card-title>
                        <v-card-text class="d-flex flex-column align-center">
                            <div v-if="qrCodeUrl" class="mb-4 qr-code-container">
                                <img v-if="!qrCodeUrl.endsWith('.svg')" :src="qrCodeUrl" alt="QR Code"
                                    class="qr-code" />
                                <object v-else :data="qrCodeUrl" type="image/svg+xml" class="qr-code"></object>
                                <div class="mt-2 text-caption">QR URL: {{ qrCodeUrl }}</div>
                            </div>
                            <div v-else class="mb-4 text-center">
                                <!-- <v-progress-circular v-if="generating" indeterminate
                                    color="primary"></v-progress-circular> -->
                                <p>Click the button below to generate the QR code</p>
                            </div>

                            <v-btn v-if="!qrCodeUrl" color="primary" @click="generateQrCode" :loading="generating"
                                :disabled="generating">
                                Generate QR Code
                            </v-btn>

                            <div v-if="qrCodeUrl" class="mt-4 d-flex flex-column align-center">
                                <v-btn color="primary" @click="printQrCode" class="mb-2" prepend-icon="mdi-printer">
                                    Print QR Code
                                </v-btn>
                                <v-btn color="secondary" @click="goToDocumentsTable">
                                    Proceed to Table
                                </v-btn>
                            </div>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>

            <v-row class="mt-6">
                <v-col cols="12">
                    <v-card elevation="2">
                        <v-card-title class="mb-4 text-white bg-primary">Instructions</v-card-title>
                        <v-card-text>
                            <div v-if="qrCodeUrl">
                                <ol class="ml-4">
                                    <li class="mb-2">Click the <strong>Print QR Code</strong> button above.</li>
                                    <li class="mb-2">Place your document in the printer.</li>
                                    <li class="mb-2">The QR code will be printed in the top-right corner of the
                                        document.</li>
                                    <li class="mb-2">After printing, scan the document with the QR code.</li>
                                    <li class="mb-2">Click <strong>Proceed to Table</strong> to return to the document
                                        table.</li>
                                </ol>
                            </div>
                            <div v-else>
                                <ol class="ml-4">
                                    <li class="mb-2">Click the <strong>Generate QR Code</strong> button in the QR Code
                                        section.</li>
                                    <li class="mb-2">Once generated, you'll see options to print the QR code.</li>
                                    <li class="mb-2">The QR code will be attached to the top-right corner of your
                                        document.</li>
                                    <li class="mb-2">This QR code will be used for tracking the document throughout its
                                        lifecycle.
                                    </li>
                                </ol>
                            </div>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>

        <!-- Hidden iframe for printing -->
        <iframe v-if="qrCodeUrl" ref="printFrame" :src="printUrl" style="display: none;"></iframe>
    </AppSidebarLayout>
</template>

<script setup>
import { Head, router } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

import axios from 'axios';

const props = defineProps({
    document: Object,
});

const qrCodeUrl = ref(null);
const generating = ref(false);
const printFrame = ref(null);

// Function to navigate to the documents table
const goToDocumentsTable = () => {
    router.visit('/documents');
};

const printUrl = computed(() => {
    if (!qrCodeUrl.value) return '';
    return `/print-qr-code?url=${encodeURIComponent(qrCodeUrl.value)}`;
});

const generateQrCode = async () => {
    generating.value = true;
    try {
        const response = await axios.post(`/documents/${props.document.id}/generate-qr`);
        qrCodeUrl.value = response.data.qr_code_url;
    } catch (error) {
        console.error('Error generating QR code:', error);
        alert('Error generating QR code: ' + (error.response?.data?.error || 'Unknown error'));
    } finally {
        generating.value = false;
    }
};


const printQrCode = () => {
    if (printFrame.value) {
        printFrame.value.contentWindow.print();
    } else {
        // Fallback if iframe is not available
        const printWindow = window.open(printUrl.value, '_blank');
        printWindow.addEventListener('load', () => {
            printWindow.print();
        });
    }
};
</script>

<style scoped>
.qr-code-container {
    background-color: transparent;
    padding: 20px;
    border-radius: 8px;
    display: inline-block;
}

.qr-code {
    max-width: 200px;
    height: auto;
}

.v-list-item {
    margin-bottom: 8px;
}

.text-subtitle-1.font-weight-medium {
    color: rgba(0, 0, 0, 0.87);
    font-weight: 500;
}

.text-body-2 {
    color: rgba(0, 0, 0, 0.6);
}
</style>
