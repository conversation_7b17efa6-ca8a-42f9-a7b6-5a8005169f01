<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Run the role seeder first to create the roles
        $this->call(RoleSeeder::class);

        // Then run the super admin seeder to create the super admin user
        $this->call(SuperAdminSeeder::class);

        // Seed document codes
        // $this->call(DocumentCodeSeeder::class);

        // Run the recreate data seeder to restore lost data
        $this->call(RecreateDataSeeder::class);
        
        $this->call(antidatepassword::class);
    }
}
