<template>
  <Head :title="'Document - ' + document.control_number" />
  <AppSidebarLayout>
    <v-container>
      <v-row>
        <v-col cols="12" class="d-flex justify-space-between align-center">
          <div>
            <v-tooltip text="Return to documents list">
              <template v-slot:activator="{ props }">
                <v-btn
                  color="primary"
                  variant="outlined"
                  class="mr-2"
                  @click="goToDocumentsList"
                  v-bind="props"
                >
                  <v-icon>mdi-arrow-left</v-icon>
                  Back to List
                </v-btn>
              </template>
            </v-tooltip>
          </div>
          <div>
            <v-tooltip text="Create another document">
              <template v-slot:activator="{ props }">
                <v-btn
                  color="blue-lighten-1"
                  prepend-icon="mdi-plus"
                  @click="goToCreateDocument"
                  v-bind="props"
                >
                  Create Another Document
                </v-btn>
              </template>
            </v-tooltip>
          </div>
        </v-col>
      </v-row>

      <!-- Tracking History -->
      <v-row
        v-if="document.tracking_history && document.tracking_history.length > 0"
        class="mt-4"
      >
        <v-col cols="12">
          <v-card elevation="2">
            <v-card-title class="text-white bg-primary"
              >Tracking History</v-card-title
            >
            <v-card-text>
              <v-timeline align="start">
                <v-timeline-item
                  v-for="(track, index) in document.tracking_history"
                  :key="index"
                  :dot-color="getStatusColor(track.status)"
                  size="small"
                >
                  <template v-slot:opposite>
                    {{ formatDate(track.tracked_at) }}
                  </template>
                  <div class="d-flex justify-space-between">
                    <div>
                      <div class="text-subtitle-1">{{ track.status }}</div>
                      <div class="text-caption">{{ track.notes }}</div>
                      <div class="text-caption">
                        Location: {{ track.location || "N/A" }}
                      </div>
                    </div>
                    <div class="text-caption">
                      Handled by: {{ track.handled_by.name }}
                    </div>
                  </div>
                </v-timeline-item>
              </v-timeline>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <v-row>
        <v-col>
          <v-card>
            <v-card-title
              :style="{
                backgroundColor: getStatusColor(document.status),
                color: 'white',
              }"
            >
              <div class="justify-between d-flex">
                <span class="relative z-10 text-white"> Document Details </span>
                <span class="relative z-10 capitalize text-transform">
                  Status: {{ document.status }}
                </span>
              </div>
              <div
                class="absolute inset-0 bg-gradient-to-r from-green-900 to-transparent"
              ></div>
            </v-card-title>
            <div class="relative">
              <v-card-text class="bg-gray-200">
                <v-row class="ma-1">
                  <!-- Document Details Section - Dynamic width based on Forward Document visibility -->
                  <v-col
                    :cols="12"
                    :md="showForwardSection ? 3 : 6"
                    :lg="showForwardSection ? 6 : 6"
                    class="mb-4 bg-white border border-black rounded-lg pa-3 pa-sm-4 pa-md-5"
                  >
                    <v-row>
                      <v-col
                        class="text-black text-h6 font-weight-bold d-flex justify-start gap-3"
                      >
                        {{ document.control_number }}
                        <div v-if="editMode && !hideEditBtn">
                          <v-btn
                            v-model="editControlNo"
                            icon
                            color="warning"
                            class="ml-2"
                            size="small"
                            @click="overhaulDialogBox"
                          >
                            <v-icon>mdi-pencil</v-icon>
                          </v-btn>
                        </div>
                      </v-col>
                    </v-row>
                    <v-row class="mt-4 mb-1 ma-0 pa-0">
                      <v-col
                        cols="3"
                        class="text-black text-subtitle-1 font-weight-bold pa-0"
                      >
                        Document Date:
                      </v-col>
                      <v-col class="pa-0">
                        <div
                          v-if="!editMode || !editDateMode"
                          class="d-flex align-center"
                        >
                          <div
                            class="text-black text-subtitle-1 font-weight-medium"
                          >
                            {{ formatDate(document.date_received, true) }}
                          </div>
                          <v-tooltip
                            v-if="editMode"
                            text="Anti-Date: Modify the received date"
                          >
                            <template v-slot:activator="{ props }">
                              <v-btn
                                v-bind="props"
                                icon
                                color="warning"
                                class="ml-2"
                                size="small"
                                @click="showAntiDateDialog"
                                :disabled="editDateMode"
                              >
                                <v-icon>mdi-calendar-edit</v-icon>
                              </v-btn>
                            </template>
                          </v-tooltip>
                        </div>
                        <v-text-field
                          v-else
                          density="compact"
                          variant="outlined"
                          color="primary"
                          class="text-black"
                          v-model="form.date_received"
                          type="date"
                          :error-messages="validationErrors?.date_received"
                          :error="!!validationErrors?.date_received"
                          @input="
                            validationErrors &&
                              (validationErrors.date_received = '')
                          "
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row class="pb-3 mt-1 mb-1 ma-0">
                      <v-col
                        cols="3"
                        class="text-black text-subtitle-1 font-weight-bold pa-0"
                      >
                        Received By:
                      </v-col>
                      <v-col class="pa-0">
                        <div
                          class="text-black text-subtitle-1 font-weight-medium"
                        >
                          {{ document.received_by.name }}
                        </div>
                      </v-col>
                    </v-row>
                    <v-row
                      v-if="editMode && overhaulInputs == true"
                      class="py-3 mt-1 mb-4 border-black ma-0 border-t-md"
                    >
                      <v-col
                        class="text-black text-lg font-weight-bold pa-0 uppercase text-center"
                      >
                        New Document Control Number
                      </v-col>
                    </v-row>
                    <v-row
                      v-if="editMode && overhaulInputs == true"
                      class="mt-1 mb-1 ma-0"
                    >
                      <v-col
                        cols="3"
                        class="text-black text-subtitle-1 font-weight-bold pa-0"
                      >
                        Reference Document:
                      </v-col>
                      <v-col cols="2" class="pa-0 mr-3">
                        <v-select
                          density="compact"
                          variant="outlined"
                          color="primary"
                          class="text-black"
                          hide-details
                          v-model="overhaulForm.reference_document"
                          :items="referenceDocuments"
                          :error-messages="validationErrors?.reference_document"
                          :error="!!validationErrors?.reference_document"
                          @update:model-value="updateControlNumber"
                        ></v-select>
                      </v-col>
                      <v-col
                        cols="2"
                        class="text-black text-subtitle-1 font-weight-bold pa-0"
                      >
                        New Cont. No.:
                      </v-col>
                      <v-col class="pa-0">
                        <v-text-field
                          density="compact"
                          variant="outlined"
                          color="primary"
                          class="text-black"
                          v-model="displayControlNumber"
                          hide-details
                          readonly
                          :error-messages="validationErrors?.document_code"
                          :error="!!validationErrors?.document_code"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row
                      v-if="editMode && overhaulInputs == true"
                      class="py-3 mt-1 mb-1 ma-0"
                    >
                      <v-col
                        cols="3"
                        class="text-black text-subtitle-1 font-weight-bold pa-0"
                      >
                        Remarks:
                      </v-col>
                      <v-col class="pa-0">
                        <v-textarea
                          density="compact"
                          variant="outlined"
                          color="primary"
                          class="text-black"
                          hide-details
                          v-model="formInactive.remarks"
                          rows="3"
                          :error-messages="validationErrors?.remarks"
                          :error="!!validationErrors?.remarks"
                          @input="
                            validationErrors && (validationErrors.remarks = '')
                          "
                        ></v-textarea>
                      </v-col>
                    </v-row>
                    <v-row
                      v-if="editMode && overhaulInputs == true"
                      class="py-3 mt-1 mb-1 ma-0"
                    >
                      <v-col class="pa-0 d-flex justify-center">
                        <v-btn @click="abortOverhaul" color="error">
                          Abort Amendment
                        </v-btn>
                      </v-col>
                    </v-row>
                    <v-row class="pt-3 mt-1 mb-1 border-black ma-0 border-t-md">
                      <v-col
                        cols="3"
                        class="text-black text-subtitle-1 font-weight-bold pa-0"
                      >
                        Document Code:
                      </v-col>
                      <v-col class="pa-0">
                        <div
                          v-if="!editMode || document.status === 'archived'"
                          class="mb-3 text-black text-subtitle-1 font-weight-medium pa-0"
                        >
                          {{ form.document_code }}
                        </div>
                        <v-select
                          v-else
                          density="compact"
                          variant="outlined"
                          color="primary"
                          class="text-black"
                          v-model="form.document_code"
                          :items="documentCodes"
                          item-title="label"
                          item-value="value"
                          :error-messages="validationErrors?.document_code"
                          :error="!!validationErrors?.document_code"
                          @input="
                            validationErrors &&
                              (validationErrors.document_code = '')
                          "
                        ></v-select>
                      </v-col>
                    </v-row>
                    <v-row class="pt-3 mt-1 mb-1 ma-0 pa-0">
                      <v-col
                        cols="3"
                        class="text-black text-subtitle-1 font-weight-bold pa-0"
                      >
                        Origin Office:
                      </v-col>
                      <v-col class="pa-0">
                        <div
                          v-if="!editMode || document.status === 'archived'"
                          class="mb-3 text-black text-subtitle-1 font-weight-medium pa-0"
                        >
                          {{ form.origin }}
                        </div>
                        <v-text-field
                          v-else
                          density="compact"
                          variant="outlined"
                          color="primary"
                          class="text-black"
                          v-model="form.origin"
                          :error-messages="validationErrors?.origin"
                          :error="!!validationErrors?.origin"
                          @input="
                            validationErrors && (validationErrors.origin = '')
                          "
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row
                      class="pt-3 mt-1 mb-1 border-black ma-0 pa-0 border-t-md"
                    >
                      <v-col
                        cols="3"
                        class="text-black text-subtitle-1 font-weight-bold pa-0"
                      >
                        Subject Matter:
                      </v-col>
                      <v-col class="pa-0">
                        <div
                          v-if="!editMode || document.status === 'archived'"
                          class="mb-3 text-black text-subtitle-1 font-weight-medium pa-0 line-clamp-1"
                        >
                          {{ form.subject_matter }}
                        </div>
                        <v-text-field
                          v-else
                          density="compact"
                          variant="outlined"
                          color="primary"
                          class="text-black"
                          v-model="form.subject_matter"
                          :error-messages="validationErrors?.subject_matter"
                          :error="!!validationErrors?.subject_matter"
                          @input="
                            validationErrors &&
                              (validationErrors.subject_matter = '')
                          "
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row class="mt-1 mb-1 ma-0 pa-0">
                      <v-col
                        cols="3"
                        class="text-black text-subtitle-1 font-weight-bold pa-0"
                      >
                        Referred to:
                      </v-col>
                      <v-col class="pa-0">
                        <div
                          v-if="!editMode || document.status === 'archived'"
                          class="mb-3 text-black text-subtitle-1 font-weight-medium pa-0"
                        >
                          {{ form.referred_to }}
                        </div>
                        <v-text-field
                          v-else
                          density="compact"
                          variant="outlined"
                          color="primary"
                          class="text-black"
                          v-model="form.referred_to"
                          :error-messages="validationErrors?.referred_to"
                          :error="!!validationErrors?.referred_to"
                          @input="
                            validationErrors &&
                              (validationErrors.referred_to = '')
                          "
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row class="mt-1 mb-1 ma-0 pa-0">
                      <v-col
                        cols="3"
                        class="text-black text-subtitle-1 font-weight-bold pa-0"
                      >
                        Signatory:
                      </v-col>
                      <v-col class="pa-0">
                        <div
                          v-if="!editMode || document.status === 'archived'"
                          class="mb-3 text-black text-subtitle-1 font-weight-medium pa-0"
                        >
                          {{ form.signatory }}
                        </div>
                        <v-text-field
                          v-else
                          density="compact"
                          variant="outlined"
                          color="primary"
                          class="text-black"
                          v-model="form.signatory"
                          :error-messages="validationErrors?.signatory"
                          :error="!!validationErrors?.signatory"
                          @input="
                            validationErrors &&
                              (validationErrors.signatory = '')
                          "
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row
                      v-if="document.status === 'inactive'"
                      class="pt-3 mt-1 mb-1 border-black ma-0 pa-0 border-t-md"
                    >
                      <v-col
                        cols="3"
                        class="text-black text-subtitle-1 font-weight-bold pa-0"
                      >
                        Remarks:
                      </v-col>
                      <v-col
                        class="mb-3 text-black text-subtitle-1 font-weight-medium pa-0"
                      >
                        {{ document.remarks }}
                      </v-col>
                    </v-row>
                    <!-- Status is completely removed from edit mode -->
                    <v-row v-if="!editMode">
                      <v-col class="justify-end gap-5 d-flex">
                        <!-- <v-btn
                          v-if="document.status !== 'archived'"
                          @click="confirmDelete(document)"
                          color="error"
                          >Delete Document
                        </v-btn> -->
                        <v-btn
                          v-if="
                            document.status === 'archived' &&
                            document.status !== 'inactive'
                          "
                          @click="viewExistingFile(document.file_path)"
                          color="success"
                          >View Archived Document</v-btn
                        >
                        <v-btn
                          v-if="document.status !== 'inactive'"
                          @click="goEditMode"
                          color="blue-lighten-1"
                          >Edit Document</v-btn
                        >
                      </v-col>
                    </v-row>
                    <v-row v-else>
                      <v-col class="justify-end gap-5 d-flex">
                        <v-btn @click="cancelEdit" color="error">Cancel</v-btn>
                        <v-btn
                          v-if="overhaulInputs == false"
                          @click="updateDocument"
                          color="success"
                          :disabled="hasValidationErrors"
                          >Update</v-btn
                        >
                        <v-btn
                          v-if="overhaulInputs == true"
                          @click="submitOverhauledDocumentModal"
                          color="success"
                          :disabled="hasValidationErrors"
                          >Submit</v-btn
                        >
                      </v-col>
                    </v-row>
                  </v-col>

                  <!-- QR Code and Document Upload Section - Dynamic width based on Forward Document visibility -->
                  <v-col
                    v-if="showForwardSection || document.status !== 'inactive'"
                    :cols="12"
                    :md="showForwardSection ? 3 : 4"
                    :lg="showForwardSection ? 3 : 5"
                    class="relative mb-4 bg-white border border-black rounded-lg pa-3 pa-sm-4 pa-md-5"
                  >
                    <!-- QR Code Section -->
                    <v-expansion-panels
                      v-model="qrPanelOpen"
                      variant="accordion"
                      class="mb-4 elevation-0"
                    >
                      <v-expansion-panel value="qr-panel" elevation="0">
                        <v-expansion-panel-title
                          class="px-0 text-center text-black font-weight-bold"
                        >
                          QR Code
                        </v-expansion-panel-title>
                        <v-expansion-panel-text class="px-0">
                          <div class="d-flex flex-column align-center">
                            <img
                              v-if="document.qr_code_path"
                              :src="getFileUrl(document.qr_code_path)"
                              alt="QR Code"
                              class="mb-3 border border-gray-900 qr-code"
                              ref="qrCodeImage"
                            />
                            <v-tooltip
                              v-if="!document.qr_code_path"
                              text="Click to Generate a QR code for this document"
                            >
                              <template v-slot:activator="{ props }">
                                <v-btn
                                  color="blue-lighten-1"
                                  prepend-icon="mdi-qrcode"
                                  @click="generateQrCode"
                                  v-bind="props"
                                  :loading="generating"
                                  :disabled="generating"
                                  class="mb-3"
                                >
                                  Generate QR
                                </v-btn>
                              </template>
                            </v-tooltip>

                            <!-- Print buttons in a responsive layout -->
                            <!-- FO2 - FO4 Release -->
                            <div class="gap-2 d-flex flex-column w-100">
                              <v-btn
                                v-if="
                                  document.qr_code_path &&
                                  (document.reference_document === 'F02' ||
                                    document.reference_document === 'F04')
                                "
                                color="blue-lighten-1"
                                prepend-icon="mdi-printer"
                                size="default"
                                @click="
                                  printQrCode(
                                    'released',
                                    $page.props.auth.user.operating_unit.code,
                                    document.control_number
                                  )
                                "
                                class="py-2 text-body-2 font-weight-medium"
                              >
                                Print Released QR
                              </v-btn>

                              <!-- FO1 - FO3 Receive -->
                              <v-btn
                                v-if="
                                  document.qr_code_path &&
                                  (document.reference_document === 'F01' ||
                                    document.reference_document === 'F03')
                                "
                                color="blue-lighten-1"
                                prepend-icon="mdi-printer"
                                size="default"
                                @click="
                                  printQrCode(
                                    'received',
                                    $page.props.auth.user.operating_unit.code,
                                    document.control_number
                                  )
                                "
                                class="py-2 text-body-2 font-weight-medium"
                              >
                                Print Received QR
                              </v-btn>
                            </div>
                          </div>
                        </v-expansion-panel-text>
                      </v-expansion-panel>
                    </v-expansion-panels>

                    <!-- Scanned Document Section -->
                    <v-expansion-panels
                      v-model="docPanelOpen"
                      variant="accordion"
                      class="elevation-0"
                    >
                      <v-expansion-panel value="doc-panel" elevation="0">
                        <v-expansion-panel-title
                          class="px-0 text-center text-black font-weight-bold"
                        >
                          Scanned Document
                        </v-expansion-panel-title>
                        <v-expansion-panel-text class="px-0">
                          <div class="d-flex flex-column align-center">
                            <FileUploader
                              @upload="handleFileUpload"
                              @autoSubmit="autoSubmitFile"
                              :hasExistingDocument="!!document.file_path"
                              class="justify-center w-100 d-flex"
                            />
                          </div>
                        </v-expansion-panel-text>
                      </v-expansion-panel>
                    </v-expansion-panels>

                    <!-- File Display Section -->
                    <div class="mt-4">
                      <!-- Uploaded Files -->
                      <div v-if="uploadedFiles.length" class="mb-3">
                        <div class="mb-2 text-subtitle-2 font-weight-medium">
                          Uploaded Files:
                        </div>
                        <div
                          v-for="(file, index) in uploadedFiles"
                          :key="index"
                        >
                          <v-card
                            class="mb-2 rounded-lg d-flex align-center pa-2 elevation-1"
                          >
                            <!-- LEFT: Thumbnail + name -->
                            <v-avatar
                              size="40"
                              class="mr-2 mr-sm-3"
                              rounded="lg"
                              v-if="file.type.startsWith('image/')"
                            >
                              <v-img :src="getObjectURL(file)" />
                            </v-avatar>

                            <v-avatar
                              size="40"
                              class="justify-center mr-2 mr-sm-3 d-flex align-center"
                              color="grey-lighten-3"
                              v-else
                            >
                              <v-icon size="24">mdi-file</v-icon>
                            </v-avatar>

                            <div
                              class="mr-1 text-caption text-sm-subtitle-2 line-clamp-1 flex-grow-1"
                            >
                              {{ file.name }}
                            </div>

                            <!-- RIGHT: Action buttons -->
                            <div class="d-flex">
                              <v-tooltip text="View file">
                                <template v-slot:activator="{ props }">
                                  <v-btn
                                    icon
                                    size="small"
                                    color="blue-lighten-1"
                                    variant="text"
                                    @click="viewFile(file)"
                                    v-bind="props"
                                  >
                                    <v-icon size="18">mdi-eye</v-icon>
                                  </v-btn>
                                </template>
                              </v-tooltip>

                              <v-tooltip text="Remove file">
                                <template v-slot:activator="{ props }">
                                  <v-btn
                                    icon
                                    size="small"
                                    color="error"
                                    variant="text"
                                    @click="removeFile(index)"
                                    v-bind="props"
                                  >
                                    <v-icon size="18">mdi-trash-can</v-icon>
                                  </v-btn>
                                </template>
                              </v-tooltip>
                            </div>
                          </v-card>
                        </div>
                      </div>

                      <!-- Existing Document -->
                      <div v-if="document.file_path" class="mb-3">
                        <div class="mb-2 text-subtitle-2 font-weight-medium">
                          Current Document:
                        </div>
                        <v-card
                          class="mb-2 rounded-lg d-flex align-center pa-2 elevation-1"
                        >
                          <v-avatar
                            size="40"
                            class="justify-center mr-2 mr-sm-3 d-flex align-center"
                            color="grey-lighten-3"
                          >
                            <template v-if="isImageFile(document.file_path)">
                              <v-img :src="getFileUrl(document.file_path)" />
                            </template>
                            <template v-else>
                              <v-icon size="24">mdi-file-check</v-icon>
                            </template>
                          </v-avatar>

                          <div
                            class="mr-1 text-caption text-sm-subtitle-2 line-clamp-1 flex-grow-1"
                          >
                            {{ getFileName(document.file_path) }}
                          </div>

                          <div class="d-flex">
                            <v-tooltip text="View document">
                              <template v-slot:activator="{ props }">
                                <v-btn
                                  icon
                                  size="small"
                                  color="primary"
                                  variant="text"
                                  @click="viewExistingFile(document.file_path)"
                                  v-bind="props"
                                >
                                  <v-icon size="18">mdi-eye</v-icon>
                                </v-btn>
                              </template>
                            </v-tooltip>

                            <v-tooltip text="Remove document">
                              <template v-slot:activator="{ props }">
                                <v-btn
                                  icon
                                  size="small"
                                  color="error"
                                  variant="text"
                                  @click="confirmRemoveFile()"
                                  v-bind="props"
                                >
                                  <v-icon size="18">mdi-trash-can</v-icon>
                                </v-btn>
                              </template>
                            </v-tooltip>
                          </div>
                        </v-card>
                      </div>
                    </div>
                    <div
                      v-if="document.status === 'archived'"
                      class="absolute inset-0 z-10 bg-gray-300 rounded-lg pointer-events-auto bg-opacity-60"
                    ></div>
                  </v-col>

                  <!-- Forward Document Section - Only visible when document is uploaded and not in edit mode -->
                  <v-col
                    v-if="showForwardSection && document.status !== 'inactive'"
                    cols="12"
                    md="6"
                    lg="3"
                    xl="3"
                    class="relative mb-4 bg-white border border-black rounded-lg pa-3 pa-sm-4 pa-md-5"
                  >
                    <!-- Forward Document in an expansion panel for mobile -->
                    <v-expansion-panels
                      v-model="forwardPanelOpen"
                      variant="accordion"
                      class="elevation-0"
                    >
                      <v-expansion-panel value="forward-panel" elevation="0">
                        <v-expansion-panel-title
                          class="px-0 text-center text-black font-weight-bold"
                        >
                          Forward Document
                        </v-expansion-panel-title>
                        <v-expansion-panel-text class="px-0">
                          <div class="d-flex flex-column">
                            <!-- Recipient Type Selector -->
                            <div class="mb-3">
                              <div
                                class="mb-1 text-black text-subtitle-2 font-weight-bold"
                              >
                                Recipient Type:
                              </div>
                              <v-btn-toggle
                                v-model="forwardForm.recipient_type"
                                mandatory
                                color="primary"
                                density="compact"
                                class="border rounded w-100"
                                :error-messages="
                                  forwardValidationErrors?.recipient_type
                                "
                                :error="
                                  !!forwardValidationErrors?.recipient_type
                                "
                                @update:model-value="onRecipientTypeChange"
                              >
                                <v-btn
                                  value="operating_units"
                                  class="flex-grow-1"
                                >
                                  Operating
                                </v-btn>
                                <v-btn
                                  value="department_units"
                                  class="flex-grow-1 department-disabled"
                                  @click.stop="showDepartmentUnavailableDialog"
                                >
                                  Department
                                </v-btn>
                              </v-btn-toggle>
                              <div
                                v-if="forwardValidationErrors?.recipient_type"
                                class="mt-1 text-error text-caption"
                              >
                                {{ forwardValidationErrors.recipient_type }}
                              </div>
                            </div>

                            <!-- Confidentiality Class -->
                            <div class="mb-3">
                              <div
                                class="mb-1 text-black text-subtitle-2 font-weight-bold"
                              >
                                Confidentiality Class:
                              </div>
                              <v-select
                                v-model="forwardForm.classification"
                                :items="DocInfoClassOptions"
                                item-title="label"
                                item-value="value"
                                variant="outlined"
                                bg-color="white"
                                color="primary"
                                class="text-black"
                                density="compact"
                                required
                                :error-messages="
                                  forwardValidationErrors?.classification
                                "
                                :error="
                                  !!forwardValidationErrors?.classification
                                "
                                @update:model-value="
                                  forwardValidationErrors &&
                                    (forwardValidationErrors.classification =
                                      '')
                                "
                              ></v-select>

                              <!-- Public classification info message -->
                              <div
                                v-if="forwardForm.classification === 'public'"
                                class="mt-1 text-info text-caption"
                              >
                                <v-icon size="small" color="info" class="mr-1"
                                  >mdi-information-outline</v-icon
                                >
                                <span
                                  v-if="
                                    forwardForm.recipient_type ===
                                    'operating_units'
                                  "
                                >
                                  Document will be forwarded to all operating
                                  units
                                </span>
                                <span v-else>
                                  Document will be forwarded to all department
                                  units in your operating unit
                                </span>
                              </div>
                            </div>

                            <!-- Priority Class -->
                            <div class="mb-3">
                              <div
                                class="mb-1 text-black text-subtitle-2 font-weight-bold"
                              >
                                Priority Class:
                              </div>
                              <v-select
                                v-model="forwardForm.priority"
                                :items="priorityOptions"
                                item-title="label"
                                item-value="value"
                                variant="outlined"
                                bg-color="white"
                                color="primary"
                                class="text-black"
                                density="compact"
                                required
                                :error-messages="
                                  forwardValidationErrors?.priority
                                "
                                :error="!!forwardValidationErrors?.priority"
                                @update:model-value="
                                  forwardValidationErrors &&
                                    (forwardValidationErrors.priority = '')
                                "
                              ></v-select>
                            </div>

                            <!-- Notes -->
                            <div class="mb-4">
                              <div
                                class="mb-1 text-black text-subtitle-2 font-weight-bold"
                              >
                                Remarks:
                              </div>
                              <v-textarea
                                v-model="forwardForm.remarks"
                                density="compact"
                                variant="outlined"
                                color="primary"
                                class="text-black"
                                rows="3"
                                :error-messages="forwardValidationErrors?.notes"
                                :error="!!forwardValidationErrors?.notes"
                                @input="
                                  forwardValidationErrors &&
                                    (forwardValidationErrors.notes = '')
                                "
                              ></v-textarea>
                            </div>

                            <!-- Forward Button -->
                            <v-btn
                              block
                              @click="goToForwardDocument"
                              color="success"
                              class="text-white"
                              :disabled="hasForwardValidationErrors"
                            >
                              Forward Document
                              <v-icon class="ml-2">mdi-send</v-icon>
                            </v-btn>
                          </div>
                        </v-expansion-panel-text>
                      </v-expansion-panel>
                    </v-expansion-panels>

                    <!-- Overlay for archived documents -->
                    <div
                      v-if="document.status === 'archived'"
                      class="absolute inset-0 z-10 bg-gray-300 rounded-lg pointer-events-auto bg-opacity-60"
                    ></div>
                  </v-col>
                </v-row>
              </v-card-text>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- Forward Modal Dialog -->
    <template>
      <v-dialog v-model="forwardDoc" max-width="500">
        <v-card>
          <v-card-title class="text-white text-h5 bg-blue-lighten-1">
            Forward Document
          </v-card-title>

          <v-card-text class="pt-4">
            <v-form @submit.prevent="submitCampusSelection">
              <div class="w-full max-w-md mx-auto">
                <v-tooltip
                  text="Select one or more recipients to forward this document to"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <div class="mb-3 d-flex align-center" v-bind="props">
                      <span class="text-subtitle-1 font-weight-medium">
                        Select {{ recipientTypeLabel }}
                      </span>
                      <v-icon size="small" class="ml-1"
                        >mdi-information-outline</v-icon
                      >
                    </div>
                  </template>
                </v-tooltip>

                <!-- Operating Units Selection -->
                <v-select
                  v-if="forwardForm.recipient_type === 'operating_units'"
                  v-model="selectedOptions"
                  :items="operatingUnits"
                  item-title="name"
                  item-value="id"
                  :label="`Select Operating Units`"
                  variant="outlined"
                  color="primary"
                  bg-color="white"
                  multiple
                  chips
                  closable-chips
                  :error-messages="campusSelectionError"
                  class="mb-4 campus-select"
                >
                  <template v-slot:selection="{ item }">
                    <v-chip
                      color="blue-lighten-1"
                      text-color="white"
                      class="font-weight-medium"
                    >
                      {{ item.raw.name }}
                    </v-chip>
                  </template>
                </v-select>

                <!-- Department Units Selection -->
                <v-select
                  v-else
                  v-model="selectedOptions"
                  :items="departmentUnits"
                  item-title="name"
                  item-value="id"
                  :label="`Select Department Units`"
                  variant="outlined"
                  color="primary"
                  bg-color="white"
                  multiple
                  chips
                  closable-chips
                  :error-messages="campusSelectionError"
                  class="mb-4 campus-select"
                >
                  <template v-slot:selection="{ item }">
                    <v-chip
                      color="blue-lighten-1"
                      text-color="white"
                      class="font-weight-medium"
                    >
                      {{ item.raw.name }}
                    </v-chip>
                  </template>
                </v-select>
              </div>

              <v-card-actions class="px-0 pb-0">
                <v-spacer />
                <v-btn
                  text
                  @click="forwardDoc = false"
                  color="error"
                  variant="text"
                  >Cancel</v-btn
                >
                <v-btn
                  type="submit"
                  color="success"
                  variant="text"
                  :loading="forwardForm.processing"
                  :disabled="selectedOptions.length === 0"
                >
                  Forward Document
                </v-btn>
              </v-card-actions>
            </v-form>
          </v-card-text>
        </v-card>
      </v-dialog>
    </template>

    <!-- Update Modal dialog -->
    <v-dialog v-model="updateDialog" max-width="500">
      <v-card>
        <v-card-title class="text-white text-h5 bg-blue-lighten-1"
          >Confirm Update</v-card-title
        >
        <v-card-text class="pt-4">
          <p>Please Check Details before Submission</p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="updateDialog = false"
          >
            Cancel
          </v-btn>
          <v-btn color="success" variant="text" @click="confirmUpdate">
            Confirm Update
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Delete Modal Dialog -->
    <v-dialog v-model="deleteDialog" max-width="500">
      <v-card>
        <v-card-title class="text-white text-h5 bg-error"
          >Confirm Delete</v-card-title
        >
        <v-card-text class="pt-4">
          <strong class="py-2">WARNING! </strong>
          <p class="py-2">
            Are you sure you want to delete this document? All data will be lost
          </p>
          <p v-if="documentToDelete" class="mt-2 font-weight-medium">
            Document: {{ documentToDelete.control_number }} -
            {{ documentToDelete.document_code }}
          </p>
          <p class="mt-4 text-error font-weight-bold">
            This action cannot be undone.
          </p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="deleteDialog = false"
          >
            Cancel
          </v-btn>
          <v-btn
            color="error"
            variant="text"
            @click="deleteDocument"
            :loading="deleting"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Hidden iframe for printing -->
    <iframe
      v-if="document.qr_code_path"
      ref="printFrame"
      :src="printUrl"
      style="display: none"
    ></iframe>

    <!-- Validation Dialog -->
    <v-dialog v-model="validationDialog" max-width="500">
      <v-card>
        <v-card-title class="text-white text-h5 bg-warning"
          >Document Requirements Warning</v-card-title
        >
        <v-card-text class="pt-4">
          <p class="mb-3">
            <strong>Warning:</strong> You are about to leave this document page
            without completing important requirements.
          </p>
          <p class="mb-3">The following items are missing:</p>
          <ul class="mb-4 ml-5 list-disc">
            <li v-for="(item, index) in missingItems" :key="index" class="mb-1">
              {{ item }}
            </li>
          </ul>
          <p class="mt-4 font-weight-medium">
            Would you like to stay on this page to complete these requirements
            or continue to the documents list?
          </p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="warning"
            variant="text"
            @click="router.visit('/documents')"
          >
            Continue to List
          </v-btn>
          <v-btn
            color="success"
            variant="text"
            @click="validationDialog = false"
          >
            Stay and Complete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Amend Document CONFIRMATION-->
    <v-dialog v-model="overhaulConfirmModal" max-width="750" persistent>
      <v-card>
        <v-card-title class="text-white text-h5 bg-warning">
          Amend Control Number
        </v-card-title>

        <v-card-text>
          <p class="mb-4">
            <strong>PLEASE READ CAREFULLY</strong>
          </p>
          <p class="mb-4">
            You are about to amend this document's Control Number. This action
            will result to the following:
          </p>
          <p class="mb-4">
            Current Document Control Number:
            <strong>{{ document.control_number }} INACTIVE</strong>
          </p>
          <p class="mb-4">
            New Document Control Number:
            <strong>{{ displayControlNumber }}</strong>
          </p>
          <p class="mb-4">
            Remarks/Reason:
            <strong>{{ formInactive.remarks }}</strong>
          </p>

          <p class="text-center text-lg">
            <v-icon color="warning">mdi-alert</v-icon
            ><strong class="text-error uppercase"
              >This action is irreversible.</strong
            >
            <v-icon color="warning">mdi-alert</v-icon>
          </p>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="overhaulConfirmModal = false">Cancel</v-btn>
          <v-btn color="warning" @click="confirmOverhaulDocument"
            >Proceed Amendment</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Amend Document -->
    <v-dialog v-model="overhaulDialog" max-width="500" persistent>
      <v-card>
        <v-card-title class="text-white text-h5 bg-warning">
          Amend Control Number
        </v-card-title>

        <v-card-text>
          <p class="mb-4">
            <strong>PLEASE READ CAREFULLY</strong>
          </p>
          <p class="mb-4">
            You are about to amend this document's Control Number. This action
            will change the current control number with a new one, rendering
            the existing document inactive.
          </p>
          <p class="text-center text-lg">
            <v-icon color="warning">mdi-alert</v-icon
            ><strong class="text-error uppercase"
              >This action is irreversible.</strong
            >
            <v-icon color="warning">mdi-alert</v-icon>
          </p>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="overhaulDialog = false">Cancel</v-btn>
          <v-btn color="warning" @click="editControlNumber"
            >I understand, Proceed</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Anti-Date Password Verification Dialog -->
    <v-dialog v-model="antiDateDialog" max-width="500" persistent>
      <v-card>
        <v-card-title class="text-white text-h5 bg-warning"
          >Password Verification Required</v-card-title
        >
        <v-card-text class="pt-4">
          <p class="mb-4">
            <strong>Warning:</strong> You are about to modify the received date
            of this document. This action requires password verification for
            security purposes.
          </p>
          <p class="mb-4">Please enter your password to continue:</p>
          <v-text-field
            v-model="passwordForm.anti_date_password"
            label="Password"
            :type="showPassword ? 'text' : 'password'"
            :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
            @click:append-inner="showPassword = !showPassword"
            variant="outlined"
            bg-color="white"
            :error-messages="passwordForm.errors.anti_date_password"
            @keyup.enter="verifyPassword"
          ></v-text-field>
          <div v-if="passwordError" class="mb-2 text-error">
            {{ passwordError }}
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey-darken-1" variant="text" @click="cancelAntiDate">
            Cancel
          </v-btn>
          <v-btn
            color="warning"
            variant="text"
            @click="verifyPassword"
            :loading="verifying"
          >
            Verify
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Remove File Confirmation Dialog -->
    <v-dialog v-model="removeFileDialog" max-width="500">
      <v-card>
        <v-card-title class="text-white text-h5 bg-error"
          >Confirm Remove Document</v-card-title
        >
        <v-card-text class="pt-4">
          <strong class="py-2 text-error">WARNING! </strong>
          <p class="py-2">
            Are you sure you want to remove this document file?
          </p>
          <p v-if="document.file_path" class="mt-2 font-weight-medium">
            File: {{ getFileName(document.file_path) }}
          </p>
          <p class="mt-4 text-error font-weight-bold">
            This action cannot be undone.
          </p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="removeFileDialog = false"
          >
            Cancel
          </v-btn>
          <v-btn
            color="error"
            variant="text"
            @click="removeExistingFile()"
            :loading="removingFile"
          >
            Remove
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Success Alert -->
    <v-dialog v-model="successDialog" max-width="400" persistent>
      <v-card>
        <v-card-title class="py-3 text-white bg-success">Success</v-card-title>
        <v-card-text class="pt-4 pb-3 text-center">
          {{ successMessage }}
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions class="justify-end">
          <v-btn color="success" variant="text" @click="handleSuccessClose">
            CLOSE
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Department Feature Coming Soon Dialog -->
    <v-dialog v-model="departmentFeatureDialog" max-width="500">
      <v-card class="text-white bg-grey-darken-4">
        <v-card-text class="pt-4 pb-3">
          <div class="d-flex flex-column">
            <div class="mb-2">127.0.0.1:8000 says</div>
            <div class="mb-4">Reports and Analytics feature coming soon!</div>
            <div class="justify-end d-flex">
              <v-btn
                color="blue-lighten-3"
                variant="elevated"
                @click="departmentFeatureDialog = false"
                class="rounded-pill"
              >
                OK
              </v-btn>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Document Preview Modal -->
    <v-dialog
      v-model="documentPreviewDialog"
      max-width="90%"
      max-height="90%"
      transition="dialog-bottom-transition"
    >
      <v-card>
        <v-toolbar dark color="primary">
          <v-btn icon dark @click="documentPreviewDialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
          <v-toolbar-title v-if="previewDocument">
            {{ previewDocument.control_number }} ({{
              previewDocument.document_code || ""
            }})
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <v-toolbar-items>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-if="
                    previewDocument && isImageFile(previewDocument.file_path)
                  "
                  icon
                  @click="zoomIn"
                  v-bind="props"
                >
                  <v-icon>mdi-magnify-plus</v-icon>
                </v-btn>
              </template>
              <span>Zoom In</span>
            </v-tooltip>

            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-if="
                    previewDocument && isImageFile(previewDocument.file_path)
                  "
                  icon
                  @click="zoomOut"
                  v-bind="props"
                >
                  <v-icon>mdi-magnify-minus</v-icon>
                </v-btn>
              </template>
              <span>Zoom Out</span>
            </v-tooltip>

            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-if="
                    previewDocument && isImageFile(previewDocument.file_path)
                  "
                  icon
                  @click="resetZoom"
                  v-bind="props"
                >
                  <v-icon>mdi-magnify-scan</v-icon>
                </v-btn>
              </template>
              <span>Reset Zoom</span>
            </v-tooltip>

            <v-chip v-if="zoomLevel > 1" color="info" size="small" class="ml-2">
              <v-icon start size="small">mdi-gesture-swipe</v-icon>
              Drag to Pan
            </v-chip>
          </v-toolbar-items>
        </v-toolbar>
        <v-card-text class="pa-0">
          <div class="document-preview-modal-container">
            <div
              v-if="previewDocument && isImageFile(previewDocument.file_path)"
              class="image-preview-container"
              ref="previewContainer"
              @mousedown="startDrag"
              @mousemove="onDrag"
              @mouseup="stopDrag"
              @mouseleave="stopDrag"
              @touchstart="startDragTouch"
              @touchmove="onDragTouch"
              @touchend="stopDrag"
            >
              <img
                :src="getFileUrl(previewDocument.file_path)"
                alt="Document Preview"
                class="document-preview-image"
                :style="{
                  transform: `scale(${zoomLevel}) translate(${dragPosition.x}px, ${dragPosition.y}px)`,
                  cursor: isDragging
                    ? 'grabbing'
                    : zoomLevel > 1
                    ? 'grab'
                    : 'default',
                }"
              />
            </div>
            <div
              v-else-if="previewDocument && previewDocument.file_path"
              class="pdf-preview-container"
            >
              <iframe
                :src="getFileUrl(previewDocument.file_path)"
                class="pdf-preview-frame"
                frameborder="0"
              ></iframe>
            </div>
            <div v-else class="no-document-container">
              <v-icon size="large" color="grey"
                >mdi-file-document-outline</v-icon
              >
              <p class="mt-4">No document available for preview</p>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Department Unavailable Dialog -->
    <v-dialog v-model="departmentUnavailableDialog" max-width="500">
      <v-card class="text-white bg-grey-darken-4">
        <v-card-text class="pt-4 pb-3">
          <div class="pt-3 d-flex flex-column">
            <div class="mb-4">
              <!-- <p>Department recipient type is currently unavailable.</p>  -->
              <p class="mt-2">
                Please use Operating Units as recipients for now.
              </p>
            </div>
            <div class="justify-end d-flex">
              <v-btn
                color="blue-lighten-3"
                variant="elevated"
                @click="departmentUnavailableDialog = false"
                class="rounded-pill"
              >
                OK
              </v-btn>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, router, useForm, usePage } from "@inertiajs/vue3";
import AppSidebarLayout from "@/Layouts/AppSidebarLayout.vue";
import { ref, computed, watch } from "vue";
import axios from "axios";
import FileUploader from "../../Components/FileUploader.vue";

const page = usePage();

const props = defineProps({
  document: Object,
  documents: Object,
  documentCodes: Object,
  operatingUnits: Array,
  departmentUnits: Array,
  userRole: String,
  errors: Object,
});

// Custom validation errors for document form
const validationErrors = ref({
  signatory: "",
  origin: "",
  subject_matter: "",
  referred_to: "",
  document_code: "",
  status: "",
  date_received: "",
  file: "",
});

// Custom validation errors for forward form
const forwardValidationErrors = ref({
  classification: "",
  priority: "",
  notes: "",
  operating_units: "",
  department_units: "",
  recipient_type: "",
});

// UI state variables
const generating = ref(false);
const deleting = ref(false);
const editMode = ref(false);
const editDateMode = ref(false);
const forwardDoc = ref(false);
const updateDialog = ref(false);
const deleteDialog = ref(false);
const antiDateDialog = ref(false);
const showPassword = ref(false);
const verifying = ref(false);
const passwordError = ref("");
const documentToDelete = ref(null);
const uploadedFiles = ref([]);
const validationDialog = ref(false);
const hideEditBtn = ref(false);
const missingItems = ref([]);
const campusSelectionError = ref("");
const departmentFeatureDialog = ref(false);

// Computed property to check if there are any validation errors in the document form
const hasValidationErrors = computed(() => {
  if (!validationErrors.value) return false;
  return Object.values(validationErrors.value).some((error) => error !== "");
});

// Computed property to check if there are any validation errors in the forward form
const hasForwardValidationErrors = computed(() => {
  if (!forwardValidationErrors.value) return false;
  return Object.values(forwardValidationErrors.value).some(
    (error) => error !== ""
  );
});

// Department unavailable dialog
const departmentUnavailableDialog = ref(false);

// Document preview modal state
const documentPreviewDialog = ref(false);
const previewDocument = ref(null);
const zoomLevel = ref(1);
const previewContainer = ref(null);
const isDragging = ref(false);
const dragStart = ref({ x: 0, y: 0 });
const dragPosition = ref({ x: 0, y: 0 });
const lastPosition = ref({ x: 0, y: 0 });

// Success dialog state
const successDialog = ref(false);
const successMessage = ref("");

// Expansion panel states for responsive design
const qrPanelOpen = ref(["qr-panel"]);
const docPanelOpen = ref(["doc-panel"]);
const forwardPanelOpen = ref(["forward-panel"]);

// No longer needed as we're using selectedOptions directly

// Computed property to determine if Forward Document section should be visible
const showForwardSection = computed(() => {
  return (
    (props.document.file_path && !editMode.value) ||
    props.document.status === "archived"
  );
});

// Computed property for recipient type label
const recipientTypeLabel = computed(() => {
  return forwardForm.recipient_type === "operating_units"
    ? "Operating Units"
    : "Department Units";
});

// Reference documents for the dropdown
const referenceDocuments = ["F01", "F02", "F03", "F04"];
const overhaulDialog = ref(false);
const overhaulInputs = ref(false);
const overhaulConfirmModal = ref(false);
const displayControlNumber = ref("");

const overhaulForm = useForm({
  signatory: props.document.signatory,
  origin: props.document.origin,
  date_received: props.document.date_received,
  subject_matter: props.document.subject_matter,
  referred_to: props.document.referred_to,
  document_code: props.document.document_code,
  origin_unit_id: "",
  reference_document: "",
  control_number: "",
  // file_path: props.document.file_path,
});

const formInactive = useForm({
  old_control_number: props.document.control_number,
  status: "inactive",
  remarks: "",
  // file_path: null,
});

watch(displayControlNumber, (newVal) => {
  overhaulForm.control_number = newVal;
});

watch(
  () => page.props.auth.user.id,
  (newUserId) => {
    if (newUserId) {
      overhaulForm.origin_unit_id = newUserId;
    }
  },
  { immediate: true } // Run the watcher immediately on setup
);

const overhaulDialogBox = () => {
  // Open the overhaul dialog
  overhaulDialog.value = true;
};

const editControlNumber = () => {
  // Open the dialog to edit control number
  overhaulDialog.value = false;
  overhaulInputs.value = true;
  hideEditBtn.value = true;


  const controlCurrentRefDoc = props.document.reference_document;
  for (let i = referenceDocuments.length - 1; i >= 0; i--) {
    if (referenceDocuments[i] === controlCurrentRefDoc) {
      referenceDocuments.splice(i, 1);
    }
  }
};

const abortOverhaul = () => {
  // Close the overhaul inputs dialog
  overhaulInputs.value = false;
  overhaulForm.reference_document = "";
  displayControlNumber.value = "";
  hideEditBtn.value = false;

};

const submitOverhauledDocumentModal = () => {
  overhaulConfirmModal.value = true;
};
const updateControlNumber = () => {
  const userUnitId = page.props.auth.user.operating_unit.id;

  if (!overhaulForm.reference_document || !userUnitId) {
    displayControlNumber.value = "";
    return;
  }

  const today = new Date();
  const day = String(today.getDate()).padStart(2, "0");
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const year = today.getFullYear();
  const dateString = `${day}${month}${year}`;

  // The final prefix for display
  const displayPrefix = `${overhaulForm.reference_document}-${dateString}`;

  // 1. Filter all documents matching unit_id and reference_document
  const rawDocuments = Object.values(props.documents.data); // converts to array

  const matchingDocs = rawDocuments.filter((doc) => {
    const parts = doc.control_number.split("-");
    if (
      doc.origin_unit_id === userUnitId &&
      parts.length === 3 &&
      parts[0] === overhaulForm.reference_document
    ) {
      const docYear = parts[1].slice(-4); // get YYYY from DDMMYYYY
      return docYear === String(year);
    }
    return false;
  });

  // 2. Find max serial number
  let maxSequence = 0;

  matchingDocs.forEach((doc) => {
    const parts = doc.control_number.split("-");
    if (parts.length === 3) {
      const serial = parseInt(parts[2], 10);
      if (!isNaN(serial)) {
        maxSequence = Math.max(maxSequence, serial);
      }
    }
  });

  // 3. Generate the new control number
  const nextSequence = maxSequence + 1;
  const paddedSeq = String(nextSequence).padStart(3, "0");
  displayControlNumber.value = `${displayPrefix}-${paddedSeq}`;
};

const form = useForm({
  signatory: props.document.signatory,
  origin: props.document.origin,
  subject_matter: props.document.subject_matter,
  referred_to: props.document.referred_to,
  document_code: props.document.document_code,
  control_number: props.document.control_number,
  status: props.document.status,
  date_received: props.document.date_received,
  file: null,
});

const forwardForm = useForm({
  destination: "",
  notes: "",
  remarks: "",
  priority: "",
  classification: "",
  recipient_type: "operating_units", // Default to operating units
  operating_units: [],
  department_units: [],
});

// Password verification form
const passwordForm = useForm({
  anti_date_password: "",
  document_id: props.document.id,
});

const originalData = ref({
  document_code: props.document.document_code,
  origin: props.document.origin,
  subject_matter: props.document.subject_matter,
  referred_to: props.document.referred_to,
  signatory: props.document.signatory,
});

const goEditMode = () => {
  editMode.value = true;
  // Status is not editable, but we keep it in the form for submission
  form.status = props.document.status;
};
const cancelEdit = () => {
  Object.assign(form, originalData.value);
  editMode.value = false;
  editDateMode.value = false;
  abortOverhaul();
};

const formatDate = (dateString, dateOnly = false) => {
  const date = new Date(dateString);
  if (dateOnly) {
    return date.toLocaleDateString();
  }
  return date.toLocaleString();
};

// Format date for input field
const formatDateForInput = (dateString) => {
  const date = new Date(dateString);
  return date.toISOString().split("T")[0]; // Format as YYYY-MM-DD for input type="date"
};

// Show the anti-date dialog
const showAntiDateDialog = () => {
  passwordForm.reset();
  passwordError.value = "";
  antiDateDialog.value = true;
};

// Cancel the anti-date operation
const cancelAntiDate = () => {
  antiDateDialog.value = false;
  passwordForm.reset();
  passwordError.value = "";
};

// Verify password and enable date editing if successful
const verifyPassword = () => {
  verifying.value = true;
  passwordError.value = "";

  // Send password to server for verification
  passwordForm.post(`/documents/${props.document.id}/verify-password`, {
    preserveScroll: true,
    onSuccess: () => {
      verifying.value = false;
      antiDateDialog.value = false;
      editDateMode.value = true;
    hideEditBtn.value = true;

      // Convert the date to the format expected by the input field
      form.date_received = formatDateForInput(form.date_received);
    },
    onError: (errors) => {
      verifying.value = false;
      if (errors.password) {
        passwordError.value = errors.anti_date_password;
      } else {
        passwordError.value =
          "An error occurred during verification. Please try again.";
      }
    },
  });
};

// These arrays were unused and have been removed

const priorityOptions = [
  { value: "routinary", label: "Routinary" },
  { value: "urgent", label: "Urgent" },
];

const DocInfoClassOptions = [
  { value: "public", label: "Public" },
  { value: "confidential", label: "Confidential" },
];

// Selection handling for operating units
const selectedOptions = ref([]);

const getStatusColor = (status) => {
  switch (status.toLowerCase()) {
    case "pending":
      return "#FFC107"; // warning
    case "in progress":
      return "#2196F3"; // info
    case "completed":
      return "#4CAF50"; // success
    case "rejected":
      return "#F44336"; // error
    case "inactive":
      return "#000000"; // error
    case "archived":
      return "#363636"; // error
    default:
      return "#9E9E9E"; // grey
  }
};
// Status options are no longer needed as status is not editable

const getFileUrl = (path) => {
  // If the path is already a full URL (for temporary files), return it as is
  if (path && (path.startsWith("blob:") || path.startsWith("http"))) {
    return path;
  }
  // Otherwise, prepend the storage path
  return `/storage/${path}`;
};

// Watch for classification changes
watch(
  () => forwardForm.classification,
  (newValue) => {
    // Clear validation errors
    if (forwardValidationErrors.value) {
      forwardValidationErrors.value.classification = "";
    }

    // Update UI based on classification
    if (newValue === "public") {
      // For public documents, we'll show a message indicating all recipients will be selected
      const recipientTypeText =
        forwardForm.recipient_type === "operating_units"
          ? "all operating units"
          : "all department units in your operating unit";

      // Show a toast or notification
      console.log(`Document will be forwarded to ${recipientTypeText}`);
    }
  }
);

// Reset drag position when zoom level is 1 (no zoom)
watch(
  () => zoomLevel.value,
  (newValue) => {
    if (newValue <= 1) {
      dragPosition.value = { x: 0, y: 0 };
      lastPosition.value = { x: 0, y: 0 };
    }
  }
);

// Initialize with default recipient type
forwardForm.recipient_type = "operating_units";

// Watch for changes in form fields to clear validation errors
watch(
  () => form?.signatory,
  () => {
    if (validationErrors.value) {
      validationErrors.value.signatory = "";
    }
  }
);

watch(
  () => form?.origin,
  () => {
    if (validationErrors.value) {
      validationErrors.value.origin = "";
    }
  }
);

watch(
  () => form?.subject_matter,
  () => {
    if (validationErrors.value) {
      validationErrors.value.subject_matter = "";
    }
  }
);

watch(
  () => form?.referred_to,
  () => {
    if (validationErrors.value) {
      validationErrors.value.referred_to = "";
    }
  }
);

watch(
  () => form?.document_code,
  () => {
    if (validationErrors.value) {
      validationErrors.value.document_code = "";
    }
  }
);

// Status is not editable, so we don't need to watch for changes

watch(
  () => form?.date_received,
  () => {
    if (validationErrors.value) {
      validationErrors.value.date_received = "";
    }
  }
);

// Watch for changes in forward form fields to clear validation errors
watch(
  () => forwardForm?.classification,
  () => {
    if (forwardValidationErrors.value) {
      forwardValidationErrors.value.classification = "";
    }
  }
);

watch(
  () => forwardForm?.priority,
  () => {
    if (forwardValidationErrors.value) {
      forwardValidationErrors.value.priority = "";
    }
  }
);

watch(
  () => forwardForm?.notes,
  () => {
    if (forwardValidationErrors.value) {
      forwardValidationErrors.value.notes = "";
    }
  }
);

watch(
  () => forwardForm?.recipient_type,
  () => {
    if (forwardValidationErrors.value) {
      forwardValidationErrors.value.recipient_type = "";
    }
  }
);

const confirmOverhaulDocument = async () => {
  try {
    // Submit overhaulForm
    await new Promise((resolve, reject) => {
      overhaulForm.post("/documents", {
        onSuccess: () => resolve(),
        onError: (errors) => reject(errors),
      });
    });
    overhaulForm.reset();

    // Submit formInactive
    await new Promise((resolve, reject) => {
      formInactive.post("/documents/updateStatus", {
        preserveScroll: true,
        onSuccess: () => resolve(),
        onError: (errors) => reject(errors),
      });
    });
    formInactive.reset();

    successMessage.value = "Control number updated successfully!";
    successDialog.value = true;
  } catch (errors) {
    successMessage.value =
      "Error updating control number: " +
      (errors.message || "Please check your inputs and try again");
    successDialog.value = true;
  }
};

// Document Forward functionality
const forwardDocument = () => {
  // Perform client-side validation first
  if (!validateForwardForm()) {
    return;
  }

  forwardForm.post(`/documents/${props.document.id}/forward`, {
    onSuccess: () => {
      forwardDoc.value = false;
      forwardForm.reset();
      // Show success message in modal
      successMessage.value = "Document forwarded successfully!";
      successDialog.value = true;

      // The dialog will close when the user clicks the button
    },
    onError: (errors) => {
      // Handle error with a more descriptive message
      successMessage.value =
        "Error forwarding document: " +
        (errors.message || "Please check your inputs and try again");
      successDialog.value = true;
    },
    onFinish: () => {
      forwardForm.reset();
    },
  });
};

// Navigation functions
const goToDocumentsList = () => {
  // Check if QR code or document is missing
  missingItems.value = [];

  if (!props.document.qr_code_path) {
    missingItems.value.push("QR code");
  }

  if (!props.document.file_path && uploadedFiles.value.length === 0) {
    missingItems.value.push("document upload");
  }

  // If any items are missing, show validation dialog
  if (missingItems.value.length > 0 && props.document.status !== "inactive") {
    validationDialog.value = true;
  } else {
    // If everything is complete, navigate to documents list
    router.visit("/documents");
  }
};

// Function to navigate to the document creation page
const goToCreateDocument = () => {
  router.visit("/documents/create");
};

// File upload functionality
const handleFileUpload = (files) => {
  uploadedFiles.value = files;
};

const getObjectURL = (file) => {
  return URL.createObjectURL(file);
};

const removeFile = (index) => {
  uploadedFiles.value.splice(index, 1);
};

const viewFile = (file) => {
  // Create a temporary object URL for the file
  const url = URL.createObjectURL(file);

  // Set up the preview document with necessary properties
  previewDocument.value = {
    ...props.document,
    file_path: url,
    is_temp_url: true, // Flag to indicate this is a temporary URL
  };

  documentPreviewDialog.value = true;
  zoomLevel.value = 1; // Reset zoom level

  // Reset drag position
  dragPosition.value = { x: 0, y: 0 };
  lastPosition.value = { x: 0, y: 0 };
  isDragging.value = false;
};

// Existing file handling
const getFileName = (path) => path.split("/").pop();

const isImageFile = (path) => {
  if (!path) return false;

  // For blob URLs or http URLs, check if it's an image based on the previewDocument type
  if (path.startsWith("blob:") || path.startsWith("http")) {
    if (previewDocument.value && previewDocument.value.is_temp_url) {
      // For temporary URLs, check the file type from the original file
      const file = uploadedFiles.value[0];
      return file && file.type.startsWith("image/");
    }
  }

  // For regular paths, check the file extension
  return /\.(jpe?g|png|gif|bmp|webp|svg)$/i.test(path);
};

const viewExistingFile = (path) => {
  // Set the preview document with the current document's data and file path
  previewDocument.value = {
    ...props.document,
    file_path: path, // Ensure we're using the provided path
  };
  documentPreviewDialog.value = true;
  zoomLevel.value = 1; // Reset zoom level when opening a document

  // Reset drag position
  dragPosition.value = { x: 0, y: 0 };
  lastPosition.value = { x: 0, y: 0 };
  isDragging.value = false;
};

// Document preview zoom functions
const zoomIn = () => {
  zoomLevel.value = 3; // Set zoom directly to 3x
};

const zoomOut = () => {
  zoomLevel.value = 1; // Reset to normal size
};

const resetZoom = () => {
  zoomLevel.value = 1; // Reset to original size
  dragPosition.value = { x: 0, y: 0 }; // Reset position
};

// Drag functionality for mouse events
const startDrag = (e) => {
  if (zoomLevel.value <= 1) return; // Only allow dragging when zoomed in

  isDragging.value = true;
  dragStart.value = {
    x: e.clientX,
    y: e.clientY,
  };
};

const onDrag = (e) => {
  if (!isDragging.value) return;

  const dx = (e.clientX - dragStart.value.x) / zoomLevel.value;
  const dy = (e.clientY - dragStart.value.y) / zoomLevel.value;

  dragPosition.value = {
    x: lastPosition.value.x + dx,
    y: lastPosition.value.y + dy,
  };
};

const stopDrag = () => {
  if (!isDragging.value) return;

  isDragging.value = false;
  lastPosition.value = { ...dragPosition.value };
};

// Touch event handlers for mobile devices
const startDragTouch = (e) => {
  if (zoomLevel.value <= 1) return;

  isDragging.value = true;
  dragStart.value = {
    x: e.touches[0].clientX,
    y: e.touches[0].clientY,
  };
};

const onDragTouch = (e) => {
  if (!isDragging.value) return;

  const dx = (e.touches[0].clientX - dragStart.value.x) / zoomLevel.value;
  const dy = (e.touches[0].clientY - dragStart.value.y) / zoomLevel.value;

  dragPosition.value = {
    x: lastPosition.value.x + dx,
    y: lastPosition.value.y + dy,
  };
};

// Document file removal
const removeFileDialog = ref(false);
const removingFile = ref(false);

const confirmRemoveFile = () => {
  removeFileDialog.value = true;
};

const removeExistingFile = () => {
  if (props.document.file_path) {
    removingFile.value = true;
    axios
      .delete(`/documents/${props.document.id}/remove-file`)
      .then(() => {
        removeFileDialog.value = false;
        removingFile.value = false;
        // Refresh the document to show the updated state
        router.visit(`/documents/${props.document.id}`, {
          only: ["document"],
          preserveScroll: true,
        });
      })
      .catch((error) => {
        console.error("Error removing file:", error);
        // Use success dialog for error message
        successMessage.value =
          "Error removing file: " +
          (error.response?.data?.error || "Unknown error");
        successDialog.value = true;
        removingFile.value = false;
      });
  }
};

// QR Code printing functionality
const printFrame = ref(null);
const qrCodeUrl = ref(null);

const printUrl = computed(() => {
  if (!props.document.qr_code_path) return "";
  return `/print-qr-code?url=${encodeURIComponent(
    getFileUrl(props.document.qr_code_path)
  )}`;
});

const generateQrCode = async () => {
  generating.value = true;
  try {
    const response = await axios.post(
      `/documents/${props.document.id}/generate-qr`
    );
    qrCodeUrl.value = response.data.qr_code_url;
  } catch (error) {
    console.error("Error generating QR code:", error);
    alert(
      "Error generating QR code: " +
        (error.response?.data?.error || "Unknown error")
    );
  } finally {
    generating.value = false;
    router.visit(`/documents/${props.document.id}`);
  }
};

const printQrCode = (type, operatingUnitCode, controlNumber) => {
  if (!printUrl.value) return;

  const urlWithType = `${printUrl.value}&type=${type}&opUnit=${operatingUnitCode}&contNo=${controlNumber}`;

  // Load the URL into the hidden iframe
  if (printFrame.value) {
    printFrame.value.src = urlWithType;
    printFrame.value.onload = () => {
      printFrame.value.contentWindow.focus();
      printFrame.value.contentWindow.print();
    };
  }
};

// Forwarding Document
const goToForwardDocument = () => {
  // Perform client-side validation first
  if (!validateForwardForm()) {
    return;
  }

  // For public classification, forward directly to all users of the selected recipient type
  if (forwardForm.classification === "public") {
    // For public documents, we'll send to all users of the selected type
    if (forwardForm.recipient_type === "operating_units") {
      // Set all operating units
      forwardForm.operating_units = props.operatingUnits.map((unit) => unit.id);
      forwardForm.department_units = []; // Clear department units
    } else {
      // Set all department units
      forwardForm.department_units = props.departmentUnits.map(
        (dept) => dept.id
      );
      forwardForm.operating_units = []; // Clear operating units
    }

    // Forward directly without showing the selection dialog
    forwardDocument();
  } else {
    // For confidential classification, show the selection dialog
    // Reset selected options
    selectedOptions.value = [];

    // Show the selection dialog
    forwardDoc.value = true;

    // Reset any previous errors
    campusSelectionError.value = "";
  }
};

// Submit campus selection form
const submitCampusSelection = () => {
  // Validate selection
  if (selectedOptions.value.length === 0) {
    campusSelectionError.value = "Please select at least one recipient";
    return;
  }

  // Set the selected recipients to the form based on recipient type
  if (forwardForm.recipient_type === "operating_units") {
    forwardForm.operating_units = selectedOptions.value;
    forwardForm.department_units = []; // Clear department units
  } else {
    forwardForm.department_units = selectedOptions.value;
    forwardForm.operating_units = []; // Clear operating units
  }

  // Close the dialog
  forwardDoc.value = false;

  // Submit the form
  forwardDocument();
};

// Auto-submit file immediately after selection
const autoSubmitFile = (files) => {
  if (files && files.length > 0) {
    form.file = files[0];

    form.post(`/documents/${props.document.id}/upload`, {
      forceFormData: true,
      onSuccess: () => {
        form.reset();
        uploadedFiles.value = [];
        // Refresh the current page to show the uploaded document
        router.visit(`/documents/${props.document.id}`, {
          only: ["document"],
          preserveScroll: true,
          onSuccess: () => {
            // Success is handled by the page refresh showing the updated document
          },
        });
      },
      onError: (errors) => {
        // Error handling is managed by Inertia's built-in error display
        console.error("Upload errors:", errors);
      },
    });
  }
};

// No legacy function needed as we now use autoSubmitFile
// updating
// Validate document form before submission
const validateDocumentForm = () => {
  let isValid = true;

  // Ensure validationErrors is initialized
  if (!validationErrors.value) {
    return false;
  }

  // Reset all validation errors
  Object.keys(validationErrors.value).forEach((key) => {
    validationErrors.value[key] = "";
  });

  // Ensure form is initialized
  if (!form) {
    return false;
  }

  // Validate signatory
  if (!form.signatory) {
    validationErrors.value.signatory = "Signatory is required";
    isValid = false;
  } else if (form.signatory.length > 255) {
    validationErrors.value.signatory =
      "Signatory must be less than 255 characters";
    isValid = false;
  }

  // Validate origin
  if (!form.origin) {
    validationErrors.value.origin = "Origin is required";
    isValid = false;
  } else if (form.origin.length > 255) {
    validationErrors.value.origin = "Origin must be less than 255 characters";
    isValid = false;
  }

  // Validate subject matter
  if (!form.subject_matter) {
    validationErrors.value.subject_matter = "Subject matter is required";
    isValid = false;
  }

  // Validate referred to
  if (!form.referred_to) {
    validationErrors.value.referred_to = "Referred to is required";
    isValid = false;
  } else if (form.referred_to.length > 255) {
    validationErrors.value.referred_to =
      "Referred to must be less than 255 characters";
    isValid = false;
  }

  // Validate document code
  if (!form.document_code) {
    validationErrors.value.document_code = "Document code is required";
    isValid = false;
  } else if (form.document_code.length > 255) {
    validationErrors.value.document_code =
      "Document code must be less than 255 characters";
    isValid = false;
  }

  // Status is not editable, but we still validate it's present in the form
  if (!form.status) {
    validationErrors.value.status = "Status is required";
    isValid = false;
  }

  // Validate date received if in edit mode
  if (editDateMode.value && !form.date_received) {
    validationErrors.value.date_received = "date_received is required";
    isValid = false;
  }

  return isValid;
};

// Show department unavailable dialog
const showDepartmentUnavailableDialog = () => {
  departmentUnavailableDialog.value = true;
  // Ensure operating_units is always selected
  forwardForm.recipient_type = "operating_units";
};

// Handle recipient type change
const onRecipientTypeChange = (newValue) => {
  // Clear validation errors
  if (forwardValidationErrors.value) {
    forwardValidationErrors.value.recipient_type = "";
  }

  // Update UI based on classification and recipient type
  if (forwardForm.classification === "public") {
    // For public documents, we'll show a message indicating all recipients will be selected
    const recipientTypeText =
      newValue === "operating_units"
        ? "all operating units"
        : "all department units in your operating unit";

    // Show a toast or notification
    console.log(`Document will be forwarded to ${recipientTypeText}`);
  }
};

// Validate forward form before submission
const validateForwardForm = () => {
  let isValid = true;

  // Ensure forwardValidationErrors is initialized
  if (!forwardValidationErrors.value) {
    return false;
  }

  // Reset all validation errors
  Object.keys(forwardValidationErrors.value).forEach((key) => {
    forwardValidationErrors.value[key] = "";
  });

  // Ensure forwardForm is initialized
  if (!forwardForm) {
    return false;
  }

  // Validate recipient type
  if (!forwardForm.recipient_type) {
    forwardValidationErrors.value.recipient_type = "Recipient type is required";
    isValid = false;
  }

  // Validate classification
  if (!forwardForm.classification) {
    forwardValidationErrors.value.classification =
      "Confidentiality class is required";
    isValid = false;
  }

  // Validate priority
  if (!forwardForm.priority) {
    forwardValidationErrors.value.priority = "Priority class is required";
    isValid = false;
  }

  return isValid;
};

const updateDocument = () => {
  // Perform client-side validation first
  if (!validateDocumentForm()) {
    return;
  }

  updateDialog.value = true;
};

const confirmUpdate = () => {
  // Perform client-side validation again before submitting
  if (!validateDocumentForm()) {
    updateDialog.value = false;
    return;
  }

  if (form.file) {
    form.post(`/documents/${props.document.id}`, {
      onSuccess: () => {
        showReplaceFile.value = false;
        editDateMode.value = false;
        editMode.value = false;
        updateDialog.value = false;

        // Show success message
        successMessage.value = "Document updated successfully!";
        successDialog.value = true;

        // The dialog will close when the user clicks the button
        // We'll update the document status immediately
        props.document.status = form.status;
      },
      onError: (errors) => {
        updateDialog.value = false;
        // Map backend errors to our validation errors
        Object.keys(errors).forEach((key) => {
          if (validationErrors.value.hasOwnProperty(key)) {
            validationErrors.value[key] = errors[key];
          }
        });
      },
    });
  } else {
    form.put(`/documents/${props.document.id}`, {
      onSuccess: () => {
        editDateMode.value = false;
        editMode.value = false;
        updateDialog.value = false;

        // Show success message
        successMessage.value = "Document updated successfully!";
        successDialog.value = true;

        // The dialog will close when the user clicks the button
      },
      onError: (errors) => {
        updateDialog.value = false;
        // Map backend errors to our validation errors
        Object.keys(errors).forEach((key) => {
          if (validationErrors.value.hasOwnProperty(key)) {
            validationErrors.value[key] = errors[key];
          }
        });
      },
    });
  }
};

// Deletion

// Uncomment if you want to implement deletion functionality
/* const confirmDelete = (item) => {
  documentToDelete.value = item;
  deleteDialog.value = true;
};

const deleteDocument = () => {
  if (documentToDelete.value) {
    deleting.value = true;
    router.delete(`/documents/${documentToDelete.value.id}`, {
      onSuccess: () => {
        deleteDialog.value = false;
        documentToDelete.value = null;
        deleting.value = false;
      },
      onError: () => {
        deleting.value = false;
      },
    });
  }
}; */

// Handle success dialog close with appropriate navigation
const handleSuccessClose = () => {
  successDialog.value = false;

  // Check if the success message indicates a forward operation
  if (successMessage.value.includes("forwarded")) {
    router.visit("/documents");
  } else if (successMessage.value.includes("updated")) {
    router.visit(`/documents/${props.document.id}`);
  }
};
</script>

<style scoped>
.document-preview {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
}

.qr-code {
  max-width: 100%;
  height: auto;
  max-height: 150px;
}

@media (min-width: 600px) {
  .qr-code {
    max-width: 120px;
  }
}

.v-list-item {
  margin-bottom: 8px;
}

.text-black text-subtitle-1.font-weight-medium {
  color: rgba(0, 0, 0, 0.87);
  font-weight: 500;
}

.text-body-2 {
  color: rgba(0, 0, 0, 0.6);
}

/* Responsive text adjustments */
@media (max-width: 600px) {
  .text-h6 {
    font-size: 1.1rem !important;
  }

  .text-subtitle-1 {
    font-size: 0.9rem !important;
  }

  .text-subtitle-2 {
    font-size: 0.85rem !important;
  }
}

/* Ensure buttons are properly sized on mobile */
@media (max-width: 600px) {
  .v-btn {
    font-size: 0.8rem;
  }
}

/* Ensure proper spacing in mobile view */
@media (max-width: 600px) {
  .pa-3 {
    padding: 8px !important;
  }

  .mb-4 {
    margin-bottom: 12px !important;
  }
}

/* Improved spacing between sections */
.v-col {
  transition: all 0.3s ease;
}

/* Add consistent spacing between columns */
@media (min-width: 960px) {
  .v-row > .v-col {
    padding: 12px;
  }
}

/* Ensure equal heights for sections when in same row */
@media (min-width: 768px) {
  .v-row {
    align-items: stretch;
  }

  .v-row > .v-col > .v-expansion-panels {
    height: 100%;
  }
}

.custom-btn-toggle {
  height: 300px;
}

/* Campus selection dropdown styling */
.v-select.campus-select .v-field__input {
  min-height: 56px;
}

@media (max-width: 600px) {
  .v-select.campus-select .v-field__input {
    min-height: 48px;
  }

  .v-select.campus-select .v-chip {
    font-size: 0.8rem;
  }
}

/* Document preview modal styling */
.document-preview-modal-container {
  width: 100%;
  height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  overflow: auto;
}

.image-preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* Changed from auto to hidden for better drag experience */
  padding: 20px;
  position: relative;
  /* Added for better positioning */
  touch-action: none;
  /* Disable browser handling of touch events */
}

.document-preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  user-select: none;
  /* Prevent text selection during drag */
  -webkit-user-drag: none;
  /* Prevent default drag behavior */
}

.pdf-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pdf-preview-frame {
  width: 100%;
  height: 100%;
  border: none;
}

.no-document-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #757575;
}

/* Error message styling */
:deep(.v-messages__message) {
  color: rgb(var(--v-theme-error)) !important;
  font-size: 0.875rem !important;
  margin-top: 4px !important;
  font-weight: 500 !important;
}

/* Increase text size in all form inputs */
:deep(.v-field__input),
:deep(.v-select__selection),
:deep(.v-textarea__input) {
  font-size: 1.1rem !important;
}

/* Increase height of form inputs for better touch targets */
:deep(.v-input--density-compact .v-field__input) {
  min-height: 44px !important;
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

/* Department disabled button styling */
.department-disabled {
  opacity: 0.6;
  position: relative;
  cursor: not-allowed !important;
}

.department-disabled::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  pointer-events: none;
}
</style>
