<template>

    <Head :title="`Edit User: ${user.name}`" />

    <AppSidebarLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">Edit User</h2>
        </template>

        <v-container>
            <v-row>
                <v-col cols="12" class="justify-end d-flex">
                    <Link href="/users" class="text-decoration-none">
                    <v-btn color="primary" variant="outlined" prepend-icon="mdi-arrow-left">
                        Back to Users
                    </v-btn>
                    </Link>
                </v-col>
            </v-row>

            <v-row>
                <v-col cols="12">
                    <v-card elevation="2">
                        <v-card-title class="text-white bg-primary">
                            Edit User: {{ user.name }}
                        </v-card-title>

                        <v-card-text>
                            <form @submit.prevent="submit">
                                <v-row class="mt-4">
                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.name" label="Name" required
                                            :error-messages="errors.name" variant="outlined"
                                            bg-color="white"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.email" label="Email" type="email" required
                                            :error-messages="errors.email" variant="outlined"
                                            bg-color="white"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.password"
                                            label="New Password (leave blank to keep current)" type="password"
                                            :error-messages="errors.password" variant="outlined"
                                            bg-color="white"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.password_confirmation" label="Confirm New Password"
                                            type="password" variant="outlined" bg-color="white"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-select v-model="form.role_id" :items="filteredRoles" item-title="name"
                                            item-value="id" label="Role" required :error-messages="errors.role_id"
                                            variant="outlined" bg-color="white">
                                            <template v-slot:append>
                                                <v-tooltip v-if="userRole === 'admin' && !isCentralAdmin"
                                                    text="Only Central Administration admins can assign admin roles for other operating units"
                                                    location="bottom">
                                                    <template v-slot:activator="{ props }">
                                                        <v-icon v-bind="props" color="info"
                                                            class="ml-2">mdi-information</v-icon>
                                                    </template>
                                                </v-tooltip>
                                                <v-tooltip
                                                    v-if="userRole === 'admin' && isCentralAdmin && isCentralAdminSelected"
                                                    text="Only Super Admin users can assign Admin role for Central Administration"
                                                    location="bottom">
                                                    <template v-slot:activator="{ props }">
                                                        <v-icon v-bind="props" color="warning"
                                                            class="ml-2">mdi-alert-circle</v-icon>
                                                    </template>
                                                </v-tooltip>
                                            </template>
                                        </v-select>
                                    </v-col>

                                    <v-col cols="12" md="6"
                                        v-if="userRole === 'super_admin' || (userRole === 'admin' && isCentralAdmin)">
                                        <v-select v-model="form.operating_unit_id" :items="operatingUnits"
                                            item-title="name" item-value="id" label="Operating Unit" required
                                            :error-messages="errors.operating_unit_id" variant="outlined"
                                            bg-color="white" @update:model-value="onOperatingUnitChange"></v-select>
                                    </v-col>

                                    <v-col cols="12" class="justify-end d-flex">
                                        <v-btn type="submit" color="primary" size="large" :loading="form.processing">
                                            Update User
                                        </v-btn>
                                    </v-col>
                                </v-row>
                            </form>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </AppSidebarLayout>
</template>

<script setup>
import { Head, useForm, Link } from '@inertiajs/vue3';
import { computed } from 'vue';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
    user: Object,
    roles: Array,
    operatingUnits: Array,
    userRole: String,
    errors: Object,
    auth: Object,
});

// Check if the current admin user belongs to Central Administration
const isCentralAdmin = computed(() => {
    if (!props.auth || !props.auth.user || !props.auth.user.operatingUnit) {
        return false;
    }

    const operatingUnit = props.auth.user.operatingUnit;
    return operatingUnit.name === 'Central Administration' ||
        operatingUnit.code === 'CENTRAL' ||
        operatingUnit.code === 'CA';
});

// Check if the selected operating unit is Central Administration
const isCentralAdminSelected = computed(() => {
    if (!form.operating_unit_id) {
        return false;
    }

    const selectedUnit = props.operatingUnits.find(unit => unit.id === form.operating_unit_id);
    if (!selectedUnit) {
        return false;
    }

    return selectedUnit.name === 'Central Administration' ||
        selectedUnit.code === 'CENTRAL' ||
        selectedUnit.code === 'CA';
});

// Filter roles based on user role and selected operating unit
const filteredRoles = computed(() => {
    // Super Admin can assign any role except super_admin
    if (props.userRole === 'super_admin') {
        return props.roles;
    }

    // Admin from Central Administration
    if (props.userRole === 'admin' && isCentralAdmin.value) {
        // If Central Administration is selected as the operating unit,
        // only show staff role (Admin role is restricted to Super Admin)
        if (isCentralAdminSelected.value) {
            return props.roles.filter(role => role.slug === 'staff');
        }
        // For other operating units, show both admin and staff roles
        return props.roles;
    }

    // Regular admin can only assign staff role
    return props.roles.filter(role => role.slug === 'staff');
});

const form = useForm({
    name: props.user.name,
    email: props.user.email,
    password: '',
    password_confirmation: '',
    role_id: props.user.role_id,
    operating_unit_id: props.user.operating_unit_id,
    _method: 'PUT',
});

// Handle operating unit change
const onOperatingUnitChange = () => {
    // If Central Administration is selected and user is not Super Admin,
    // reset role selection if it was set to admin
    if (isCentralAdminSelected.value && props.userRole === 'admin') {
        const adminRole = props.roles.find(role => role.slug === 'admin');
        if (adminRole && form.role_id === adminRole.id) {
            form.role_id = '';
        }
    }
};

const submit = () => {
    form.post(`/users/${props.user.id}`);
};
</script>

<style scoped>
:deep(.v-field__input) {
    color: rgba(0, 0, 0, 0.87) !important;
}
</style>
