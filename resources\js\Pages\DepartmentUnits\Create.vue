<template>
  <Head title="Department Units" />
  <AppSidebarLayout>
    <v-container class="">
      <v-row>
        <v-col cols="12" class="d-flex justify-space-between align-center">
          <div class="text-h5">Add a New Department Unit</div>
        </v-col>
      </v-row>
      <v-row class="d-flex justify-center">
        <v-col>
          <v-card elevation="2">
            <v-card-title class="text-white bg-primary"> Details </v-card-title>
            <v-card-text>
              <v-form @submit.prevent="submit">
              <v-row class="ma-0 mt-4 pa-0">
                <v-col
                  cols="2"
                  class="text-black text-subtitle-1"
                >
                  Department Name
                </v-col>
                <v-col>
                  <v-text-field
                    required
                    v-model="form.name"
                    variant="outlined"
                    bg-color="white"
                    color="primary"
                    density="compact"
                    hide-details
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row class="ma-0 pa-0">
                <v-col
                  cols="2"
                  class="text-black text-subtitle-1"
                >
                  Department Code
                </v-col>
                <v-col>
                  <v-text-field
                    required
                    v-model="form.code"
                    variant="outlined"
                    bg-color="white"
                    color="primary"
                    density="compact"
                    hide-details
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row class="ma-0 pa-0">
                <v-col class="d-flex justify-end">
                  <v-btn
                  type="submit"
                  class="bg-blue-lighten-1"
                  :loading="form.processing">
                    Create Department Unit
                  </v-btn>
                </v-col>
              </v-row>
              <!-- <v-row class="ma-0 pa-0">
                <v-col
                  cols="2"
                  class="text-black text-subtitle-1"
                >
                  Department Email
                </v-col>
                <v-col>
                  <v-text-field
                    required
                    v-model="form.name"
                    variant="outlined"
                    bg-color="white"
                    color="primary"
                    density="compact"
                    hide-details
                  >
                  </v-text-field>
                </v-col>
              </v-row> -->
              
              </v-form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </AppSidebarLayout>
</template>


<script setup>
  
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import { router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const route = window.route;

const props = defineProps({
    errors: Object
});

const form = useForm({
    name: '',
    code: '',
    email: '',
});

const submit = () => {
  form.post(route('department-units.store'), {
    onSuccess: ()=> {
      form.reset();
    }
  });
}

</script>
<style scoped>
</style>