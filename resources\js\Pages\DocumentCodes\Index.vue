<template>
  <Head title="Document Codes" />
  <AppSidebarLayout>
    <v-container>
      <v-row>
        <!-- Left side: Create Document Code Form -->
        <v-col cols="12" md="5" lg="4">
          <v-card elevation="2" class="h-100">
            <v-card-title class="text-white bg-primary">
              <v-icon class="mr-2">mdi-plus-circle</v-icon>
              Create Document Code
            </v-card-title>
            <v-card-text class="pt-4">
              <form @submit.prevent="submitForm">
                <v-row>
                  <v-col cols="12">
                    <v-text-field
                      v-model="newCode"
                      label="Document Code"
                      hint="Enter a unique code (e.g., MEMO, LETTER)"
                      persistent-hint
                      variant="outlined"
                      density="comfortable"
                      bg-color="white"
                    ></v-text-field>
                    <v-btn
                      @click.prevent="addCode"
                      class="mt-2"
                      color="secondary"
                      small
                    >
                      Add Code
                    </v-btn>
                  </v-col>

                  <!-- <p
                    v-if="alertMessage"
                    style="color: red"
                    class="ml-3 text-bold-medium"
                  >
                    {{ alertMessage }}
                  </p> -->
                  <v-col cols="12">
                    <div v-if="codeValues.length">
                      <p class="font-weight-bold">Codes to Save:</p>
                      <v-chip
                        v-for="(code, index) in codeValues"
                        :key="code"
                        class="ma-1"
                        closable
                        @click:close="removeCode(index)"
                      >
                        {{ code }}
                      </v-chip>
                    </div>
                  </v-col>

                  <v-col cols="12" class="justify-end d-flex">
                    <v-btn
                      type="submit"
                      color="primary"
                      size="large"
                      :loading="form.processing"
                      prepend-icon="mdi-content-save"
                    >
                      Save Document Code(s)
                    </v-btn>
                  </v-col>
                </v-row>
              </form>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- Right side: Document Codes Table -->
        <v-col cols="12" md="7" lg="8">
          <v-card elevation="2" class="h-100">
            <v-card-title class="flex-wrap mt-2 d-flex align-center pa-4">
              <v-spacer></v-spacer>
              <div style="width: 300px">
                <v-text-field
                  v-model="search"
                  append-icon="mdi-magnify"
                  label="Search document codes..."
                  single-line
                  hide-details
                  variant="outlined"
                  density="compact"
                ></v-text-field>
              </div>
            </v-card-title>
            <v-data-table
              :headers="headers"
              :items="code_data"
              :search="search"
              :loading="loading"
              density="compact"
              fixed-header
              hover
              :items-per-page="itemsPerPage"
              :items-per-page-options="itemsPerPageOptions"
            >
              <template v-slot:item.actions="{ item }">
                <v-btn icon color="info" @click="openEditModal(item)">
                  <v-icon>mdi-pencil</v-icon>
                </v-btn>
                <v-btn icon color="error" @click="confirmDelete(item)">
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </template>
            </v-data-table>

            <v-dialog v-model="modalVisible" max-width="400">
              <v-card>
                <v-card-title>Edit Document Code</v-card-title>
                <v-card-text>
                  <v-text-field
                    v-model="editInput"
                    label="Document Code"
                    outlined
                    dense
                    autofocus
                  />
                </v-card-text>
                <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn text @click="closeModal">Cancel</v-btn>
                  <v-btn color="primary" text @click="saveEdit">Save</v-btn>
                </v-card-actions>
              </v-card>
            </v-dialog>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="deleteDialog" max-width="500">
      <v-card>
        <v-card-title class="text-white text-h5 bg-error"
          >Confirm Delete</v-card-title
        >
        <v-card-text class="pt-4">
          <p>Are you sure you want to delete this document code?</p>
          <p v-if="documentCodeToDelete" class="mt-2 font-weight-medium">
            Document Code: {{ documentCodeToDelete.document_code_key }}
          </p>
          <p class="mt-4 text-error font-weight-bold">
            This action cannot be undone.
          </p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="deleteDialog = false"
          >
            Cancel
          </v-btn>
          <v-btn
            color="error"
            variant="text"
            @click="deleteDocumentCode(documentCodeToDelete.document_code_key)"
            :loading="deleting"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Success Alert -->
    <v-snackbar
      v-model="showSuccessAlert"
      color="success"
      timeout="3000"
      location="top"
    >
      {{ alertMessage }}
      <template v-slot:actions>
        <v-btn variant="text" @click="showSuccessAlert = false"> Close </v-btn>
      </template>
    </v-snackbar>

    <!-- Error Alert -->
    <v-snackbar
      v-model="showErrorAlert"
      color="error"
      timeout="5000"
      location="top"
    >
      {{ errorMessage }}
      <template v-slot:actions>
        <v-btn variant="text" @click="showErrorAlert = false"> Close </v-btn>
      </template>
    </v-snackbar>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import { router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
    documentCodes: Object,
    flash: Object,
});

const search = ref('');
const loading = ref(false);
const deleteDialog = ref(false);
const documentCodeToDelete = ref(null);
const deleting = ref(false);
const showSuccessAlert = ref(false);
const showErrorAlert = ref(false);
const alertMessage = ref('');
const errorMessage = ref('');
const itemsPerPage = ref(props.documentCodes.per_page === -1 ? -1 : (props.documentCodes.per_page || 10));
const itemsPerPageOptions = [
    { value: 5, title: '5' },
    { value: 10, title: '10' },
    { value: 25, title: '25' },
    { value: 50, title: '50' },
    { value: -1, title: 'All' },
];

// Check for flash messages from the server
if (props.flash && props.flash.success) {
    alertMessage.value = props.flash.success;
    showSuccessAlert.value = true;
}

if (props.flash && props.flash.error) {
    errorMessage.value = props.flash.error;
    showErrorAlert.value = true;
}


const newCode = ref('');
const codeValues = ref([]);
const form = useForm({
  key: 'document_code_key',
  value: ''
});

const editInput = ref('');
const originalCode = ref('');
const editingItem = ref(null); // the whole row item
const modalVisible = ref(false);    

const openEditModal = (item) => {
  editingItem.value = item;
  editInput.value = item.document_code_key;
  originalCode.value = item.document_code_key;
  modalVisible.value = true;
};

const closeModal = () => {
  modalVisible.value = false;
  editingItem.value = null;
  originalCode.value = '';
  editInput.value = '';
};

const saveEdit = () => {
  if (!editInput.value.trim()) {
    alert('Please enter a valid code');
    return;
  }

  // Assuming `editingItem.value` contains the row being edited
  const oldCode = originalCode.value;
  const oldCodeKey = 'document_code_key';
  const newCodeValue = editInput.value.trim();
  
  // alert(originalCode.value);
  router.put(route('document-codes.update', { document_code: oldCode }), {
    key: oldCodeKey, // e.g., 'document_code_key'
    old_code: oldCode,
    new_code: newCodeValue,
  }, {
    onSuccess: () => {
      alertMessage.value = 'Document code updated successfully!';
      showSuccessAlert.value = true;
      window.location.reload();

      // Optionally update local table data after successful update
      editingItem.value.operating_unit_code_value = newCodeValue;
      editingItem.value = null;
      editInput.value = '';
    },
    onError: (errors) => {
      errorMessage.value = errors.message || 'Failed to update document code.';
      showErrorAlert.value = true;
    }
  });
};

const confirmDelete = (item) => {
    documentCodeToDelete.value = item;
    deleteDialog.value = true;
};



const jsonDecoder = (value) => {
  try {
    return JSON.parse(value);
  } catch (e) {
    console.error('Error parsing JSON:', e);
    return value; // Return original value if parsing fails
  }
};

const rawCodes = jsonDecoder(props.documentCodes.document_code_key || '[]');

const code_data = rawCodes.map(code => ({
  document_code_key: code,  // matches the header value key
  actions: '', // placeholder for action buttons, if any
}));

//  For Debugging Only
// alert(jsonDecoder(props.documentCodes.document_code_key));

const addCode = () => {
  const trimmedCode = newCode.value.trim();

  if (!trimmedCode) return; // ignore empty

  if (codeValues.value.includes(trimmedCode)) {
    alertMessage.value = 'This code is already added.';
    return;
  }

  // Clear alert if no duplicate
  alertMessage.value = '';

  codeValues.value.push(trimmedCode);
  newCode.value = '';
};
const removeCode = (index) => {
  codeValues.value.splice(index, 1);
};  

// Headers for the data table

const headers = [
  { title: 'Document Code', value: 'document_code_key', width: '200px', align: 'start' },
  { title: 'Actions', value: 'actions', sortable: false, width: '120px', align: 'center' },
];
// Handle table options update (pagination, sorting, etc.)
const handleTableOptionsUpdate = (options) => {
    if (options.itemsPerPage !== itemsPerPage.value) {
        itemsPerPage.value = options.itemsPerPage;
        // Reload the page with the new per_page parameter
        router.visit(route('document-codes.index', { per_page: options.itemsPerPage }), {
            preserveState: true,
            preserveScroll: true,
            only: ['documentCodes'],
        });
    }
};

const submitForm = () => {
  form.value = JSON.stringify(codeValues.value); // Set JSON string as value

  form.post(route('document-codes.store'), {
    onSuccess: () => {
      alertMessage.value = 'Document code(s) saved!';
      codeValues.value = [];
      showSuccessAlert.value = true;

      window.location.reload();
    },
    onError: (errors) => {
      console.error('Form errors:', errors);
    }
  });
};



const deleteDocumentCode = (code) => {
  deleting.value = true;

  // alert(code); // For debugging, remove in production

  router.delete(route('document-codes.destroy'), {
    data: {
      key: 'document_code_key',     // e.g., 'document_code_key'
      code: code
    },
    onSuccess: () => {
      deleteDialog.value = false;
      deleting.value = false;
      alertMessage.value = 'Document code deleted successfully!'; 
      showSuccessAlert.value = true;
      documentCodeToDelete.value = null;
      window.location.reload();
    },
    onError: (errors) => {
      deleting.value = false;
      errorMessage.value = errors.message || 'Failed to delete document code. It may be in use.';
      showErrorAlert.value = true;
    },
  });
};
</script>

<style scoped>
/* Table header styling */
:deep(.v-data-table-header th) {
  font-weight: bold;
  color: rgba(0, 0, 0, 0.87) !important;
}

:deep(.v-data-table .v-data-table__td) {
  color: rgba(0, 0, 0, 0.87) !important;
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

/* Remove blue focus outline from input fields */
:deep(.v-field__input:focus-visible) {
  outline: none !important;
}

/* Table row hover styling */
:deep(.v-data-table .v-data-table__tr:hover) {
  background-color: rgba(25, 118, 210, 0.05) !important;
}

/* Pagination styling */
:deep(.v-data-table-footer) {
  border-top: thin solid rgba(0, 0, 0, 0.12);
  padding: 0 16px;
}

:deep(.v-data-table-footer__items-per-page) {
  margin-right: 16px;
}

:deep(.v-data-table-footer__info) {
  margin-right: 16px;
}

/* Button styling */
:deep(.v-btn--icon.v-btn--density-default) {
  width: 36px;
  height: 36px;
}

/* Tooltip styling */
:deep(.v-tooltip > .v-overlay__content) {
  background-color: rgba(33, 33, 33, 0.9);
  color: white;
  border-radius: 4px;
  font-size: 0.75rem;
  padding: 4px 8px;
}
</style>
