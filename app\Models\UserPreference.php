<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserPreference extends Model
{
    // Specify the table name (optional if it follows Lara<PERSON> convention)
    use HasFactory;
    
    protected $table = 'user_preferences';

    // Fields that can be mass-assigned
    protected $fillable = [
        'user_id',
        'preference_key',
        'preference_value',
    ];

    // Cast preference_value to array automatically (if you store JSON)
    protected $casts = [
        'preference_value' => 'array',
    ];

    // Define relationship to User model
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
