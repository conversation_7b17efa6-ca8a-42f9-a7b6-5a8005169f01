<template>

  <Head title="Document Code Details" />
  <AppSidebarLayout>
    <v-container>
      <v-row>
        <v-col cols="12" class="d-flex align-center">
          <Link :href="route('document-codes.index')" class="text-decoration-none me-4">
          <v-btn icon color="primary">
            <v-icon>mdi-arrow-left</v-icon>
          </v-btn>
          </Link>
          <h1 class="text-h5">Document Code Details</h1>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" md="8" lg="6">
          <v-card elevation="2">
            <v-card-text>
              <v-row>
                <v-col cols="12">
                  <div class="text-subtitle-1 font-weight-bold">Document Code</div>
                  <div class="text-body-1">{{ documentCode.docu_code }}</div>
                </v-col>

                <v-col cols="12">
                  <div class="text-subtitle-1 font-weight-bold">Description</div>
                  <div class="text-body-1">{{ documentCode.description }}</div>
                </v-col>

                <v-col cols="12">
                  <div class="text-subtitle-1 font-weight-bold">Status</div>
                  <v-chip :color="documentCode.is_active ? 'success' : 'error'" size="small" class="text-white">
                    {{ documentCode.is_active ? 'Active' : 'Inactive' }}
                  </v-chip>
                </v-col>

                <v-col cols="12">
                  <div class="text-subtitle-1 font-weight-bold">Created At</div>
                  <div class="text-body-1">{{ formatDate(documentCode.created_at) }}</div>
                </v-col>

                <v-col cols="12">
                  <div class="text-subtitle-1 font-weight-bold">Last Updated</div>
                  <div class="text-body-1">{{ formatDate(documentCode.updated_at) }}</div>
                </v-col>

                <v-col cols="12" class="d-flex justify-end">
                  <Link :href="route('document-codes.edit', { document_code: documentCode.docu_code })"
                    class="text-decoration-none">
                  <v-btn color="primary" prepend-icon="mdi-pencil">
                    Edit
                  </v-btn>
                  </Link>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
  documentCode: Object,
});

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleString();
};
</script>
