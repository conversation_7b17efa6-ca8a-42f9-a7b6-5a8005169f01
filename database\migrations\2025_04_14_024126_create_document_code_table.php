<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('document_codes')) {
            Schema::create('document_codes', function (Blueprint $table) {
                $table->id();
                $table->foreignId('operating_unit_id')->constrained()->onDelete('cascade');
                $table->string('operating_unit_code_key');
                $table->text('operating_unit_code_value')->nullable();
                $table->timestamps();

                $table->unique(['operating_unit_id', 'operating_unit_code_key'], 'operating_unit_code_unique');
            });

            // Seed the table with common document codes
            // $this->seedDocumentCodes();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_codes');
    }

    /**
     * Seed the document_code table with actual data from the current database.
     *
     * This includes the exact document codes that are currently in use.
     */
   /*  private function seedDocumentCodes(): void
    {
        // Actual document codes from the database
        $documentCodes = [
            "201",
            "ALUMNI",
            "AO",
            "AUX",
            "BAO",
            "BOARD RES",
            "CAS",
            "CE",
            "CGS",
            "CHANC",
            "CIT",
            "COA",
            "COE",
            "COL",
            "COM",
            "COT",
            "CULTURAL",
            "FAD",
            "FIN",
            "HI",
            "HRMO",
            "ICJE",
            "INFRA",
            "INTERNATIONAL",
            "LIB",
            "MED",
            "MIS",
            "NSTP",
            "NTA",
            "OUS",
            "PRES",
            "QA",
            "R&E",
            "RECORDS",
            "REGISTRAR",
            "RESOURCE",
            "SAS/SBO",
            "SECURITY",
            "SPORTS",
            "SPROJ",
            "SUPPLY",
            "TRAV",
        ];

        // Insert the document codes
        foreach ($documentCodes as $code) {
            DB::table('document_code')->insert([
                'docu_code' => $code,
            ]);
        }
    } */
};
