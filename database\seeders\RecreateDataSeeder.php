<?php

namespace Database\Seeders;

use App\Models\DocumentCode;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Inbox;
use App\Models\Role;
use App\Models\OperatingUnit;
use App\Models\UserPreference;
use Illuminate\Support\Facades\Hash;

class RecreateDataSeeder extends Seeder
{
    /**
     * Run the database seeds to recreate lost data.
     */
    public function run(): void
    {
        // Create roles if they don't exist
        $this->createRoles();

        // Create operating units if they don't exist
        $this->createOperatingUnits();

        // Create users
        $users = $this->createUsers();

        // Create inbox messages
        $this->createInboxMessages($users);

        $this->command->info('Data has been recreated successfully!');
    }

    /**
     * Create roles
     */
    private function createRoles(): void
    {
        $roles = [
            ['name' => 'Super Admin', 'slug' => 'super-admin'],
            ['name' => 'Admin', 'slug' => 'admin'],
            ['name' => 'Staff', 'slug' => 'staff'],
        ];

        foreach ($roles as $role) {
            Role::firstOrCreate(
                ['slug' => $role['slug']],
                ['name' => $role['name']]
            );
        }

        $this->command->info('Roles created successfully');
    }

    /**
     * Create operating units
     */
    private function createOperatingUnits(): void
    {
        $operatingUnits = [
            // ['name' => 'Central Administration', 'code' => 'CA'],
            ['name' => 'North La Union Campus', 'code' => 'NLUC',  'email' => '<EMAIL>',],
            ['name' => 'Mid-La Union Campus', 'code' => 'MLUC',  'email' => '<EMAIL>',],
            ['name' => 'South La Union Campus', 'code' => 'SLUC',  'email' => '<EMAIL>',],
            ['name' => 'Open University System', 'code' => 'OUS',  'email' => '<EMAIL>',],
            ['name' => 'National Agriculture Research, Training and Development Institute', 'code' => 'NARTDI',  'email' => '<EMAIL>',],
            ['name' => 'Sericulture Research and Development Institute', 'code' => 'SRDI',  'email' => '<EMAIL>',],
        ];

        foreach ($operatingUnits as $unit) {
            OperatingUnit::firstOrCreate(
                ['name' => $unit['name']],
                [
                    'code' => $unit['code'],
                    'email' => $unit['email'],
                ]
            );
        }

        $this->command->info('Operating units created successfully');
    }

    /**
     * Create users
     */
    private function createUsers(): array
    {
        // Get roles
        $superAdminRole = Role::where('slug', 'super-admin')->first();
        $adminRole = Role::where('slug', 'admin')->first();
        $staffRole = Role::where('slug', 'staff')->first();

        // Get operating units
        // $centralAdmin = OperatingUnit::where('code', 'CA')->first();
        $NLUCOperatingUnit = OperatingUnit::where('code', 'NLUC')->first();
        $MLUCOperatingUnit = OperatingUnit::where('code', 'MLUC')->first();
        $SLUCOperatingUnit = OperatingUnit::where('code', 'SLUC')->first();
        $OUSOperatingUnit = OperatingUnit::where('code', 'OUS')->first();
        $NARTDIOperatingUnit = OperatingUnit::where('code', 'NARTDI')->first();
        $SRDIOperatingUnit = OperatingUnit::where('code', 'SRDI')->first();

        // Create users
        $users = [
            /*             // Central Admin
            [
                'name' => 'CA Records',
                'email' => '<EMAIL>',
                'password' => Hash::make('Ilovedmmmsu_01'),
                'role_id' => $superAdminRole->id,
                'operating_unit_id' => $centralAdmin->id,
            ], */

            // NLUC Records
            [
                'name' => 'NLUC Records',
                'email' => '<EMAIL>',
                'password' => Hash::make('Ilovedmmmsu_01'),
                'role_id' => $adminRole->id,
                'operating_unit_id' => $NLUCOperatingUnit->id,
            ],

            // MLUC Records
            [
                'name' => 'MLUC Records',
                'email' => '<EMAIL>',
                'password' => Hash::make('Ilovedmmmsu_01'),
                'role_id' => $adminRole->id,
                'operating_unit_id' => $MLUCOperatingUnit->id,
            ],

            // SLUC Records
            [
                'name' => 'SLUC Records',
                'email' => '<EMAIL>',
                'password' => Hash::make('Ilovedmmmsu_01'),
                'role_id' => $adminRole->id,
                'operating_unit_id' => $SLUCOperatingUnit->id,
            ],

            // OUS Records
            [
                'name' => 'OUS Records',
                'email' => '<EMAIL>',
                'password' => Hash::make('Ilovedmmmsu_01'),
                'role_id' => $adminRole->id,
                'operating_unit_id' => $OUSOperatingUnit->id,
            ],

            // NARTDI Records
            [
                'name' => 'NARTDI Records',
                'email' => '<EMAIL>',
                'password' => Hash::make('Ilovedmmmsu_01'),
                'role_id' => $adminRole->id,
                'operating_unit_id' => $NARTDIOperatingUnit->id,
            ],

            // SRDI Records
            [
                'name' => 'SRDI Records',
                'email' => '<EMAIL>',
                'password' => Hash::make('Ilovedmmmsu_01'),
                'role_id' => $adminRole->id,
                'operating_unit_id' => $SRDIOperatingUnit->id,
            ],
        ];

        $createdUsers = [];

        foreach ($users as $userData) {
            $user = User::firstOrCreate(
                ['email' => $userData['email']],
                [
                    'name' => $userData['name'],
                    'password' => $userData['password'],
                    'role_id' => $userData['role_id'],
                    'operating_unit_id' => $userData['operating_unit_id'],
                ]
            );

            UserPreference::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'preference_key' => 'document_column_arrangement', // example key
                ],
                [
                    'preference_value' => json_encode([
                        'control_number',
                        'reference_document',
                        'signatory',
                        'received_by.name',
                        'origin',
                        'document_code',
                        'subject_matter',
                        'referred_to',
                        'date_received',
                        'time_received',
                        'status'
                    ]), // example default preference value
                ]
            );

            DocumentCode::create([
                'operating_unit_id' => $userData['operating_unit_id'],
                'operating_unit_code_key' => 'document_code_key',
                'operating_unit_code_value' => 'N/A',
            ]);

            $createdUsers[] = $user;
        }

        $this->command->info('Users created successfully');

        return $createdUsers;
    }

    /**
     * Create inbox messages
     */
    private function createInboxMessages(array $users): void
    {
        // Create inbox messages with different references
        $references = ['FO1', 'FO2', 'FO3', 'FO4'];
        $priorities = ['routinary', 'urgent'];

        foreach ($users as $sender) {
            foreach ($users as $recipient) {
                if ($sender->id !== $recipient->id) {
                    continue;
                }

                foreach ($references as $reference) {
                    $priority = $priorities[array_rand($priorities)];

                    Inbox::create([
                        'subject' => "Test message with reference {$reference}",
                        'message' => "This is a test message with reference {$reference} from {$sender->name} to {$recipient->name}.",
                        'sender_id' => $sender->id,
                        'recipient_id' => $recipient->id,
                        'reference' => $reference,
                        'is_read' => rand(0, 1) === 1,
                        'priority' => $priority,
                    ]);
                }
            }
        }

        $this->command->info('Inbox messages created successfully');
    }
}
