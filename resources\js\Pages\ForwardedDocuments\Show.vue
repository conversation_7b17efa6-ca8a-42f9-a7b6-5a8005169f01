<template>
    <Head :title="`Forwarded Message: ${message.subject}`" />

    <AppSidebarLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">View Forwarded Message</h2>
        </template>

        <v-container>
            <v-row>
                <v-col cols="12" class="justify-end d-flex">
                    <Link href="/forwarded-documents" class="text-decoration-none">
                        <v-btn color="primary" variant="outlined" prepend-icon="mdi-arrow-left">
                            Back to Forwarded Documents
                        </v-btn>
                    </Link>
                </v-col>
            </v-row>

            <v-row>
                <v-col cols="12">
                    <v-card>
                        <v-card-title class="bg-primary text-white d-flex justify-space-between align-center">
                            <div>
                                <div class="text-h5">{{ message.subject }}</div>
                                <div class="text-subtitle-1 mt-1">
                                    <v-chip size="small" :color="getPriorityColor(message.priority)" class="mr-2">
                                        {{ message.priority }}
                                    </v-chip>
                                    <span v-if="message.document">
                                        <v-chip size="small" color="info" class="mr-2">
                                            {{ message.document.reference_document ? message.document.reference_document.replace('FO', 'F0') : '' }}
                                        </v-chip>
                                        {{ message.document.control_number }}
                                    </span>
                                </div>
                            </div>
                            <div>
                                <v-btn icon variant="text" color="white" @click="printMessage">
                                    <v-icon>mdi-printer</v-icon>
                                </v-btn>
                            </div>
                        </v-card-title>

                        <v-card-text class="pa-4">
                            <div class="d-flex justify-space-between align-center mb-4 pb-4 border-b">
                                <div>
                                    <div class="text-subtitle-1 font-weight-bold">
                                        To: {{ message.recipient ? message.recipient.name : 'Unknown' }}
                                    </div>
                                    <div class="text-caption text-grey-darken-1">
                                        {{ message.recipient ? message.recipient.email : '' }}
                                    </div>
                                </div>
                                <div class="text-caption text-grey-darken-1">
                                    {{ formatDate(message.created_at) }}
                                </div>
                            </div>

                            <div v-if="message.document" class="mb-4 pb-4 border-b">
                                <div class="text-subtitle-1 font-weight-bold mb-2">Attached Document</div>
                                <v-card variant="outlined" class="pa-3">
                                    <div class="d-flex align-center">
                                        <v-icon color="primary" class="mr-2">mdi-file-document-outline</v-icon>
                                        <div>
                                            <div class="text-subtitle-2 font-weight-bold">
                                                {{ message.document.control_number }}
                                            </div>
                                            <div class="text-caption">
                                                {{ message.document.subject_matter }}
                                            </div>
                                        </div>
                                        <v-spacer></v-spacer>
                                        <Link :href="route('documents.show', message.document.id)" class="text-decoration-none">
                                            <v-btn color="primary" size="small" variant="text">
                                                View Document
                                            </v-btn>
                                        </Link>
                                    </div>
                                </v-card>
                            </div>

                            <div class="message-content">
                                <div class="text-subtitle-1 font-weight-bold mb-2">Message</div>
                                <div class="text-body-1 message-body">
                                    {{ message.message }}
                                </div>
                            </div>
                        </v-card-text>

                        <v-card-actions class="pa-4 pt-0">
                            <v-spacer></v-spacer>
                            <Link :href="route('inbox.create')" class="text-decoration-none">
                                <v-btn color="primary" prepend-icon="mdi-email-plus-outline">
                                    Compose New Message
                                </v-btn>
                            </Link>
                        </v-card-actions>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </AppSidebarLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
    message: Object,
});

const getPriorityColor = (priority) => {
    switch (priority) {
        case 'high':
            return 'error';
        case 'medium':
            return 'warning';
        case 'low':
            return 'success';
        default:
            return 'grey';
    }
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

const printMessage = () => {
    window.print();
};
</script>

<style scoped>
.border-b {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.message-body {
    white-space: pre-wrap;
    line-height: 1.6;
}

@media print {
    .v-btn {
        display: none !important;
    }
}
</style>
