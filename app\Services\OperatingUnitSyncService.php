<?php

namespace App\Services;

use App\Models\Document;
use App\Models\DocumentTracking;
use App\Models\DocumentTrackingHistory;
use App\Models\Inbox;
use App\Models\OperatingUnit;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class OperatingUnitSyncService
{
    /**
     * Cache key prefix for operating unit data
     */
    const CACHE_PREFIX = 'operating_unit_';

    /**
     * Cache TTL in seconds (1 hour)
     */
    const CACHE_TTL = 3600;

    /**
     * Get all users in the same operating unit
     *
     * @param int $operatingUnitId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUsersInOperatingUnit(int $operatingUnitId)
    {
        return User::where('operating_unit_id', $operatingUnitId)->get();
    }

    /**
     * Get documents for an operating unit
     *
     * @param int $operatingUnitId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDocumentsForOperatingUnit(int $operatingUnitId)
    {
        $cacheKey = self::CACHE_PREFIX . 'documents_' . $operatingUnitId;

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($operatingUnitId) {
            return Document::where('origin_unit_id', $operatingUnitId)
                ->orWhere('current_unit_id', $operatingUnitId)
                ->orWhereHas('receivedBy', function ($query) use ($operatingUnitId) {
                    $query->where('origin_unit_id', $operatingUnitId);
                })
                ->get();
        });
    }

    /**
     * Get inbox messages for an operating unit
     *
     * @param int $operatingUnitId
     * @param string|null $reference Filter by reference document type
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getInboxMessagesForOperatingUnit(int $operatingUnitId, ?string $reference = null)
    {
        // Build the query to get inbox messages
        $query = Inbox::with(['sender', 'document'])
            ->whereHas('recipient', function ($query) use ($operatingUnitId) {
                $query->where('operating_unit_id', $operatingUnitId);
            });

        // Only apply reference filter if a specific reference is provided
        if ($reference) {
            $query->where('reference', $reference);
            \Log::info('Applying reference filter to query', [
                'operating_unit_id' => $operatingUnitId,
                'reference' => $reference
            ]);
        } else {
            \Log::info('No reference filter applied, retrieving ALL messages', [
                'operating_unit_id' => $operatingUnitId
            ]);
        }

        // Get the results ordered by creation date (newest first)
        $results = $query->orderBy('created_at', 'desc')->get();

        \Log::info('Retrieved inbox messages from database', [
            'count' => $results->count(),
            'operating_unit_id' => $operatingUnitId,
            'reference_filter' => $reference ?? 'All',
            'sql_query' => $query->toSql(),
            'sql_bindings' => $query->getBindings(),
            'first_few_messages' => $results->take(3)->map(function ($message) {
                return [
                    'id' => $message->id,
                    'subject' => $message->subject,
                    'reference' => $message->reference,
                    'recipient_id' => $message->recipient_id
                ];
            })
        ]);

        return $results;
    }

    /**
     * Get document tracking history for an operating unit
     *
     * @param int $operatingUnitId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDocumentTrackingForOperatingUnit(int $operatingUnitId)
    {
        $cacheKey = self::CACHE_PREFIX . 'tracking_' . $operatingUnitId;

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($operatingUnitId) {
            return DocumentTrackingHistory::with(['document'])
                ->whereHas('handledBy', function ($query) use ($operatingUnitId) {
                    $query->where('operating_unit_id', $operatingUnitId);
                })
                ->get();
        });
    }

    /**
     * Get forwarded documents for an operating unit
     *
     * @param int $operatingUnitId
     * @param string|null $reference Filter by reference document type
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getForwardedDocumentsForOperatingUnit(int $operatingUnitId, ?string $reference = null)
    {
        $cacheKey = self::CACHE_PREFIX . 'forwarded_' . $operatingUnitId;
        if ($reference) {
            $cacheKey .= '_' . $reference;
        }

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($operatingUnitId, $reference) {
            $query = DocumentTrackingHistory::with(['document'])
                ->whereHas('handledBy', function ($query) use ($operatingUnitId) {
                    $query->where('operating_unit_id', $operatingUnitId);
                })
                ->where('status', 'forwarded');

            if ($reference) {
                // Use F01 format directly (with zero, not letter O)
                $query->whereHas('document', function ($q) use ($reference) {
                    $q->where('reference_document', $reference);
                });
            }

            return $query->orderBy('created_at', 'desc')->get();
        });
    }

    /**
     * Clear cache for an operating unit
     *
     * @param int $operatingUnitId
     * @return void
     */
    public function clearOperatingUnitCache(int $operatingUnitId)
    {
        \Log::info('Clearing cache for operating unit', ['operating_unit_id' => $operatingUnitId]);

        // Clear document cache
        Cache::forget(self::CACHE_PREFIX . 'documents_' . $operatingUnitId);

        // Clear tracking cache
        Cache::forget(self::CACHE_PREFIX . 'tracking_' . $operatingUnitId);

        // Clear forwarded documents cache
        Cache::forget(self::CACHE_PREFIX . 'forwarded_' . $operatingUnitId);

        // Clear reference-specific forwarded document caches
        foreach (['F01', 'F02', 'F03', 'F04'] as $reference) {
            Cache::forget(self::CACHE_PREFIX . 'forwarded_' . $operatingUnitId . '_' . $reference);
        }

        \Log::info('Cache cleared for operating unit', ['operating_unit_id' => $operatingUnitId]);
    }
}
