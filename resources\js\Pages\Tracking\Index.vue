<template>

  <Head title="Tracking" />
  <AppSidebarLayout>
    <v-container>

      <v-row>
        <v-col cols="12">
          <v-card elevation="2">
            <v-card-title class="text-white bg-primary">
              <v-text-field v-model="search" append-icon="mdi-magnify"
                label="Search for Control No., Subject Matter, References, Etc" single-line hide-details
                variant="outlined" bg-color="white" density="compact">
              </v-text-field>
            </v-card-title>
            <v-data-table :headers="headers" density="compact" class="">
            </v-data-table>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, useForm, router } from "@inertiajs/vue3";
import { ref, computed } from "vue";
import AppSidebarLayout from "@/Layouts/AppSidebarLayout.vue";


// Open soon
/* const props = defineProps({
  tracking: Object,
}); */

//  Tracking Table Header
const headers = [
  { title: "Control Number", value: "control_number" },
  { title: "Document Code", value: "document_code" },
  { title: "Document Name", value: "document_name" },
  { title: "Date Created", value: "created_at" },
  { title: "Status", value: "status" },
];

</script>
<style scoped>
:deep(.v-data-table thead) {
  background: #d4d4d4;
}
</style>
