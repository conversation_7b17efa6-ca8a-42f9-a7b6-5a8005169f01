<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Check if column does not exist
        if (!Schema::hasColumn('documents', 'remarks')) {
            Schema::table('documents', function (Blueprint $table) {
                $table->string('remarks')->nullable();
            });
        }
    }

    public function down()
    {
        // Remove the column if exists
        if (Schema::hasColumn('documents', 'remarks')) {
            Schema::table('documents', function (Blueprint $table) {
                $table->dropColumn('remarks');
            });
        }
    }
};
