<template>

    <Head :title="`${archiveFile.original_name} - Records Archive`" />
    <AppSidebarLayout>
        <v-container>
            <v-row>
                <v-col cols="12">
                    <!-- Breadcrumb Navigation -->
                    <v-breadcrumbs class="mb-4 pa-0">
                        <v-breadcrumbs-item>
                            <v-btn variant="text" prepend-icon="mdi-arrow-left" @click="goBack" color="primary">
                                Records Archive
                            </v-btn>
                        </v-breadcrumbs-item>
                        <v-breadcrumbs-divider>/</v-breadcrumbs-divider>
                        <v-breadcrumbs-item v-if="archiveFile.folder">
                            {{ archiveFile.folder.name }}
                        </v-breadcrumbs-item>
                        <v-breadcrumbs-divider v-if="archiveFile.folder">/</v-breadcrumbs-divider>
                        <v-breadcrumbs-item>{{ archiveFile.original_name }}</v-breadcrumbs-item>
                    </v-breadcrumbs>

                    <v-card elevation="2">
                        <v-card-title class="text-white bg-primary d-flex align-center">
                            <v-icon :icon="getFileIcon(archiveFile.original_name)" class="mr-2"></v-icon>
                            {{ archiveFile.original_name }}
                            <v-spacer></v-spacer>
                            <v-chip color="white" text-color="primary" size="small">
                                {{ formatFileSize(archiveFile.size) }}
                            </v-chip>
                        </v-card-title>

                        <v-card-subtitle class="pa-4 bg-grey-lighten-4">
                            <div class="d-flex align-center justify-space-between">
                                <div>
                                    <div class="text-h6 font-weight-medium">{{ archiveFile.original_name }}</div>
                                    <div class="text-caption text-grey-darken-1">
                                        Uploaded: {{ formatDate(archiveFile.created_at) }}
                                    </div>
                                </div>
                                <div class="d-flex align-center gap-2">
                                    <v-btn color="primary" prepend-icon="mdi-folder-move" @click="moveFile">
                                        Move
                                    </v-btn>
                                    <v-btn color="error" prepend-icon="mdi-delete" @click="confirmDelete">
                                        Delete
                                    </v-btn>
                                </div>
                            </div>
                        </v-card-subtitle>

                        <v-card-text class="pa-0">
                            <div v-if="loading" class="py-8 text-center">
                                <v-progress-circular indeterminate color="primary"></v-progress-circular>
                                <div class="mt-2">Loading file content...</div>
                            </div>

                            <div v-else-if="error" class="py-8 text-center">
                                <v-icon size="64" color="error">mdi-alert-circle</v-icon>
                                <div class="mt-4 text-h6 text-error">Error Loading File</div>
                                <div class="text-body-2 text-grey-darken-1">{{ error }}</div>
                            </div>

                            <div v-else>
                                <!-- File Content Display -->
                                <div v-if="fileType === 'csv'" class="pa-4">
                                    <ExcelViewer :file-url="props.fileUrl"
                                        :filename="props.archiveFile.original_name" />
                                </div>
                                <div v-else-if="fileType === 'xlsx' || fileType === 'xls'" class="pa-4">
                                    <ExcelViewer :file-url="props.fileUrl"
                                        :filename="props.archiveFile.original_name" />
                                </div>
                                <div v-else class="pa-4">
                                    <v-card variant="outlined">
                                        <v-card-title class="text-h6">Unsupported File Type</v-card-title>
                                        <v-card-text class="text-center py-8">
                                            <v-icon size="96" color="grey-darken-2">mdi-file-question</v-icon>
                                            <div class="mt-4 text-h6">{{ archiveFile.original_name }}</div>
                                            <div class="mt-2 text-body-2 text-grey-darken-1">
                                                This file type is not supported for preview.
                                            </div>
                                        </v-card-text>
                                    </v-card>
                                </div>
                            </div>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>



        <!-- Move File Dialog -->
        <v-dialog v-model="moveDialog" max-width="500" persistent>
            <v-card>
                <v-card-title class="text-white bg-primary">
                    <v-icon icon="mdi-folder-move" class="mr-2"></v-icon>
                    Move File
                </v-card-title>

                <v-card-text class="pt-4">
                    <div class="mb-4">
                        <h3 class="text-h6 mb-2">File Details</h3>
                        <v-card variant="outlined" class="pa-3">
                            <div class="d-flex align-center">
                                <v-icon icon="mdi-file-document" class="mr-3" color="primary"></v-icon>
                                <div>
                                    <div class="font-weight-medium">{{ archiveFile.original_name }}</div>
                                    <div class="text-caption text-medium-emphasis">
                                        Current location: {{ archiveFile.folder?.name || 'Unknown' }}
                                    </div>
                                </div>
                            </div>
                        </v-card>
                    </div>

                    <div class="mb-4">
                        <h3 class="text-h6 mb-2">Select Destination Folder</h3>
                        <v-select v-model="selectedDestinationFolder" :items="availableFolders" item-title="name"
                            item-value="id" label="Choose destination folder" variant="outlined"
                            :error-messages="moveError" :disabled="moving">
                            <template v-slot:item="{ props, item }">
                                <v-list-item v-bind="props" :disabled="item.raw.id === archiveFile.folder?.id">
                                    <template v-slot:prepend>
                                        <v-icon icon="mdi-folder" class="mr-2"></v-icon>
                                    </template>
                                    <v-list-item-title>{{ item.raw.name }}</v-list-item-title>
                                    <v-list-item-subtitle>{{ item.raw.file_count }} files</v-list-item-subtitle>
                                </v-list-item>
                            </template>
                        </v-select>
                    </div>

                    <v-alert v-if="moveError" type="error" variant="tonal" class="mb-3">
                        {{ moveError }}
                    </v-alert>
                </v-card-text>

                <v-card-actions class="px-4 pb-4">
                    <v-spacer></v-spacer>
                    <v-btn color="grey-darken-1" variant="text" @click="cancelMove" :disabled="moving">
                        Cancel
                    </v-btn>
                    <v-btn color="primary" variant="flat" @click="confirmMove" :loading="moving"
                        :disabled="!selectedDestinationFolder">
                        Move File
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Delete Confirmation Dialog -->
        <v-dialog v-model="deleteDialog" max-width="400" persistent>
            <v-card>
                <v-card-title class="text-white text-h5 bg-error">
                    Delete File
                </v-card-title>
                <v-card-text class="pt-4">
                    <p>Are you sure you want to delete this archive file?</p>
                    <p class="mt-2 text-caption text-grey-darken-1">
                        File: <strong>{{ archiveFile.original_name }}</strong>
                    </p>
                    <p class="mt-2 text-caption text-warning">
                        This action cannot be undone.
                    </p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="grey-darken-1" variant="text" @click="deleteDialog = false">
                        Cancel
                    </v-btn>
                    <v-btn color="error" variant="text" @click="deleteFile" :loading="deleting">
                        Delete
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </AppSidebarLayout>
</template>

<script setup>
import { Head } from "@inertiajs/vue3";
import { ref } from "vue";
import { router } from "@inertiajs/vue3";
import AppSidebarLayout from "@/Layouts/AppSidebarLayout.vue";
import ExcelViewer from "@/Components/ExcelViewer.vue";


const props = defineProps({
    archiveFile: Object,
    fileContent: String,
    fileType: String,
    fileUrl: String,
    availableFolders: Array,
});

// Reactive data
const loading = ref(false);
const error = ref(null);
const moveDialog = ref(false);
const selectedDestinationFolder = ref(null);
const availableFolders = ref([]);
const moving = ref(false);
const moveError = ref('');
const deleteDialog = ref(false);
const deleting = ref(false);

// Methods
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
    });
};

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};



const goBack = () => {
    if (props.archiveFile.folder) {
        router.visit(`/records-archive/folders/${props.archiveFile.folder.id}`);
    } else {
        router.visit('/records-archive');
    }
};

const moveFile = () => {
    selectedDestinationFolder.value = null;
    moveError.value = '';

    // Use folders from props
    availableFolders.value = props.availableFolders || [];

    moveDialog.value = true;
};



const cancelMove = () => {
    moveDialog.value = false;
    selectedDestinationFolder.value = null;
    moveError.value = '';
    moving.value = false;
};

const confirmMove = async () => {
    if (!selectedDestinationFolder.value) return;

    moving.value = true;
    moveError.value = '';

    try {
        const response = await fetch(`/records-archive/files/${props.archiveFile.id}/move`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
            body: JSON.stringify({
                destination_folder_id: selectedDestinationFolder.value,
            }),
        });

        const result = await response.json();

        if (response.ok) {
            moveDialog.value = false;
            alert(result.message || 'File moved successfully!');
            // Navigate to the destination folder
            const destinationFolder = availableFolders.value.find(f => f.id === selectedDestinationFolder.value);
            if (destinationFolder) {
                router.visit(`/records-archive/folders/${destinationFolder.id}`);
            } else {
                router.visit('/records-archive');
            }
        } else {
            moveError.value = result.error || 'Failed to move file';
        }
    } catch (error) {
        console.error('Move file error:', error);
        moveError.value = 'Failed to move file. Please try again.';
    } finally {
        moving.value = false;
    }
};

const confirmDelete = () => {
    deleteDialog.value = true;
};

const deleteFile = async () => {
    deleting.value = true;

    try {
        const response = await fetch(`/records-archive/files/${props.archiveFile.id}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
        });

        if (response.ok) {
            alert('File deleted successfully!');
            router.visit('/records-archive');
        } else {
            const result = await response.json();
            throw new Error(result.message || 'Delete failed');
        }
    } catch (error) {
        console.error('Delete error:', error);
        alert('Failed to delete file. Please try again.');
    } finally {
        deleting.value = false;
        deleteDialog.value = false;
    }
};

const getFileIcon = (filename) => {
    const extension = filename.split('.').pop().toLowerCase();
    switch (extension) {
        case 'xlsx':
        case 'xls':
            return 'mdi-file-excel';
        case 'csv':
            return 'mdi-file-delimited';
        default:
            return 'mdi-file';
    }
};
</script>

<style scoped>
.csv-content {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    white-space: pre-wrap;
    max-height: 400px;
    overflow-y: auto;
}
</style>

<style scoped>
.xml-content {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 500px;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
}

:deep(.v-data-table-header th) {
    font-weight: bold;
    color: rgba(0, 0, 0, 0.87) !important;
    background-color: white !important;
}

:deep(.v-data-table .v-data-table__td) {
    color: rgba(0, 0, 0, 0.87) !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
}
</style>
