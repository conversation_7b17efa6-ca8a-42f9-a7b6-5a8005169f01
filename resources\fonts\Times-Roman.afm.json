{"codeToName": {"32": "space", "160": "space", "33": "exclam", "34": "quotedbl", "35": "numbersign", "36": "dollar", "37": "percent", "38": "ampersand", "146": "quoteright", "40": "parenleft", "41": "parenright", "42": "asterisk", "43": "plus", "44": "comma", "45": "hyphen", "173": "hyphen", "46": "period", "47": "slash", "48": "zero", "49": "one", "50": "two", "51": "three", "52": "four", "53": "five", "54": "six", "55": "seven", "56": "eight", "57": "nine", "58": "colon", "59": "semicolon", "60": "less", "61": "equal", "62": "greater", "63": "question", "64": "at", "65": "A", "66": "B", "67": "C", "68": "D", "69": "E", "70": "F", "71": "G", "72": "H", "73": "I", "74": "J", "75": "K", "76": "L", "77": "M", "78": "N", "79": "O", "80": "P", "81": "Q", "82": "R", "83": "S", "84": "T", "85": "U", "86": "V", "87": "W", "88": "X", "89": "Y", "90": "Z", "91": "bracketleft", "92": "backslash", "93": "bracketright", "94": "asciicircum", "95": "underscore", "145": "quoteleft", "97": "a", "98": "b", "99": "c", "100": "d", "101": "e", "102": "f", "103": "g", "104": "h", "105": "i", "106": "j", "107": "k", "108": "l", "109": "m", "110": "n", "111": "o", "112": "p", "113": "q", "114": "r", "115": "s", "116": "t", "117": "u", "118": "v", "119": "w", "120": "x", "121": "y", "122": "z", "123": "braceleft", "124": "bar", "125": "braceright", "126": "asciitilde", "161": "exclamdown", "162": "cent", "163": "sterling", "165": "yen", "131": "florin", "167": "section", "164": "currency", "39": "<PERSON><PERSON><PERSON>", "147": "quotedblleft", "171": "guillemotleft", "139": "guil<PERSON>lle<PERSON>", "155": "guil<PERSON><PERSON><PERSON>", "150": "endash", "134": "dagger", "135": "daggerdbl", "183": "periodcentered", "182": "paragraph", "149": "bullet", "130": "quotesinglbase", "132": "quotedblbase", "148": "<PERSON><PERSON><PERSON><PERSON>", "187": "guil<PERSON><PERSON><PERSON>", "133": "ellipsis", "137": "perth<PERSON>and", "191": "questiondown", "96": "grave", "180": "acute", "136": "circumflex", "152": "tilde", "175": "macron", "168": "<PERSON><PERSON><PERSON>", "184": "cedilla", "151": "emdash", "198": "AE", "170": "ordfeminine", "216": "<PERSON><PERSON><PERSON>", "140": "OE", "186": "ordmasculine", "230": "ae", "248": "oslash", "156": "oe", "223": "germandbls", "207": "Idieresis", "233": "eacute", "159": "Ydieresis", "247": "divide", "221": "Ya<PERSON>", "194": "Acircumflex", "225": "aacute", "219": "Ucircumflex", "253": "yacute", "234": "ecircumflex", "220": "Udieresis", "218": "Uacute", "203": "Edieresis", "169": "copyright", "229": "aring", "224": "agrave", "227": "atilde", "154": "scaron", "237": "iacute", "251": "ucircumflex", "226": "acircumflex", "231": "ccedilla", "222": "Thorn", "179": "threesuperior", "210": "<PERSON><PERSON>", "192": "<PERSON><PERSON>", "215": "multiply", "250": "uacute", "255": "ydieresis", "238": "icircumflex", "202": "Ecircumflex", "228": "adieresis", "235": "edieresis", "205": "Iacute", "177": "plus<PERSON>us", "166": "brokenbar", "174": "registered", "200": "<PERSON><PERSON>", "142": "<PERSON><PERSON><PERSON>", "208": "Eth", "199": "Ccedilla", "193": "Aacute", "196": "Adieresis", "232": "egrave", "211": "Oacute", "243": "oacute", "239": "idieresis", "212": "Ocircumflex", "217": "<PERSON><PERSON>", "254": "thorn", "178": "twosuperior", "214": "Odieresis", "181": "mu", "236": "igrave", "190": "threequarters", "153": "trademark", "204": "<PERSON><PERSON>", "189": "onehalf", "244": "ocircumflex", "241": "ntilde", "201": "Eacute", "188": "onequarter", "138": "<PERSON><PERSON><PERSON>", "176": "degree", "242": "ograve", "249": "ugrave", "209": "Ntilde", "245": "otilde", "195": "<PERSON><PERSON>", "197": "<PERSON><PERSON>", "213": "<PERSON><PERSON><PERSON>", "206": "Icircumflex", "172": "logicalnot", "246": "odieresis", "252": "udieresis", "240": "eth", "158": "z<PERSON>on", "185": "onesuperior", "128": "Euro"}, "isUnicode": false, "FontName": "Times-Roman", "FullName": "Times Roman", "FamilyName": "Times", "Weight": "Roman", "ItalicAngle": "0", "IsFixedPitch": "false", "CharacterSet": "ExtendedRoman", "FontBBox": ["-168", "-218", "1000", "898"], "UnderlinePosition": "-100", "UnderlineThickness": "50", "Version": "002.00", "EncodingScheme": "WinAnsiEncoding", "CapHeight": "662", "XHeight": "450", "Ascender": "683", "Descender": "-217", "StdHW": "28", "StdVW": "84", "StartCharMetrics": "317", "C": {"32": 250, "160": 250, "33": 333, "34": 408, "35": 500, "36": 500, "37": 833, "38": 778, "146": 333, "40": 333, "41": 333, "42": 500, "43": 564, "44": 250, "45": 333, "173": 333, "46": 250, "47": 278, "48": 500, "49": 500, "50": 500, "51": 500, "52": 500, "53": 500, "54": 500, "55": 500, "56": 500, "57": 500, "58": 278, "59": 278, "60": 564, "61": 564, "62": 564, "63": 444, "64": 921, "65": 722, "66": 667, "67": 667, "68": 722, "69": 611, "70": 556, "71": 722, "72": 722, "73": 333, "74": 389, "75": 722, "76": 611, "77": 889, "78": 722, "79": 722, "80": 556, "81": 722, "82": 667, "83": 556, "84": 611, "85": 722, "86": 722, "87": 944, "88": 722, "89": 722, "90": 611, "91": 333, "92": 278, "93": 333, "94": 469, "95": 500, "145": 333, "97": 444, "98": 500, "99": 444, "100": 500, "101": 444, "102": 333, "103": 500, "104": 500, "105": 278, "106": 278, "107": 500, "108": 278, "109": 778, "110": 500, "111": 500, "112": 500, "113": 500, "114": 333, "115": 389, "116": 278, "117": 500, "118": 500, "119": 722, "120": 500, "121": 500, "122": 444, "123": 480, "124": 200, "125": 480, "126": 541, "161": 333, "162": 500, "163": 500, "fraction": 167, "165": 500, "131": 500, "167": 500, "164": 500, "39": 180, "147": 444, "171": 500, "139": 333, "155": 333, "fi": 556, "fl": 556, "150": 500, "134": 500, "135": 500, "183": 250, "182": 453, "149": 350, "130": 333, "132": 444, "148": 444, "187": 500, "133": 1000, "137": 1000, "191": 444, "96": 333, "180": 333, "136": 333, "152": 333, "175": 333, "breve": 333, "dotaccent": 333, "168": 333, "ring": 333, "184": 333, "hungarumlaut": 333, "ogonek": 333, "caron": 333, "151": 1000, "198": 889, "170": 276, "Lslash": 611, "216": 722, "140": 889, "186": 310, "230": 667, "dotlessi": 278, "lslash": 278, "248": 500, "156": 722, "223": 500, "207": 333, "233": 444, "abreve": 444, "uhungarumlaut": 500, "ecaron": 444, "159": 722, "247": 564, "221": 722, "194": 722, "225": 444, "219": 722, "253": 500, "scommaaccent": 389, "234": 444, "Uring": 722, "220": 722, "aogonek": 444, "218": 722, "uogonek": 500, "203": 611, "Dcroat": 722, "commaaccent": 250, "169": 760, "Emacron": 611, "ccaron": 444, "229": 444, "Ncommaaccent": 722, "lacute": 278, "224": 444, "Tcommaaccent": 611, "Cacute": 667, "227": 444, "Edotaccent": 611, "154": 389, "scedilla": 389, "237": 278, "lozenge": 471, "Rcaron": 667, "Gcommaaccent": 722, "251": 500, "226": 444, "Amacron": 722, "rcaron": 333, "231": 444, "Zdotaccent": 611, "222": 556, "Omacron": 722, "Racute": 667, "Sacute": 556, "dcaron": 588, "Umacron": 722, "uring": 500, "179": 300, "210": 722, "192": 722, "Abreve": 722, "215": 564, "250": 500, "Tcaron": 611, "partialdiff": 476, "255": 500, "Nacute": 722, "238": 278, "202": 611, "228": 444, "235": 444, "cacute": 444, "nacute": 500, "umacron": 500, "Ncaron": 722, "205": 333, "177": 564, "166": 200, "174": 760, "Gbreve": 722, "Idotaccent": 333, "summation": 600, "200": 611, "racute": 333, "omacron": 500, "Zacute": 611, "142": 611, "greaterequal": 549, "208": 722, "199": 667, "lcommaaccent": 278, "tcaron": 326, "eogonek": 444, "Uogonek": 722, "193": 722, "196": 722, "232": 444, "zacute": 444, "iogonek": 278, "211": 722, "243": 500, "amacron": 444, "sacute": 389, "239": 278, "212": 722, "217": 722, "Delta": 612, "254": 500, "178": 300, "214": 722, "181": 500, "236": 278, "ohungarumlaut": 500, "Eogonek": 611, "dcroat": 500, "190": 750, "Scedilla": 556, "lcaron": 344, "Kcommaaccent": 722, "Lacute": 611, "153": 980, "edotaccent": 444, "204": 333, "Imacron": 333, "Lcaron": 611, "189": 750, "lessequal": 549, "244": 500, "241": 500, "Uhungarumlaut": 722, "201": 611, "emacron": 444, "gbreve": 500, "188": 750, "138": 556, "Scommaaccent": 556, "Ohungarumlaut": 722, "176": 400, "242": 500, "Ccaron": 667, "249": 500, "radical": 453, "Dcaron": 722, "rcommaaccent": 333, "209": 722, "245": 500, "Rcommaaccent": 667, "Lcommaaccent": 611, "195": 722, "Aogonek": 722, "197": 722, "213": 722, "zdotaccent": 444, "Ecaron": 611, "Iogonek": 333, "kcommaaccent": 500, "minus": 564, "206": 333, "ncaron": 500, "tcommaaccent": 278, "172": 564, "246": 500, "252": 500, "notequal": 549, "gcommaaccent": 500, "240": 500, "158": 444, "ncommaaccent": 500, "185": 300, "imacron": 278, "128": 500}, "CIDtoGID_Compressed": true, "CIDtoGID": "eJwDAAAAAAE=", "_version_": 6}