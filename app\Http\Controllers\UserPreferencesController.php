<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class UserPreferencesController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        $preferences = $user->preferences()
            ->get()
            ->mapWithKeys(function ($pref) {
                return [$pref->preference_key => $pref->preference_value];
            });

        return Inertia::render('UserPreferences/Index', [
            'preferences' => $preferences,
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'key' => 'required|string',
            'value' => 'nullable',
        ]);

        $user = $request->user();

        $user->preferences()->updateOrCreate(
            ['preference_key' => $request->key],
            ['preference_value' => $request->value]
        );

        return redirect()->back()->with('success', 'Preferences saved successfully!');
    }   
}
