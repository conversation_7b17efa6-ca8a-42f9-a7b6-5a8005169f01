<template>

  <Head :title="'Operating Unit - ' + operatingUnit.name" />
  <AppSidebarLayout>
    <v-container>
      <v-row>
        <v-col cols="12" class="d-flex justify-space-between align-center">
          <div>
            <h1 class="text-h4 mb-2">Operating Unit Details</h1>
            <p class="text-subtitle-1">{{ operatingUnit.name }} ({{ operatingUnit.code }})</p>
          </div>
          <div>
            <Link :href="route('operating-units.index')" class="text-decoration-none mr-2">
            <v-btn color="primary" variant="outlined" prepend-icon="mdi-arrow-left">
              Back to List
            </v-btn>
            </Link>
            <Link :href="route('operating-units.edit', operatingUnit.id)" class="text-decoration-none">
            <v-btn color="secondary" prepend-icon="mdi-pencil">
              Edit
            </v-btn>
            </Link>
          </div>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12">
          <v-card elevation="2">
            <v-card-title class="bg-primary text-white">Operating Unit Information</v-card-title>
            <v-card-text>
              <v-list>
                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-office-building" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">Name</div>
                    <div class="text-body-2">{{ operatingUnit.name }}</div>
                  </div>
                </v-list-item>

                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-barcode" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">Code</div>
                    <div class="text-body-2">{{ operatingUnit.code }}</div>
                  </div>
                </v-list-item>



                <v-list-item v-if="operatingUnit.email">
                  <template v-slot:prepend>
                    <v-icon icon="mdi-email" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">Email</div>
                    <div class="text-body-2">{{ operatingUnit.email }}</div>
                  </div>
                </v-list-item>



                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-calendar" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">Created At</div>
                    <div class="text-body-2">{{ formatDate(operatingUnit.created_at) }}</div>
                  </div>
                </v-list-item>

                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-update" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">Last Updated</div>
                    <div class="text-body-2">{{ formatDate(operatingUnit.updated_at) }}</div>
                  </div>
                </v-list-item>
              </v-list>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';
const route = window.route;

const props = defineProps({
  operatingUnit: Object,
});

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString();
};
</script>

<style scoped>
.v-list-item {
  margin-bottom: 8px;
}

.text-subtitle-1.font-weight-medium {
  color: rgba(0, 0, 0, 0.87);
  font-weight: 500;
}

.text-body-2 {
  color: rgba(0, 0, 0, 0.6);
}
</style>
