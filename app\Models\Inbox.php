<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class Inbox extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'subject',
        'message',
        'sender_id',
        'recipient_id',
        'document_id',
        'reference',
        'is_read',
        'priority',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_read' => 'boolean',
    ];

    /**
     * Get the sender of the inbox message.
     */
    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Get the recipient of the inbox message.
     */
    public function recipient(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recipient_id');
    }

    /**
     * Get the document associated with the inbox message.
     */
    public function document(): BelongsTo
    {
        return $this->belongsTo(Document::class);
    }

    /**
     * Scope a query to only include messages with a specific reference type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $referenceType
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithReference($query, $referenceType)
    {
        return $query->where('reference', $referenceType);
    }
}

