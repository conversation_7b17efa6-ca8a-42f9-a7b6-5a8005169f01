<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\User;
use App\Models\Document;

class DocumentTrackingHistory extends Model
{
    protected $table = 'document_tracking_history';

    protected $fillable = [
        'document_id',
        'handled_by_id',
        'status',
        'location',
        'notes',
        'remarks',
        'tracked_at',
        'requires_action',
        'action_deadline',
        'action_completed',
        'action_completed_at',
    ];

    protected $casts = [
        'tracked_at' => 'datetime',
        'action_deadline' => 'date',
        'action_completed_at' => 'datetime',
        'requires_action' => 'boolean',
        'action_completed' => 'boolean',
    ];

    /**
     * Get the document that owns the tracking history.
     */
    public function document()
    {
        return $this->belongsTo(Document::class);
    }

    /**
     * Get the user who handled this tracking entry.
     */
    public function handledBy()
    {
        return $this->belongsTo(User::class, 'handled_by_id');
    }
}
