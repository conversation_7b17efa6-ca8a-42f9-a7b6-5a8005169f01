<script setup>
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import { ref, watch, computed, onMounted } from 'vue';

defineProps({
    canResetPassword: {
        type: Boolean,
    },
    status: {
        type: String,
    },
});

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const isLoading = ref(false);
const showPassword = ref(false);
const page = usePage();

// Check for flash errors from the server
onMounted(() => {
    const flashErrors = page.props.errors;

    if (flashErrors && Object.keys(flashErrors).length > 0) {
        // Copy flash errors to form errors
        Object.keys(flashErrors).forEach(key => {
            form.errors[key] = flashErrors[key];
        });

        // Set focus to the first field with an error
        if (flashErrors.email) {
            document.getElementById('email')?.focus();
        } else if (flashErrors.password) {
            document.getElementById('password')?.focus();
        }
    }
});

// Computed property for authentication error message
const authError = computed(() => {
    // Only show the general authentication error at the top
    // Field-specific errors will be shown below each field
    if (form.errors.auth_error) {
        return form.errors.auth_error;
    }
    // Return empty string if no auth error
    return '';
});

// Clear specific field errors when user types, but with a delay
let emailTypingTimer;
watch(() => form.email, () => {
    // Only clear email-related errors when the user types in the email field
    if (form.errors.email) {
        // Clear any existing timer
        clearTimeout(emailTypingTimer);

        // Set a new timer to clear the error after a delay
        emailTypingTimer = setTimeout(() => {
            // Only clear the email error, not all errors
            const currentErrors = { ...form.errors };
            delete currentErrors.email;
            form.clearErrors();

            // Restore other errors
            Object.keys(currentErrors).forEach(key => {
                form.errors[key] = currentErrors[key];
            });
        }, 2000); // 2 second delay
    }
});

let passwordTypingTimer;
watch(() => form.password, () => {
    // Only clear password-related errors when the user types in the password field
    if (form.errors.password) {
        // Clear any existing timer
        clearTimeout(passwordTypingTimer);

        // Set a new timer to clear the error after a delay
        passwordTypingTimer = setTimeout(() => {
            // Only clear the password error, not all errors
            const currentErrors = { ...form.errors };
            delete currentErrors.password;
            form.clearErrors();

            // Restore other errors
            Object.keys(currentErrors).forEach(key => {
                form.errors[key] = currentErrors[key];
            });
        }, 2000); // 2 second delay
    }
});

const submit = () => {
    isLoading.value = true;

    form.post(route('login'), {
        onFinish: () => {
            form.reset('password');
            isLoading.value = false;
        },
        onError: (errors) => {
            // Clear existing errors first
            form.clearErrors();

            // Directly set the errors on the form object
            if (errors.email) {
                form.errors.email = errors.email;
            }
            if (errors.password) {
                form.errors.password = errors.password;
            }
            if (errors.auth_error) {
                form.errors.auth_error = errors.auth_error;
            }

            // Force a UI update by creating a small delay
            setTimeout(() => {
                // Set focus to the first field with an error
                if (errors.email) {
                    document.getElementById('email').focus();
                } else if (errors.password) {
                    document.getElementById('password').focus();
                }

                isLoading.value = false;
            }, 100);
        }
    });
};
</script>

<template>
    <div class="login-page">

        <Head title="Login - Records Management Tracking System" />

        <div class="login-container">
            <div class="login-left">
                <div class="login-header">
                    <div class="logo-container">
                        <img src="/dmmmsu.png" alt="DMMMSU Logo" class="logo" />
                    </div>
                    <h1 class="app-name">Records Management Tracking System</h1>
                    <p class="app-description">Document Tracking & Management</p>
                </div>

                <div class="login-features">
                    <div class="feature">
                        <div class="feature-icon">
                            <v-icon icon="mdi-file-document-outline" size="large" color="white"></v-icon>
                        </div>
                        <div class="feature-text">
                            <h3>Document Management</h3>
                            <p>Organize and manage all your documents in one place</p>
                        </div>
                    </div>

                    <div class="feature">
                        <div class="feature-icon">
                            <v-icon icon="mdi-map-marker-path" size="large" color="white"></v-icon>
                        </div>
                        <div class="feature-text">
                            <h3>Document Tracking</h3>
                            <p>Track the movement and status of your documents</p>
                        </div>
                    </div>

                    <div class="feature">
                        <div class="feature-icon">
                            <v-icon icon="mdi-qrcode-scan" size="large" color="white"></v-icon>
                        </div>
                        <div class="feature-text">
                            <h3>QR Code Integration</h3>
                            <p>Easily scan and identify documents with QR codes</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="login-right">
                <div class="login-form-container">
                    <h2 class="login-title">Login to your account</h2>

                    <div v-if="status" class="status-message">
                        {{ status }}
                    </div>

                    <!-- Authentication Error Message -->
                    <div v-if="authError" class="auth-error-container">
                        <v-icon icon="mdi-alert-circle" size="small" color="#d32f2f" class="mr-2"></v-icon>
                        {{ authError }}
                    </div>

                    <form @submit.prevent="submit" class="login-form">
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <div class="input-with-icon" :class="{ 'error-border': form.errors.email }">
                                <v-icon icon="mdi-email-outline" size="small" class="input-icon"></v-icon>
                                <input id="email" type="email" v-model="form.email" required autofocus
                                    autocomplete="username" placeholder="Enter your email" />
                            </div>
                            <!-- Email Error Message -->
                            <div v-if="form.errors.email" class="field-error">
                                <v-icon icon="mdi-alert-circle-outline" size="x-small" color="#d32f2f"
                                    class="mr-1"></v-icon>
                                {{ form.errors.email }}
                            </div>

                        </div>

                        <div class="form-group">
                            <label for="password">Password</label>
                            <div class="input-with-icon" :class="{ 'error-border': form.errors.password }">
                                <v-icon icon="mdi-lock-outline" size="small" class="input-icon"></v-icon>
                                <input id="password" :type="showPassword ? 'text' : 'password'" v-model="form.password"
                                    required autocomplete="current-password" placeholder="Enter your password" />
                                <v-icon :icon="showPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'" size="small"
                                    class="toggle-password" @click="showPassword = !showPassword"></v-icon>
                            </div>
                            <div v-if="form.errors.password" class="field-error">
                                <v-icon icon="mdi-alert-circle-outline" size="x-small" color="#d32f2f"
                                    class="mr-1"></v-icon>
                                {{ form.errors.password }}
                            </div>
                        </div>


                        <div class="form-options">
                            <div class="remember-me">
                                <input type="checkbox" id="remember" v-model="form.remember" />
                                <label for="remember">Remember me</label>
                            </div>

                            <Link v-if="canResetPassword" :href="route('password.request')" class="forgot-password">
                            Forgot password?
                            </Link>
                        </div>

                        <button type="submit" class="login-button" :disabled="form.processing || isLoading">
                            <!-- <v-progress-circular v-if="isLoading" indeterminate size="20" width="2" color="white"
                                class="mr-2"></v-progress-circular> -->
                            <span>{{ isLoading ? 'Logging in...' : 'Login' }}</span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.login-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    padding: 20px;
}

.login-container {
    display: flex;
    width: 100%;
    max-width: 1200px;
    min-height: 600px;
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.login-left {
    flex: 1;
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    color: white;
    padding: 40px;
    display: flex;
    flex-direction: column;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.logo {
    width: 120px;
    height: 120px;
    object-fit: contain;
}

.app-name {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
}

.app-description {
    font-size: 16px;
    opacity: 0.9;
}

.login-features {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.feature {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.feature-icon {
    background-color: rgba(255, 255, 255, 0.25);
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.feature-icon :deep(.v-icon) {
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
}

.feature-text h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.feature-text p {
    font-size: 14px;
    opacity: 0.8;
}

.login-right {
    flex: 1;
    padding: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-form-container {
    width: 100%;
    max-width: 400px;
}

.login-title {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 30px;
    text-align: center;
}

.status-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-size: 14px;
}

.auth-error-container {
    display: flex;
    align-items: center;
    background-color: #ffebee;
    color: #d32f2f;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: 500;
}

.field-error {
    display: flex;
    align-items: center;
    color: #d32f2f;
    font-size: 12px;
    margin-top: 5px;
    font-weight: 500;
}



.error-border {
    border-color: #d32f2f !important;
}


.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #555;
    margin-bottom: 8px;
}

.input-with-icon {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 12px;
    color: #777;
}

.toggle-password {
    position: absolute;
    right: 12px;
    color: #777;
    cursor: pointer;
}

.input-with-icon input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.input-with-icon input:focus {
    outline: none;
    border-color: #2e7d32;
}



.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 8px;
}

.remember-me label {
    font-size: 14px;
    color: #555;
}

.forgot-password {
    font-size: 14px;
    color: #2e7d32;
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-button {
    width: 100%;
    padding: 12px;
    background-color: #2e7d32;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-button:hover {
    background-color: #1b5e20;
}

.login-button:disabled {
    background-color: #9e9e9e;
    cursor: not-allowed;
}

@media (max-width: 900px) {
    .login-container {
        flex-direction: column;
    }

    .login-left {
        padding: 30px;
    }

    .login-features {
        display: none;
    }

    .login-right {
        padding: 30px;
    }
}
</style>
