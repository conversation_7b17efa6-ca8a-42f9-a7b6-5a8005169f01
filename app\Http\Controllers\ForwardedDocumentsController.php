<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\DocumentTrackingHistory;
use App\Services\OperatingUnitSyncService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ForwardedDocumentsController extends Controller
{
    /**
     * @var OperatingUnitSyncService
     */
    protected $syncService;

    /**
     * Constructor
     *
     * @param OperatingUnitSyncService $syncService
     */
    public function __construct(OperatingUnitSyncService $syncService)
    {
        $this->syncService = $syncService;
    }

    /**
     * Display a listing of forwarded documents.
     */
    public function index(Request $request)
    {
        // Get the authenticated user
        $user = Auth::user();

        // If user doesn't have an operating unit, fall back to user-specific forwarded documents
        if (!$user->operating_unit_id) {
            return $this->getUserSpecificForwardedDocuments($user, $request);
        }

        // Get forwarded documents for the user's operating unit
        $allForwardedDocuments = $this->syncService->getForwardedDocumentsForOperatingUnit(
            $user->operating_unit_id,
            $request->has('reference') && in_array($request->reference, ['F01', 'F02', 'F03', 'F04']) ? $request->reference : null
        );

        // Filter to only show documents handled by this user
        $filteredDocuments = $allForwardedDocuments->filter(function ($tracking) use ($user) {
            return $tracking->handled_by_id === $user->id;
        });

        // Paginate the filtered documents
        $forwardedDocuments = new \Illuminate\Pagination\LengthAwarePaginator(
            $filteredDocuments->forPage($request->page ?? 1, 10),
            $filteredDocuments->count(),
            10,
            $request->page ?? 1,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        // Get reference types for dropdown
        $referenceTypes = $filteredDocuments
            ->pluck('document.reference_document')
            ->filter()
            ->unique()
            ->values()
            ->toArray();

        return Inertia::render('ForwardedDocuments/Index', [
            'forwardedDocuments' => $forwardedDocuments,
            'activeReference' => $request->reference ?? null,
            'referenceTypes' => $referenceTypes,
        ]);
    }

    /**
     * Get user-specific forwarded documents (fallback for users without operating unit)
     */
    private function getUserSpecificForwardedDocuments($user, $request)
    {
        // Base query for forwarded documents
        $query = DocumentTrackingHistory::with(['document'])
            ->where('handled_by_id', $user->id)
            ->where('status', 'forwarded');

        // Filter by reference if provided
        if ($request->has('reference') && in_array($request->reference, ['F01', 'F02', 'F03', 'F04'])) {
            // Use F01 format directly (with zero, not letter O)
            $query->whereHas('document', function ($q) use ($request) {
                $q->where('reference_document', $request->reference);
            });
        }

        // Get paginated results
        $forwardedDocuments = $query->orderBy('tracked_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Get reference types for dropdown
        $referenceTypes = Document::whereIn('id', function ($query) use ($user) {
            $query->select('document_id')
                ->from('document_tracking_history')
                ->where('handled_by_id', $user->id)
                ->where('status', 'forwarded');
        })
            ->whereNotNull('reference_document')
            ->distinct()
            ->pluck('reference_document')
            ->toArray();

        return Inertia::render('ForwardedDocuments/Index', [
            'forwardedDocuments' => $forwardedDocuments,
            'activeReference' => $request->reference ?? null,
            'referenceTypes' => $referenceTypes,
        ]);
    }

    /**
     * Display the specified forwarded document.
     *
     * @param string $id The ID of the document to show
     * @return \Illuminate\Http\RedirectResponse
     */
    public function show(string $id)
    {
        // Redirect to the show-forwarded route which is the correct one to use
        return redirect()->route('forwarded-documents.show-forwarded', $id);
    }

    /**
     * Show forwarded document details.
     */
    public function showForwarded(string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // If user doesn't have an operating unit, fall back to user-specific query
        if (!$user->operating_unit_id) {
            // Get the tracking history entry
            $trackingEntry = DocumentTrackingHistory::with(['document'])
                ->where('id', $id)
                ->where('handled_by_id', $user->id)
                ->where('status', 'forwarded')
                ->firstOrFail();
        } else {
            // Get all forwarded documents for the operating unit
            $allForwardedDocuments = $this->syncService->getForwardedDocumentsForOperatingUnit($user->operating_unit_id);

            // Find the specific tracking entry
            $trackingEntry = $allForwardedDocuments->firstWhere('id', $id);

            // If not found or not handled by this user, throw 404
            if (!$trackingEntry || $trackingEntry->handled_by_id !== $user->id) {
                abort(404);
            }
        }

        return Inertia::render('ForwardedDocuments/ShowForwarded', [
            'trackingEntry' => $trackingEntry,
        ]);
    }

    /**
     * Remove the specified forwarded document record.
     */
    public function destroy(string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Find the tracking history entry
        $trackingEntry = DocumentTrackingHistory::where('id', $id)
            ->where('handled_by_id', $user->id)
            ->where('status', 'forwarded')
            ->firstOrFail();

        // Delete the tracking history entry
        $trackingEntry->delete();

        return redirect()->route('forwarded-documents.index')
            ->with('success', 'Forwarded document record deleted successfully.');
    }
}
