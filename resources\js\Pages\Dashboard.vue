<template>

    <Head title="Dashboard" />
    <AppSidebarLayout>
        <v-container fluid class="dashboard-container">
            <!-- Loading Overlay -->
            <v-overlay v-if="loading" class="justify-center align-center">
                <!-- <v-progress-circular indeterminate size="64"></v-progress-circular> -->
            </v-overlay>

            <!-- Error <PERSON> -->
            <v-alert v-if="error" type="error" title="Error Loading Dashboard"
                text="There was a problem loading the dashboard data. Please try again later." class="mb-4"
                closable></v-alert>

            <!-- Key Metrics Section -->
            <!-- <h2 class="mb-4 text-h5 font-weight-bold">Key Metrics</h2> -->
            <v-row>
                <v-col cols="12" sm="6" md="3">
                    <v-card class="metric-card" elevation="2" rounded="lg">
                        <v-card-text class="pa-4">
                            <div class="d-flex align-center">
                                <v-avatar color="info" size="48" class="mr-4">
                                    <v-icon icon="mdi-file-multiple" size="24"></v-icon>
                                </v-avatar>
                                <div>
                                    <div class="text-overline text-medium-emphasis">
                                        Total Documents
                                    </div>
                                    <div class="text-h4 font-weight-bold">
                                        {{ analyticsData.totalDocuments || 0 }}
                                    </div>
                                    <div class="text-caption">
                                        {{ userRole === 'super_admin' ? 'All Operating Units' : 'Your Operating Unit' }}
                                    </div>
                                </div>
                            </div>
                        </v-card-text>
                    </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                    <v-card class="metric-card" elevation="2" rounded="lg">
                        <v-card-text class="pa-4">
                            <div class="d-flex align-center">
                                <v-avatar color="success" size="48" class="mr-4">
                                    <v-icon icon="mdi-send" size="24"></v-icon>
                                </v-avatar>
                                <div>
                                    <div class="text-overline text-medium-emphasis">
                                        Forwarded Documents
                                    </div>
                                    <div class="text-h4 font-weight-bold">
                                        {{ analyticsData.forwardedDocuments || 0 }}
                                    </div>
                                    <div class="text-caption">
                                        Documents sent to other units
                                    </div>
                                </div>
                            </div>
                        </v-card-text>
                    </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                    <v-card class="metric-card" elevation="2" rounded="lg">
                        <v-card-text class="pa-4">
                            <div class="d-flex align-center">
                                <v-avatar color="accent" size="48" class="mr-4">
                                    <v-icon icon="mdi-inbox-arrow-down" size="24"></v-icon>
                                </v-avatar>
                                <div>
                                    <div class="text-overline text-medium-emphasis">
                                        My Documents
                                    </div>
                                    <div class="text-h4 font-weight-bold">
                                        {{ analyticsData.receivedDocuments || 0 }}
                                    </div>
                                    <div class="text-caption">
                                        Documents received by your unit
                                    </div>
                                </div>
                            </div>
                        </v-card-text>
                    </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                    <v-card class="metric-card" elevation="2" rounded="lg">
                        <v-card-text class="pa-4">
                            <div class="d-flex align-center">
                                <v-avatar color="warning" size="48" class="mr-4">
                                    <v-icon icon="mdi-clock-alert" size="24"></v-icon>
                                </v-avatar>
                                <div>
                                    <div class="text-overline text-medium-emphasis">
                                        Unforwarded
                                    </div>
                                    <div class="text-h4 font-weight-bold">
                                        {{ analyticsData.unforwardedDocuments || 0 }}
                                    </div>
                                    <div class="text-caption">
                                        Documents pending forwarding
                                    </div>
                                </div>
                            </div>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>

            <!-- Operating Unit Breakdown (Super Admin Only) -->
            <v-row v-if="userRoleId === 1 && operatingUnitBreakdown.length > 0" class="mt-6">
                <v-col cols="12">
                    <v-card elevation="2" rounded="lg">
                        <v-card-title class="text-h6 font-weight-bold pa-4">
                            <v-icon icon="mdi-office-building" class="mr-2"></v-icon>
                            Document Count by Operating Unit
                        </v-card-title>
                        <v-divider></v-divider>
                        <v-card-text class="pa-0">
                            <v-table>
                                <thead>
                                    <tr>
                                        <th class="text-left">Code</th>
                                        <th class="text-left">Operating Unit</th>
                                        <th class="text-center">
                                            <v-tooltip location="top" text="Documents received by the operating unit">
                                                <template v-slot:activator="{ props }">
                                                    <div v-bind="props" class="d-flex align-center justify-center">
                                                        <v-icon icon="mdi-file-outline" size="small"
                                                            class="mr-1"></v-icon>
                                                        Document Count
                                                    </div>
                                                </template>
                                            </v-tooltip>
                                        </th>
                                        <th class="text-center">
                                            <v-tooltip location="top"
                                                text="Documents forwarded by the operating unit to others">
                                                <template v-slot:activator="{ props }">
                                                    <div v-bind="props" class="d-flex align-center justify-center">
                                                        <v-icon icon="mdi-send" size="small" class="mr-1"></v-icon>
                                                        Forwarded
                                                    </div>
                                                </template>
                                            </v-tooltip>
                                        </th>
                                        <th class="text-center">
                                            <v-tooltip location="top"
                                                text="New documents created/registered by the operating unit">
                                                <template v-slot:activator="{ props }">
                                                    <div v-bind="props" class="d-flex align-center justify-center">
                                                        <v-icon icon="mdi-file-plus" size="small" class="mr-1"></v-icon>
                                                        Registered
                                                    </div>
                                                </template>
                                            </v-tooltip>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(unit, index) in operatingUnitBreakdown" :key="index">
                                        <td>{{ unit.code }}</td>
                                        <td>{{ unit.name }}</td>
                                        <td class="text-center font-weight-bold">
                                            {{ unit.receivedCount }}
                                        </td>
                                        <td class="text-center font-weight-bold">
                                            {{ unit.forwardedCount }}
                                        </td>
                                        <td class="text-center font-weight-bold">
                                            {{ unit.registeredCount }}
                                        </td>
                                    </tr>
                                    <tr v-if="operatingUnitBreakdown.length === 0">
                                        <td colspan="5" class="text-center text-medium-emphasis">
                                            No operating units found
                                        </td>
                                    </tr>
                                </tbody>
                            </v-table>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>

            <!-- Department Unit Breakdown (Admin and Staff Only) -->
            <v-row v-if="(userRoleId === 2 || userRoleId === 3) && departmentUnitBreakdown.length > 0" class="mt-6">
                <v-col cols="12">
                    <v-card elevation="2" rounded="lg">
                        <v-card-title class="text-h6 font-weight-bold pa-4">
                            <v-icon icon="mdi-domain" class="mr-2"></v-icon>
                            Document Count by Department Unit
                        </v-card-title>
                        <v-divider></v-divider>
                        <v-card-text class="pa-0">
                            <v-table>
                                <thead>
                                    <tr>
                                        <th class="text-left">Department</th>
                                        <th class="text-center">Document Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(department, index) in departmentUnitBreakdown" :key="index">
                                        <td>{{ department.name }}</td>
                                        <td class="text-center font-weight-bold">{{ department.count }}</td>
                                    </tr>
                                    <tr v-if="departmentUnitBreakdown.length === 0">
                                        <td colspan="2" class="text-center text-medium-emphasis">
                                            No department units found
                                        </td>
                                    </tr>
                                </tbody>
                            </v-table>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>

            <!-- Two Column Layout for Recent Activity and Status Updates -->
            <v-row class="mt-6">
                <!-- Recent Activity Section -->
                <v-col cols="12" md="7">
                    <v-card class="activity-card" elevation="2" rounded="lg">
                        <v-card-title class="text-h6 font-weight-bold pa-4">
                            <v-icon icon="mdi-clock-outline" class="mr-2"></v-icon>
                            Recent Activity
                        </v-card-title>
                        <v-divider></v-divider>
                        <v-card-text class="pa-0">
                            <v-list lines="two">
                                <v-list-item v-for="(activity, index) in recentActivities" :key="index" :ripple="false">
                                    <template v-slot:prepend>
                                        <v-avatar :color="activity.color" size="36">
                                            <v-icon :icon="activity.icon" size="20"></v-icon>
                                        </v-avatar>
                                    </template>
                                    <v-list-item-title class="text-black font-weight-medium">
                                        {{ activity.title }}
                                    </v-list-item-title>
                                    <v-list-item-subtitle class="text-black">
                                        {{ activity.description }}
                                    </v-list-item-subtitle>
                                    <template v-slot:append>
                                        <div class="text-black text-caption text-medium-emphasis">
                                            {{ activity.time }}
                                        </div>
                                    </template>
                                </v-list-item>
                                <v-list-item v-if="recentActivities.length === 0">
                                    <v-list-item-title class="text-center text-medium-emphasis">
                                        No recent activities found
                                    </v-list-item-title>
                                </v-list-item>
                            </v-list>
                        </v-card-text>
                        <v-divider></v-divider>
                        <v-card-actions class="pa-4">
                            <v-btn variant="text" color="primary" prepend-icon="mdi-history">
                                View All Activity
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </v-col>

                <!-- Status Updates Section -->
                <v-col cols="12" md="5">
                    <v-card class="status-card" elevation="2" rounded="lg">
                        <v-card-title class="text-h6 font-weight-bold pa-4">
                            <v-icon icon="mdi-alert-circle-outline" class="mr-2"></v-icon>
                            Status Updates
                        </v-card-title>
                        <v-divider></v-divider>
                        <v-card-text class="pa-4">
                            <v-list>
                                <v-list-item v-for="(update, index) in statusUpdates" :key="index"
                                    class="mb-3 rounded-lg pa-3 status-item">
                                    <template v-slot:prepend>
                                        <v-avatar :color="update.statusColor" size="36">
                                            <v-icon :icon="update.icon" size="20"></v-icon>
                                        </v-avatar>
                                    </template>
                                    <v-list-item-title class="text-black font-weight-medium">
                                        {{ update.title }}
                                    </v-list-item-title>
                                    <v-list-item-subtitle class="text-black text">
                                        {{ update.description }}
                                    </v-list-item-subtitle>
                                    <template v-slot:append>
                                        <v-chip :color="update.statusColor" size="small" class="text-black text">
                                            {{ update.status }}
                                        </v-chip>
                                    </template>
                                </v-list-item>
                                <v-list-item v-if="statusUpdates.length === 0">
                                    <v-list-item-title class="text-center text-medium-emphasis">
                                        No status updates available
                                    </v-list-item-title>
                                </v-list-item>
                            </v-list>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </AppSidebarLayout>
</template>

<script setup>
import { Head } from '@inertiajs/vue3';
import { ref } from 'vue';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

// Props from the controller
const props = defineProps({
    analyticsData: {
        type: Object,
        default: () => ({
            totalDocuments: 0,
            forwardedDocuments: 0,
            receivedDocuments: 0,
            unforwardedDocuments: 0
        })
    },
    operatingUnitBreakdown: {
        type: Array,
        default: () => []
    },
    departmentUnitBreakdown: {
        type: Array,
        default: () => []
    },
    recentActivities: {
        type: Array,
        default: () => []
    },
    statusUpdates: {
        type: Array,
        default: () => []
    },
    userRoleId: {
        type: Number,
        default: 3 // Default to staff role ID (3)
    },
    userRole: {
        type: String,
        default: 'staff'
    }
});

// Loading and error states
const loading = ref(false);
const error = ref(false);
</script>

<style scoped>
.dashboard-container {
    padding-bottom: 32px;
}

.welcome-banner {
    background: linear-gradient(to right,
            rgba(46, 125, 50, 0.05),
            rgba(129, 199, 132, 0.1));
    border-left: 4px solid var(--v-primary-base);
    transition: all 0.3s ease;
}

.welcome-banner:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-card {
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.metric-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: var(--v-info-base);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.metric-card:hover::before {
    opacity: 1;
}

.quick-action-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-action-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background-color: rgba(0, 0, 0, 0.02);
}

.activity-card,
.status-card {
    height: 100%;
    transition: all 0.3s ease;
}

.activity-card:hover,
.status-card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.status-item {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.status-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .welcome-banner .v-card-text {
        padding: 16px !important;
    }

    .welcome-banner h1 {
        font-size: 1.5rem !important;
    }

    .metric-card .v-card-text {
        padding: 12px !important;
    }
}
</style>
