<template>
  <Head title="Create Document Code" />
  <AppSidebarLayout>
    <v-container>
      <v-row>
        <v-col cols="12" class="d-flex align-center">
          <Link :href="route('document-codes.index')" class="text-decoration-none me-4">
            <v-btn icon color="primary">
              <v-icon>mdi-arrow-left</v-icon>
            </v-btn>
          </Link>
          <h1 class="text-h5">Create Document Code</h1>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" md="8" lg="6">
          <v-card elevation="2">
            <v-card-text>
              <form @submit.prevent="submit">
                <v-row>
                  <v-col cols="12">
                    <v-text-field
                      v-model="form.docu_code"
                      label="Document Code"
                      :error-messages="form.errors.docu_code"
                      required
                      variant="outlined"
                      density="comfortable"
                      bg-color="white"
                      hint="Enter a unique code (e.g., MEMO, LETTER, etc.)"
                      persistent-hint
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12">
                    <v-text-field
                      v-model="form.description"
                      label="Description"
                      :error-messages="form.errors.description"
                      required
                      variant="outlined"
                      density="comfortable"
                      bg-color="white"
                      hint="Enter a description for this document code"
                      persistent-hint
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12">
                    <v-switch
                      v-model="form.is_active"
                      label="Active"
                      color="primary"
                      hide-details
                    ></v-switch>
                  </v-col>

                  <v-col cols="12" class="d-flex justify-end">
                    <v-btn
                      type="submit"
                      color="primary"
                      size="large"
                      :loading="form.processing"
                    >
                      Create Document Code
                    </v-btn>
                  </v-col>
                </v-row>
              </form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, useForm, Link } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const form = useForm({
  docu_code: '',
  description: '',
  is_active: true,
});

const submit = () => {
  form.post(route('document-codes.store'));
};
</script>
