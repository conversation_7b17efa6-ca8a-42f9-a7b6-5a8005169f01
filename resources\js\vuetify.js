import "vuetify/styles";
import '@mdi/font/css/materialdesignicons.css';
import { createVuetify } from "vuetify";
import * as components from "vuetify/components";
import * as directives from "vuetify/directives";

const modernTheme = {
    dark: false,
    colors: {
        primary: "#2E7D32",      // Deep green
        secondary: "#424242",    // Dark gray
        accent: "#81C784",       // Light green
        highlight: "#C8E6C9",    // Very light green for highlights
        sidebar: "#2E7D32",      // Sidebar green
        "text-sidebar": "#FFFFFF", // Text color for sidebar items
        yellow: "#FFD600",       // Bright yellow for active items
        background: "#F5F5F5",   // Light gray background
        surface: "#FFFFFF",      // White surface
        error: "#FF5252",        // Red for errors
        info: "#2196F3",         // Blue for info
        success: "#4CAF50",      // Green for success
        warning: "#FFC107",      // Amber for warnings
    }
};

const vuetify = createVuetify({
    components,
    directives,
    theme: {
        defaultTheme: "modernTheme",
        themes: {
            modernTheme
        }
    },
    icons: {
        defaultSet: "mdi"
    }
});

export default vuetify;
