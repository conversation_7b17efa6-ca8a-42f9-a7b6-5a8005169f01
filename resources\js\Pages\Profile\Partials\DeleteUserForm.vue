<script setup>
import { useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const dialog = ref(false);
const showPassword = ref(false);

const form = useForm({
    password: '',
});

const deleteUser = () => {
    form.delete(route('profile.destroy'), {
        preserveScroll: true,
        onSuccess: () => closeDialog(),
        onFinish: () => form.reset(),
    });
};

const closeDialog = () => {
    dialog.value = false;
    form.clearErrors();
    form.reset();
};
</script>

<template>
    <div>
        <p class="mt-3 mb-6 text-sm text-gray-600">
            Once your account is deleted, all of its resources and data will be permanently deleted.
            Before deleting your account, please download any data or information that you wish to retain.
        </p>

        <div class="d-flex">
            <v-btn color="error" variant="outlined" prepend-icon="mdi-delete" @click="dialog = true">
                Delete Account
            </v-btn>
        </div>

        <v-dialog v-model="dialog" max-width="500px">
            <v-card>
                <v-card-title class="text-white bg-error">
                    Delete Account
                </v-card-title>

                <v-card-text class="pt-4">
                    <p class="mb-4">
                        Are you sure you want to delete your account? Once your account is deleted,
                        all of its resources and data will be permanently deleted. Please enter your
                        password to confirm you would like to permanently delete your account.
                    </p>

                    <v-text-field v-model="form.password" label="Password" variant="outlined"
                        :error-messages="form.errors.password" :type="showPassword ? 'text' : 'password'"
                        :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                        @click:append-inner="showPassword = !showPassword" bg-color="white" density="comfortable"
                        placeholder="Enter your password to confirm" @keyup.enter="deleteUser"></v-text-field>
                </v-card-text>

                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" variant="text" @click="closeDialog">
                        Cancel
                    </v-btn>
                    <v-btn color="error" :loading="form.processing" :disabled="form.processing" @click="deleteUser">
                        Delete Account
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>
