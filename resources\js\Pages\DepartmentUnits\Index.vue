<template>

    <Head title="Department Units" />
    <AppSidebarLayout>
        <v-container>
            <v-row>
                <v-col cols="12" class="justify-end d-flex align-center">
                    <Link :href="route('department-units.create')" class="text-decoration-none">
                    <v-btn class="bg-blue-lighten-1" prepend-icon="mdi-plus">Add New Department Unit</v-btn>
                    </Link>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-card elevation="2">
                        <v-card-title class="flex-wrap mt-2 d-flex align-center pa-4">

                            <v-spacer></v-spacer>
                            <div style="width: 300px">
                                <v-text-field v-model="search" append-icon="mdi-magnify"
                                    label="Search department units..." single-line hide-details variant="outlined"
                                    density="compact"></v-text-field>
                            </div>
                        </v-card-title>
                        <v-data-table :headers="headers" :items="departmentUnits.data" :search="search"
                            :loading="loading" class="elevation-1" density="compact" fixed-header hover
                            :items-per-page-options="itemsPerPageOptions" :items-per-page="itemsPerPage"
                            @update:options="handleTableOptions">
                            <template v-slot:item.actions="{ item }">
                                <div class="justify-center d-flex">
                                    <v-tooltip location="top">
                                        <template v-slot:activator="{ props }">
                                            <Link :href="`/department-units/${item.id}`"
                                                class="mr-2 text-decoration-none">
                                            <v-btn icon size="small" color="blue-lighten-1" v-bind="props">
                                                <v-icon>mdi-eye</v-icon>
                                            </v-btn>
                                            </Link>
                                        </template>
                                        View
                                    </v-tooltip>

                                    <v-tooltip location="top">
                                        <template v-slot:activator="{ props }">
                                            <Link :href="`/department-units/${item.id}/edit`"
                                                class="mr-2 text-decoration-none">
                                            <v-btn icon size="small" color="secondary" v-bind="props">
                                                <v-icon>mdi-pencil</v-icon>
                                            </v-btn>
                                            </Link>
                                        </template>
                                        Edit
                                    </v-tooltip>

                                    <v-tooltip location="top">
                                        <template v-slot:activator="{ props }">
                                            <v-btn icon size="small" color="error" @click="confirmDelete(item)"
                                                v-bind="props">
                                                <v-icon>mdi-delete</v-icon>
                                            </v-btn>
                                        </template>
                                        Delete
                                    </v-tooltip>
                                </div>
                            </template></v-data-table>

                        <!-- Delete Confirmation Dialog -->
                        <v-dialog v-model="deleteDialog" max-width="500">
                            <v-card>
                                <v-card-title class="text-white text-h5 bg-error">Confirm Delete</v-card-title>
                                <v-card-text class="pt-4">
                                    <p>Are you sure you want to delete this department unit?</p>
                                    <p v-if="departmentUnitToDelete" class="mt-2 font-weight-medium">
                                        Department Unit: {{ departmentUnitToDelete.code }} - {{
                                            departmentUnitToDelete.name }}
                                    </p>
                                    <p class="mt-4 text-error font-weight-bold">
                                        This action cannot be undone.
                                    </p>
                                </v-card-text>
                                <v-card-actions>
                                    <v-spacer></v-spacer>
                                    <v-btn color="grey-darken-1" variant="text" @click="deleteDialog = false">
                                        Cancel
                                    </v-btn>
                                    <v-btn color="error" variant="text" @click="deleteDepartmentUnit"
                                        :loading="deleting">
                                        Delete
                                    </v-btn>
                                </v-card-actions>
                            </v-card>
                        </v-dialog>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </AppSidebarLayout>
</template>
<script setup>

import { Head, Link } from '@inertiajs/vue3';
import { ref } from 'vue';
import { router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const route = window.route;

// Delete dialog state
const deleteDialog = ref(false);
const departmentUnitToDelete = ref(null);
const deleting = ref(false);

const props = defineProps({
    departmentUnits: Object
});

const search = ref('');
const loading = ref(false);
const itemsPerPage = ref(10);
const itemsPerPageOptions = [
    { title: '5', value: 5 },
    { title: '10', value: 10 },
    { title: '15', value: 15 },
    { title: '20', value: 20 },
    { title: 'All', value: -1 },
];

const headers = [
    { title: 'Name', key: 'name' },
    { title: 'Code', key: 'code', width: '150px' },
    { title: 'Actions', key: 'actions', sortable: false, width: '150px' },
];

// Handle table options changes (pagination, sorting, etc.)
const handleTableOptions = (options) => {
    if (options.itemsPerPage !== itemsPerPage.value) {
        itemsPerPage.value = options.itemsPerPage;
    }

    // Handle page changes if pagination is implemented in the future
    if (options.page && props.departmentUnits.current_page && options.page !== props.departmentUnits.current_page) {
        changePage(options.page);
    }
};

const changePage = (page) => {
    loading.value = true;
    router.get(route('department-units.index', { page: page }), {
        preserveState: true,
        onSuccess: () => {
            loading.value = false;
        }
    });
};

// Confirm delete function
const confirmDelete = (item) => {
    departmentUnitToDelete.value = item;
    deleteDialog.value = true;
};

// Delete department unit function
const deleteDepartmentUnit = () => {
    if (departmentUnitToDelete.value) {
        deleting.value = true;
        router.delete(route('department-units.destroy', departmentUnitToDelete.value.id), {
            onSuccess: () => {
                deleteDialog.value = false;
                departmentUnitToDelete.value = null;
                deleting.value = false;
            },
            onError: () => {
                deleting.value = false;
            }
        });
    }
};

</script>
<style scoped>
/* Table header styling */
:deep(.v-data-table-header th) {
    font-weight: bold;
    color: rgba(0, 0, 0, 0.87) !important;
}

:deep(.v-data-table .v-data-table__td) {
    color: rgba(0, 0, 0, 0.87) !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
}

/* Remove blue focus outline from input fields */
:deep(.v-field__input:focus-visible) {
    outline: none !important;
}

/* Table row hover styling */
:deep(.v-data-table .v-data-table__tr:hover) {
    background-color: rgba(25, 118, 210, 0.05) !important;
}

/* Pagination styling */
:deep(.v-data-table-footer) {
    border-top: thin solid rgba(0, 0, 0, 0.12);
    padding: 0 16px;
}

:deep(.v-data-table-footer__items-per-page) {
    margin-right: 16px;
}

:deep(.v-data-table-footer__info) {
    margin-right: 16px;
}

/* Button styling */
:deep(.v-btn--icon.v-btn--density-default) {
    width: 36px;
    height: 36px;
}

/* Tooltip styling */
:deep(.v-tooltip > .v-overlay__content) {
    background-color: rgba(33, 33, 33, 0.9);
    color: white;
    border-radius: 4px;
    font-size: 0.75rem;
    padding: 4px 8px;
}
</style>
