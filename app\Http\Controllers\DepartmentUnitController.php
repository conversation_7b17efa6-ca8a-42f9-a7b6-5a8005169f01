<?php

namespace App\Http\Controllers;

use App\Models\DepartmentUnit;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DepartmentUnitController extends Controller
{
    public function index()
    {
        // Fetch data from the database or perform any necessary logic here
        // For example, you might want to fetch department units from a model
        // $departmentUnits = DepartmentUnit::all();

        // Pass the data to the Inertia view
        // return Inertia::render('DepartmentUnit/Index', [
        //     'departmentUnits' => $departmentUnits,
        // ]);

        $departmentUnits = DepartmentUnit::orderBy('name')->paginate(10);

        return Inertia::render('DepartmentUnits/Index', [
            'departmentUnits' => $departmentUnits
        ]);


    }

    public function create()
    {
        return Inertia::render('DepartmentUnits/Create');
    }

    public function show(DepartmentUnit $departmentUnit)
    {
        return Inertia::render('DepartmentUnits/Show', [
            'departmentUnit' => $departmentUnit,
            'errors' => session()->get('errors') ? session()->get('errors')->getBag('default')->getMessages() : (object) [],
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:department_units,code',
        ]);

        DepartmentUnit::create($request->all());

        return redirect()->route('department-units.index')
            ->with('success', 'Department Unit created successfully.');
    }

    public function edit(DepartmentUnit $departmentUnit)
    {
        return Inertia::render('DepartmentUnits/Edit', [
            'departmentUnit' => $departmentUnit
        ]);
    }

    public function update(Request $request, DepartmentUnit $departmentUnit)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => "required|string|max:50|unique:department_units,code,{$departmentUnit->id}",
        ]);

        $departmentUnit->update($request->all());

        return redirect()->route('department-units.index')
            ->with('success', 'Department Unit updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DepartmentUnit $departmentUnit)
    {
        // Check if the department unit is being used by any documents
        if ($departmentUnit->documents()->count() > 0) {
            return redirect()->route('department-units.index')
                ->with('error', 'Cannot delete department unit because it is being used by documents.');
        }

        $departmentUnit->delete();

        return redirect()->route('department-units.index')
            ->with('success', 'Department Unit deleted successfully.');
    }
}
