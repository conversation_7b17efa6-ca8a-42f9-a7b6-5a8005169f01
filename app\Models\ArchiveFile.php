<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class ArchiveFile extends Model
{
    protected $fillable = [
        'original_name',
        'stored_name',
        'file_path',
        'mime_type',
        'size',
        'extension',
        'folder_id',
        'operating_unit_id',
        'uploaded_by',
        'metadata',
    ];

    protected $casts = [
        'size' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the folder that contains this file.
     */
    public function folder(): BelongsTo
    {
        return $this->belongsTo(ArchiveFolder::class, 'folder_id');
    }

    /**
     * Get the operating unit that owns this file.
     */
    public function operatingUnit(): BelongsTo
    {
        return $this->belongsTo(OperatingUnit::class);
    }

    /**
     * Get the user who uploaded this file.
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }



    /**
     * Get the file size in human readable format.
     */
    public function getFormattedSizeAttribute(): string
    {
        $bytes = $this->size;
        if ($bytes === 0)
            return '0 Bytes';

        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));

        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }

    /**
     * Check if the file exists in storage.
     */
    public function exists(): bool
    {
        return Storage::disk('public')->exists($this->file_path);
    }

    /**
     * Delete the file from storage.
     */
    public function deleteFile(): bool
    {
        if ($this->exists()) {
            return Storage::disk('public')->delete($this->file_path);
        }
        return true;
    }

    /**
     * Scope to filter files by folder.
     */
    public function scopeInFolder($query, $folderId)
    {
        return $query->where('folder_id', $folderId);
    }

    /**
     * Scope to filter files by operating unit.
     */
    public function scopeForOperatingUnit($query, $operatingUnitId)
    {
        return $query->where('operating_unit_id', $operatingUnitId);
    }

    /**
     * Scope to filter files by extension.
     */
    public function scopeByExtension($query, $extension)
    {
        return $query->where('extension', $extension);
    }
}
