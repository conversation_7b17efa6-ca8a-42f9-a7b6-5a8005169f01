<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Models\UserPreference;


class User extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'operating_unit_id',
        'department_unit_id',
        'role_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'anti_date_password' => 'hashed',
        ];
    }


    /**
     * 
     * 
     */
    public function preferences()
    {
        return $this->hasMany(UserPreference::class);
    }


    /**
     * Get the documents received by the user.
     */
    public function receivedDocuments(): HasMany
    {
        return $this->hasMany(Document::class, 'received_by_id');
    }

    /**
     * Get the document tracking records handled by the user.
     */
    public function handledDocuments(): HasMany
    {
        return $this->hasMany(DocumentTracking::class, 'handled_by_id');
    }

    /**
     * Get the role that the user belongs to.
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Check if the user is a super admin.
     *
     * @return bool
     */
    public function isSuperAdmin(): bool
    {
        return $this->role && $this->role->isSuperAdmin();
    }

    /**
     * Check if the user is an admin.
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->role && $this->role->isAdmin();
    }

    /**
     * Check if the user is a staff member.
     *
     * @return bool
     */
    public function isStaff(): bool
    {
        return $this->role && $this->role->isStaff();
    }

    /**
     * Check if the user belongs to the Central Administration operating unit.
     *
     * @return bool
     */
    public function isCentralAdministration(): bool
    {
        if (!$this->operatingUnit) {
            return false;
        }

        return $this->operatingUnit->isCentralAdministration();
    }

    /**
     * Get the operating unit that the user belongs to.
     */
    public function operatingUnit(): BelongsTo
    {
        return $this->belongsTo(OperatingUnit::class);
    }

    /**
     * Get the department unit that the user belongs to.
     */
    public function departmentUnit(): BelongsTo
    {
        return $this->belongsTo(DepartmentUnit::class);
    }
}
