<script setup>
import { useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const form = useForm({
    current_password: '',
    password: '',
    password_confirmation: '',
});

const successAlert = ref(false);
const showPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

const updatePassword = () => {
    form.put(route('password.update'), {
        preserveScroll: true,
        onSuccess: () => {
            form.reset();
            successAlert.value = true;
            setTimeout(() => {
                successAlert.value = false;
            }, 3000);
        }
    });
};
</script>

<template>
    <div>
        <p class="mt-5 mb-6 text-sm text-gray-600">
            Ensure your account is using a long, random password to stay secure.
        </p>

        <v-alert v-if="successAlert" type="success" variant="tonal" class="mb-4" density="compact">
            Password updated successfully.
        </v-alert>

        <form @submit.prevent="updatePassword">
            <v-row>
                <v-col cols="12" md="6">
                    <v-text-field v-model="form.current_password" label="Current Password" variant="outlined"
                        :error-messages="form.errors.current_password" :type="showPassword ? 'text' : 'password'"
                        :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                        @click:append-inner="showPassword = !showPassword" bg-color="white" density="comfortable"
                        autocomplete="current-password"></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                    <v-text-field v-model="form.password" label="New Password" variant="outlined"
                        :error-messages="form.errors.password" :type="showNewPassword ? 'text' : 'password'"
                        :append-inner-icon="showNewPassword ? 'mdi-eye-off' : 'mdi-eye'"
                        @click:append-inner="showNewPassword = !showNewPassword" bg-color="white" density="comfortable"
                        autocomplete="new-password"></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                    <v-text-field v-model="form.password_confirmation" label="Confirm New Password" variant="outlined"
                        :error-messages="form.errors.password_confirmation"
                        :type="showConfirmPassword ? 'text' : 'password'"
                        :append-inner-icon="showConfirmPassword ? 'mdi-eye-off' : 'mdi-eye'"
                        @click:append-inner="showConfirmPassword = !showConfirmPassword" bg-color="white"
                        density="comfortable" autocomplete="new-password"></v-text-field>
                </v-col>

                <v-col cols="12" class="justify-end d-flex">
                    <v-btn type="submit" color="primary" :loading="form.processing" :disabled="form.processing">
                        Update Password
                    </v-btn>
                </v-col>
            </v-row>
        </form>
    </div>
</template>
