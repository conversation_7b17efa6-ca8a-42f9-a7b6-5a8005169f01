<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_tracking_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('document_id')->constrained()->onDelete('cascade');
            $table->foreignId('handled_by_id')->constrained('users');
            $table->string('status');
            $table->text('location')->nullable();
            $table->text('notes')->nullable();
            $table->text('remarks')->nullable();
            $table->timestamp('tracked_at');
            $table->boolean('requires_action')->default(false);
            $table->date('action_deadline')->nullable();
            $table->boolean('action_completed')->default(false);
            $table->timestamp('action_completed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_tracking_history');
    }
};
