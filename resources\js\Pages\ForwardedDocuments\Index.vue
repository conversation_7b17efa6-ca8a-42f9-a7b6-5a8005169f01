<template>

    <Head title="Forwarded Documents" />

    <AppSidebarLayout>


        <v-container fluid class="px-4">
            <!-- Forwarded Documents Section -->
            <v-row>
                <v-col cols="12">
                    <v-card elevation="2">


                        <!-- Toolbar with tooltips -->
                        <div class="px-4 py-3 border-b d-flex align-center justify-space-between bg-grey-lighten-5">
                            <div class="d-flex align-center">
                                <v-tooltip location="bottom">
                                    <template v-slot:activator="{ props }">
                                        <v-btn icon variant="text" color="grey-darken-1" size="small" class="mr-1"
                                            @click="refreshDocuments" v-bind="props">
                                            <v-icon>mdi-refresh</v-icon>
                                        </v-btn>
                                    </template>
                                    Refresh
                                </v-tooltip>

                                <!-- Reference dropdown moved to left side -->
                                <v-select v-model="activeReference" :items="referenceOptions" item-title="title"
                                    item-value="value" label="Reference" variant="outlined" density="compact"
                                    hide-details class="ml-3 reference-select" bg-color="white" color="primary"
                                    @update:model-value="filterByReference">
                                    <template v-slot:prepend>
                                        <v-icon color="primary">mdi-filter-variant</v-icon>
                                    </template>
                                </v-select>
                            </div>

                            <!-- Right side controls: Search -->
                            <div class="d-flex align-center">
                                <!-- Search field -->
                                <div style="width: 300px">
                                    <v-text-field v-model="search" append-icon="mdi-magnify" label="Search documents..."
                                        single-line hide-details variant="outlined" density="compact"></v-text-field>
                                </div>
                            </div>
                        </div>

                        <!-- Data table for forwarded documents -->
                        <v-data-table :headers="headers" :items="filteredDocuments" :search="search" :loading="loading"
                            class="elevation-1" density="compact" fixed-header hover
                            :items-per-page-options="itemsPerPageOptions" :items-per-page="itemsPerPage"
                            @update:options="handleTableOptions">

                            <!-- Document column -->
                            <template v-slot:item.document="{ item }">
                                <div class="d-flex align-center" @click="viewForwardedDocument(item)"
                                    style="cursor: pointer">
                                    <v-icon color="success" size="small" class="mr-2">mdi-file-send-outline</v-icon>
                                    <span class="font-weight-medium">{{ item.document ?
                                        item.document.control_number : 'Unknown Document' }}</span>
                                </div>
                            </template>

                            <!-- Location column -->
                            <template v-slot:item.location="{ item }">
                                <div @click="viewForwardedDocument(item)" style="cursor: pointer">
                                    {{ item.location }}
                                </div>
                            </template>

                            <!-- Reference column -->
                            <template v-slot:item.reference="{ item }">
                                <div @click="viewForwardedDocument(item)" style="cursor: pointer">
                                    <v-chip v-if="item.document && item.document.reference_document" color="primary"
                                        size="x-small" class="text-white">
                                        {{ item.document.reference_document.replace('FO', 'F0') }}
                                    </v-chip>
                                    <span v-else>-</span>
                                </div>
                            </template>

                            <!-- Date column -->
                            <template v-slot:item.tracked_at="{ item }">
                                <div @click="viewForwardedDocument(item)" style="cursor: pointer">
                                    {{ formatDate(item.tracked_at) }}
                                </div>
                            </template>

                            <!-- Status column -->
                            <template v-slot:item.status="{ item }">
                                <div @click="viewForwardedDocument(item)" style="cursor: pointer">
                                    <v-chip :color="getStatusColor(item.status)" size="small" class="text-white">
                                        {{ item.status || 'Forwarded' }}
                                    </v-chip>
                                </div>
                            </template>

                            <!-- Actions column -->
                            <template v-slot:item.actions="{ item }">
                                <div class="justify-center d-flex">
                                    <v-tooltip location="top">
                                        <template v-slot:activator="{ props }">
                                            <v-btn icon size="small" color="blue-lighten-1"
                                                @click.stop="viewForwardedDocument(item)" v-bind="props" class="mr-2">
                                                <v-icon>mdi-eye</v-icon>
                                            </v-btn>
                                        </template>
                                        View Details
                                    </v-tooltip>

                                    <v-tooltip location="top">
                                        <template v-slot:activator="{ props }">
                                            <v-btn icon size="small" color="error" @click.stop="confirmDelete(item)"
                                                v-bind="props">
                                                <v-icon>mdi-delete</v-icon>
                                            </v-btn>
                                        </template>
                                        Delete
                                    </v-tooltip>
                                </div>
                            </template>

                            <!-- Empty state -->
                            <template v-slot:no-data>
                                <div class="text-center pa-8">
                                    <v-icon size="large" color="grey-lighten-1"
                                        class="mb-4">mdi-file-send-outline</v-icon>
                                    <div class="mb-2 text-h6">No forwarded documents</div>
                                    <div class="text-body-2">Documents you forward will appear here</div>
                                </div>
                            </template>
                        </v-data-table>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>

        <!-- Delete Confirmation Dialog -->
        <v-dialog v-model="deleteDialog" max-width="500">
            <v-card>
                <v-card-title class="text-white text-h5 bg-error">Confirm Delete</v-card-title>
                <v-card-text class="pt-4">
                    <p>Are you sure you want to delete this forwarded document record?</p>
                    <p v-if="documentToDelete && documentToDelete.document" class="mt-2 font-weight-medium">
                        Document: {{ documentToDelete.document.control_number }}
                    </p>
                    <p v-if="documentToDelete" class="mt-2 font-weight-medium">
                        Forwarded To: {{ documentToDelete.location }}
                    </p>
                    <p class="mt-4 text-error font-weight-bold">
                        This action cannot be undone.
                    </p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="grey-darken-1" variant="text" @click="deleteDialog = false">
                        Cancel
                    </v-btn>
                    <v-btn color="error" variant="text" @click="deleteDocument" :loading="deleting">
                        Delete
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

    </AppSidebarLayout>
</template>

<script setup>
import { Head } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import { router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const route = window.route;

const props = defineProps({
    forwardedDocuments: Object,
    activeReference: String,
});

const search = ref('');
const loading = ref(false);
const forwardedPage = ref(props.forwardedDocuments.current_page);
const itemsPerPage = ref(10);

// Delete dialog state
const deleteDialog = ref(false);
const documentToDelete = ref(null);
const deleting = ref(false);
const itemsPerPageOptions = [
    { title: '5', value: 5 },
    { title: '10', value: 10 },
    { title: '15', value: 15 },
    { title: '20', value: 20 },
    { title: 'All', value: -1 },
];

// Define table headers
const headers = [
    { title: 'Document', key: 'document', sortable: true },
    { title: 'Forwarded To', key: 'location', sortable: true },
    { title: 'Reference', key: 'reference', sortable: true, width: '120px' },
    { title: 'Date', key: 'tracked_at', sortable: true, width: '120px' },
    { title: 'Status', key: 'status', sortable: true, width: '120px' },
    { title: 'Actions', key: 'actions', sortable: false, width: '100px' },
];
const activeReference = ref(props.activeReference || null);

// Create reference options with all reference codes (F01-F04)
const referenceOptions = computed(() => {
    // Standard reference codes
    const standardReferences = [
        { title: 'F01', value: 'F01' },
        { title: 'F02', value: 'F02' },
        { title: 'F03', value: 'F03' },
        { title: 'F04', value: 'F04' }
    ];

    return standardReferences;
});

// Filter documents based on search term
const filteredDocuments = computed(() => {
    if (!props.forwardedDocuments || !props.forwardedDocuments.data) return [];

    return props.forwardedDocuments.data.filter(item => {
        if (!search.value) return true;

        // Search in document control number
        if (item.document && item.document.control_number &&
            item.document.control_number.toLowerCase().includes(search.value.toLowerCase())) {
            return true;
        }

        // Search in location
        if (item.location && item.location.toLowerCase().includes(search.value.toLowerCase())) {
            return true;
        }

        return false;
    });
});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);

    // If the message is from today, show only the time
    if (date.toDateString() === now.toDateString()) {
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
        });
    }
    // If the message is from yesterday, show "Yesterday"
    else if (date.toDateString() === yesterday.toDateString()) {
        return 'Yesterday';
    }
    // If the message is from this year, show month and day
    else if (date.getFullYear() === now.getFullYear()) {
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
        });
    }
    // Otherwise show the full date
    else {
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    }
};

// Get color for status chip based on status value
const getStatusColor = (status) => {
    if (!status) return 'info'; // Default for forwarded documents

    switch (status.toLowerCase()) {
        case 'pending':
            return 'warning';
        case 'in progress':
            return 'info';
        case 'completed':
            return 'success';
        case 'rejected':
            return 'error';
        case 'forwarded':
            return 'primary';
        case 'acknowledged':
            return 'success';
        default:
            return 'grey';
    }
};

// Handle table options changes (pagination, sorting, etc.)
const handleTableOptions = (options) => {
    if (options.itemsPerPage !== itemsPerPage.value) {
        itemsPerPage.value = options.itemsPerPage;
    }

    // Handle page changes
    if (options.page !== forwardedPage.value) {
        changeForwardedPage(options.page);
    }
};

const changeForwardedPage = (page) => {
    loading.value = true;

    // Preserve the active reference when changing pages
    let url = `/forwarded-documents?page=${page}`;
    if (activeReference.value) {
        url += `&reference=${activeReference.value}`;
    }

    router.visit(url, {
        preserveState: true,
        onSuccess: () => {
            loading.value = false;
            forwardedPage.value = page;
        },
        onError: (errors) => {
            loading.value = false;
            console.error('Error changing page:', errors);
        }
    });
};

// View forwarded document function
const viewForwardedDocument = (item) => {
    router.visit(route('forwarded-documents.show-forwarded', item.id));
};

// Filter by reference
const filterByReference = (reference) => {
    // Update the active reference immediately for better UI responsiveness
    activeReference.value = reference;

    // Navigate to the selected reference
    router.get(`/forwarded-documents?reference=${reference}`);
};

// Refresh documents
const refreshDocuments = () => {
    loading.value = true;
    router.reload({
        onSuccess: () => {
            loading.value = false;
        }
    });
};

// Confirm delete function
const confirmDelete = (item) => {
    documentToDelete.value = item;
    deleteDialog.value = true;
};

// Delete document function
const deleteDocument = () => {
    if (documentToDelete.value) {
        deleting.value = true;
        router.delete(route('forwarded-documents.destroy', documentToDelete.value.id), {
            onSuccess: () => {
                deleteDialog.value = false;
                documentToDelete.value = null;
                deleting.value = false;
            },
            onError: () => {
                deleting.value = false;
            }
        });
    }
};
</script>

<style scoped>
/* Basic styling */
.border-b {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

/* Table header styling */
:deep(.v-data-table-header th) {
    font-weight: bold;
    color: rgba(0, 0, 0, 0.87) !important;
}

:deep(.v-data-table .v-data-table__td) {
    color: rgba(0, 0, 0, 0.87) !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
}

/* Remove blue focus outline from input fields */
:deep(.v-field__input:focus-visible) {
    outline: none !important;
}

/* Table row hover styling */
:deep(.v-data-table .v-data-table__tr:hover) {
    background-color: rgba(25, 118, 210, 0.05) !important;
}

/* Pagination styling */
:deep(.v-data-table-footer) {
    border-top: thin solid rgba(0, 0, 0, 0.12);
    padding: 0 16px;
}

:deep(.v-data-table-footer__items-per-page) {
    margin-right: 16px;
}

:deep(.v-data-table-footer__info) {
    margin-right: 16px;
}

/* Button styling */
:deep(.v-btn--icon.v-btn--density-default) {
    width: 36px;
    height: 36px;
}

/* Tooltip styling */
:deep(.v-tooltip > .v-overlay__content) {
    background-color: rgba(33, 33, 33, 0.9);
    color: white;
    border-radius: 4px;
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* Reference dropdown styling */
.reference-select {
    min-width: 150px;
    max-width: 180px;
    margin-left: 8px;
}

/* Improve chip visibility */
.v-chip.v-chip--size-x-small {
    font-size: 12px;
    height: 22px;
    font-weight: 600;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Status chip styling */
.v-chip.v-chip--size-small {
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Hide the extra pagination arrows at the bottom of the page */
:deep(.flex.items-center.gap-4) {
    display: none !important;
}
</style>
