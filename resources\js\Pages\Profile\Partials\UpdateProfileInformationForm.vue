<script setup>
import { Link, useForm, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';

defineProps({
    mustVerifyEmail: {
        type: Boolean,
    },
    status: {
        type: String,
    },
});

const user = usePage().props.auth.user;

const form = useForm({
    name: user.name,
    email: user.email,
});

const successAlert = ref(false);

const updateProfile = () => {
    form.patch(route('profile.update'), {
        onSuccess: () => {
            successAlert.value = true;
            setTimeout(() => {
                successAlert.value = false;
            }, 3000);
        }
    });
};
</script>

<template>
    <div>
        <p class="mt-5 mb-6 text-sm text-gray-600">
            Update your account's profile information and email address.
        </p>

        <v-alert v-if="successAlert" type="success" variant="tonal" class="mb-4" density="compact">
            Profile information updated successfully.
        </v-alert>

        <form @submit.prevent="updateProfile">
            <v-row>
                <v-col cols="12" md="6">
                    <v-text-field v-model="form.name" label="Name" variant="outlined" :error-messages="form.errors.name"
                        required bg-color="white" density="comfortable" autofocus></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                    <v-text-field v-model="form.email" label="Email" variant="outlined"
                        :error-messages="form.errors.email" type="email" required bg-color="white"
                        density="comfortable"></v-text-field>
                </v-col>

                <v-col cols="12" v-if="mustVerifyEmail && user.email_verified_at === null">
                    <v-alert type="warning" variant="tonal" density="compact" class="mb-2">
                        Your email address is unverified.
                        <Link :href="route('verification.send')" method="post" as="button"
                            class="text-primary font-weight-medium text-decoration-none">
                        Click here to re-send the verification email.
                        </Link>
                    </v-alert>

                    <v-alert v-if="status === 'verification-link-sent'" type="success" variant="tonal"
                        density="compact">
                        A new verification link has been sent to your email address.
                    </v-alert>
                </v-col>

                <v-col cols="12" class="justify-end d-flex">
                    <v-btn type="submit" color="primary" :loading="form.processing" :disabled="form.processing">
                        Save Changes
                    </v-btn>
                </v-col>
            </v-row>
        </form>
    </div>
</template>
