<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\Inbox;
use App\Models\OperatingUnit;
use App\Models\DepartmentUnit;
use App\Models\User;
use App\Models\DocumentCode;
use App\Services\OperatingUnitSyncService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use BaconQrCode\Renderer\Image\SvgImageBackEnd;
use BaconQrCode\Renderer\ImageRenderer;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use BaconQrCode\Writer;

class DocumentController extends Controller
{
    /**
     * @var OperatingUnitSyncService
     */
    protected $syncService;

    /**
     * Constructor
     *
     * @param OperatingUnitSyncService $syncService
     */
    public function __construct(OperatingUnitSyncService $syncService)
    {
        $this->syncService = $syncService;
    }

    /**
     * Display a listing of the documents.
     */
    public function index(Request $request)
    {
        $user      = Auth::user();
        $documents = $this->getUserDocuments($request, $user);

        // Fetch the authenticated user's preferences
        $preferences = Auth::user()->preferences()
            ->get()
            ->mapWithKeys(function ($pref) {
                return [$pref->preference_key => $pref->preference_value];
            });

        return Inertia::render('Documents/Index', [
            'documents' => $documents,
            'preferences' => $preferences, // ✅ send to Vue
        ]);
    }

    /**
     * Show the form for creating a new document.
     */
    public function create(Request $request)
    {
        $user = Auth::user();

        $documentCodes = $user->operatingUnit->documentCodes()
            ->orderBy('operating_unit_code_value')
            ->get()
            ->flatMap(function ($code) {
                // Decode the JSON string (e.g., ["MEMO", "LETTER"])
                $values = json_decode($code->operating_unit_code_value, true);

                // Return as key-value array, or just a list
                return collect($values)->map(function ($item) {
                    return [
                        'label' => $item,
                        'value' => $item
                    ];
                });
            })
            ->values();

        // Get unique origin office values
        $originOffices = Document::select('origin')
            ->distinct()
            ->whereNotNull('origin')
            ->where('origin', '!=', '')
            ->orderBy('origin')
            ->pluck('origin')
            ->toArray();

        // Get unique signatory values
        $signatories = Document::select('signatory')
            ->distinct()
            ->whereNotNull('signatory')
            ->where('signatory', '!=', '')
            ->orderBy('signatory')
            ->pluck('signatory')
            ->toArray();

        // Reuse logic to fetch documents
        $documents = $this->getUserDocuments($request, $user);

        return Inertia::render('Documents/Create', [
            'documents' => $documents,
            'documentCodes' => $documentCodes,
            'originOffices' => $originOffices,
            'signatories' => $signatories
        ]);
    }

    private function getUserDocuments(Request $request, User $user)
    {
        // dd($request);
        $perPage = $request->input('per_page', -1);
        $getAllRecords = $perPage == -1;
        $page = $request->input('page', 1);

        if ($user->isSuperAdmin()) {
            $query = Document::with('receivedBy')->where('received_by_id', $user->id);
            if ($getAllRecords) {
                $allDocuments = $query->orderBy('created_at', 'desc')->get();
                return [
                    'data' => $allDocuments,
                    'total' => $allDocuments->count(),
                    'per_page' => -1,
                    'current_page' => 1,
                    'last_page' => 1,
                ];
            }
            return $query->orderBy('created_at', 'desc')->paginate($perPage);
        }

        if ($user->isAdmin() && $user->operating_unit_id) {
            $query = Document::with('receivedBy')->where('received_by_id', $user->id);

            if ($getAllRecords) {
                $allDocuments = $query->orderBy('created_at', 'desc')->get();
                return [
                    'data' => $allDocuments,
                    'total' => $allDocuments->count(),
                    'per_page' => -1,
                    'current_page' => 1,
                    'last_page' => 1,
                ];
            }

            return $query->orderBy('created_at', 'desc')->paginate($perPage);
        }

        // Default: normal user
        $query = Document::with('receivedBy')->where('received_by_id', $user->id);
        if ($getAllRecords) {
            $allDocuments = $query->orderBy('created_at', 'desc')->get();
            return [
                'data' => $allDocuments,
                'total' => $allDocuments->count(),
                'per_page' => -1,
                'current_page' => 1,
                'last_page' => 1,
            ];
        }
        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }


    /**
     * Store a newly created document in storage.
     */
    public function store(Request $request)
    {
        $validationRules = [
            'signatory' => 'required|string|max:255',
            'date_received' => 'required|date',
            'origin' => 'required|string|max:255',
            'subject_matter' => 'required|string',
            'referred_to' => 'required|string|max:255',
            'reference_document' => 'required|string|in:F01,F02,F03,F04',
        ];

        // Document code is only required for F02 and F04 reference documents
        if (in_array($request->reference_document, ['F02', 'F04'])) {
            $validationRules['document_code'] = 'required|string|max:255';
        } else {
            $validationRules['document_code'] = 'nullable|string|max:255';
        }

        $request->validate($validationRules);

        // Generate control number
        // $controlNumber = Document::generateControlNumber($request->reference_document);

        // Use F01 format directly (with zero, not letter O)
        // No conversion needed

        // Set document_code based on reference_document
        $documentCode = $request->document_code;
        if (!in_array($request->reference_document, ['F02', 'F04'])) {
            // For F01 and F03, set a default value for document_code to avoid null constraint
            $documentCode = 'N/A';
        }

        // Create document
        $document = Document::create([
            'signatory' => $request->signatory,
            'date_received' => $request->date_received,
            'origin' => $request->origin,
            'origin_unit_id' => $request->origin_unit_id,
            'subject_matter' => $request->subject_matter,
            'referred_to' => $request->referred_to,
            'document_code' => $documentCode,
            'reference_document' => $request->reference_document, // Use F01 format directly
            'control_number' => $request->control_number,
            'received_by_id' => Auth::id(),
            'status' => 'pending',
            // 'file_path' => $request->file_path,
        ]);

        return redirect()->route('documents.show', $document->id)
            ->with('success', 'Document registered successfully. Please generate and print the QR code.');
    }

    /**
     * Display the specified document.
     */
    public function show(Document $document, DocumentCode $documentCodes, Request $request)
    {

        // Check if the user is authorized to view this document
        $this->authorizeDocument($document);

        $document->load(['receivedBy', 'trackingHistory.handledBy']);

        // Get the authenticated user
        $user = Auth::user();

        // Get document codes for user's operating unit, decoded and flattened as label-value pairs
        $documentCodes = $user->operatingUnit->documentCodes()
            ->orderBy('operating_unit_code_value')
            ->get()
            ->flatMap(function ($code) {
                $values = json_decode($code->operating_unit_code_value, true) ?? [];

                return collect($values)->map(function ($item) {
                    return [
                        'label' => $item,
                        'value' => $item,
                    ];
                });
            })
            ->values();
        // Get the user's operating unit
        $userOperatingUnit = $user->operatingUnit;

        // Decode the JSON string stored in the DB

        // Get all operating units except the user's operating unit (if any)
        $operatingUnits = OperatingUnit::when($userOperatingUnit, function ($query) use ($userOperatingUnit) {
            return $query->where('id', '!=', $userOperatingUnit->id);
        })
            ->orderBy('name')
            ->get();

        // Get department units that belong to the user's operating unit
        $departmentUnits = [];
        if ($userOperatingUnit) {
            $departmentUnits = DepartmentUnit::where('operating_unit_id', $userOperatingUnit->id)
                ->orderBy('name')
                ->get();
        }

        $documents = $this->getUserDocuments($request, $user);

        return Inertia::render('Documents/Show', [
            'document' => $document,
            'documents' => $documents,
            'documentCodes' => $documentCodes,
            'operatingUnits' => $operatingUnits,
            'departmentUnits' => $departmentUnits,
            'errors' => session()->get('errors') ? session()->get('errors')->getBag('default')->getMessages() : (object) [],
        ]);
    }

    /**
     * Show the form for editing the specified document.
     */
    public function edit(Document $document)
    {
        // Check if the user is authorized to edit this document
        $this->authorizeDocument($document);

        $document->load(['receivedBy']);

        return Inertia::render('Documents/Edit', [
            'document' => $document
        ]);
    }

    /**
     * Update the specified document in storage.
     */
    public function update(Request $request, Document $document)
    {
        // Check if the user is authorized to update this document
        $this->authorizeDocument($document);
        $validationRules = [
            'signatory' => 'required|string|max:255',
            'origin' => 'required|string|max:255',
            'subject_matter' => 'required|string',
            'referred_to' => 'required|string|max:255',
            'document_code' => 'required|string|max:255',
            'status' => 'required|string|max:255',
        ];

        // Add date_received validation if it's being updated
        if ($request->has('date_received')) {
            $validationRules['date_received'] = 'required|date';
        }

        // Add file validation if a file is being uploaded
        if ($request->hasFile('file')) {
            $validationRules['file'] = 'required|file|mimes:doc,docx,xlsx,pdf,jpg,jpeg,png|max:20480';
        }

        $request->validate($validationRules);

        $updateData = [
            'signatory' => $request->signatory,
            'origin' => $request->origin,
            'subject_matter' => $request->subject_matter,
            'referred_to' => $request->referred_to,
            'document_code' => $request->document_code,
            'status' => $request->status,
        ];

        // Update date_received if provided
        if ($request->has('date_received')) {
            $updateData['date_received'] = $request->date_received;
        }

        // Handle file upload if a new file is provided
        if ($request->hasFile('file')) {
            // Delete the old file if it exists
            if ($document->file_path) {
                Storage::disk('public')->delete($document->file_path);
            }

            // Store the new file
            $filePath = $request->file('file')->store('documents', 'public');
            $updateData['file_path'] = $filePath;
        }

        $document->update($updateData);

        return redirect()->route('documents.show', $document->id)
            ->with('success', 'Document updated successfully.');
    }

    /**
     * Remove the specified document from storage.
     */
    public function destroy(Document $document)
    {
        // Check if the user is authorized to delete this document
        $this->authorizeDocument($document);
        // Delete associated files
        if ($document->file_path) {
            Storage::delete($document->file_path);
        }

        if ($document->qr_code_path) {
            Storage::delete($document->qr_code_path);
        }

        $document->delete();

        return redirect()->route('documents.index')
            ->with('success', 'Document deleted successfully.');
    }

    /**
     * Show the QR code generation page.
     */
    public function showGenerateQr(Document $document)
    {
        // Check if the user is authorized to generate QR code for this document
        $this->authorizeDocument($document);
        return Inertia::render('Documents/GenerateQr', [
            'document' => $document
        ]);
    }

    /**
     * Generate QR code for the document.
     */
    public function generateQr(Document $document)
    {
        // Check if the user is authorized to generate QR code for this document
        $this->authorizeDocument($document);
        try {
            // Create directory if it doesn't exist
            if (!Storage::exists('public/qrcodes')) {
                Storage::makeDirectory('public/qrcodes');
            }

            // Generate QR code with the control number using direct BaconQrCode library
            // This doesn't require Imagick extension
            $renderer = new ImageRenderer(
                new RendererStyle(200),
                new SvgImageBackEnd()
            );
            $writer = new Writer($renderer);
            $qrCode = $writer->writeString($document->control_number);

            // Save QR code to storage
            $qrCodePath = 'qrcodes/' . $document->control_number . '.svg';
            Storage::put('public/' . $qrCodePath, $qrCode);

            // Update document with QR code path
            $document->update([
                'qr_code_path' => $qrCodePath
            ]);

            return response()->json([
                'qr_code_url' => Storage::url('public/' . $qrCodePath),
                'document' => $document
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Show the document upload page.
     */
    public function showUpload(Document $document)
    {
        // Check if the user is authorized to upload for this document
        $this->authorizeDocument($document);
        return Inertia::render('Documents/Upload', [
            'document' => $document
        ]);
    }

    /**
     * Upload the scanned document.
     */
    public function upload(Request $request, Document $document)
    {
        // Check if the user is authorized to upload for this document
        $this->authorizeDocument($document);
        $request->validate([
            'file' => 'required|file|mimes:doc,docx,xlsx,pdf,jpg,jpeg,png|max:20480',
        ]);

        // Store the uploaded file
        $filePath = $request->file('file')->store('documents', 'public');

        // Update document with file path
        $document->update([
            'file_path' => $filePath
        ]);

        return redirect()->route('documents.show', $document->id)
            ->with('success', 'Document uploaded successfully.');
    }

    /**
     * Remove the document file.
     */
    public function removeFile(Document $document)
    {
        // Check if the user is authorized to modify this document
        $this->authorizeDocument($document);

        // Check if the document has a file
        if ($document->file_path) {
            // Delete the file from storage
            Storage::disk('public')->delete($document->file_path);

            // Update the document to remove the file path
            $document->update([
                'file_path' => null
            ]);

            return response()->json(['success' => true, 'message' => 'Document file removed successfully']);
        }

        return response()->json(['success' => false, 'message' => 'No document file found'], 404);
    }

    /**
     * Function for updating the document status.    
     */

    public function updateStatus(Request $request)
    {
        $document = Document::where('control_number', $request->old_control_number)->firstOrFail();

        $this->authorizeDocument($document);

        $request->validate([
            'status' => 'required|string|in:pending,in progress,completed,inactive',
            'remarks' => 'nullable|string|max:255',
            // 'file_path' => 'nullable|file|mimes:doc,docx,xlsx,pdf,jpg,jpeg,png|max:20480',
        ]);

        $document->update([
            'status' => $request->status,
            'remarks' => $request->remarks,
            // 'file_path' => $request->file_path
        ]);

        return redirect()->route('documents.show', $document->id)
            ->with('success', 'Document status updated successfully.');
    }

    /**
     * Show the form for forwarding a document.
     */
    public function showForward(Document $document)
    {
        // Check if the user is authorized to forward this document
        $this->authorizeDocument($document);
        $document = $document->load(['receivedBy', 'originUnit']);

        // Get the authenticated user
        $user = Auth::user();

        // Get the user's operating unit
        $userOperatingUnit = $user->operatingUnit;

        // Get all operating units except the user's operating unit (if any)
        $operatingUnits = OperatingUnit::when($userOperatingUnit, function ($query) use ($userOperatingUnit) {
            return $query->where('id', '!=', $userOperatingUnit->id);
        })
            ->orderBy('name')
            ->get();

        return Inertia::render('Documents/ForwardDocument', [
            'document' => $document,
            'operatingUnits' => $operatingUnits
        ]);
    }

    /**
     * Forward the document to another department/person.
     */
    public function forward(Request $request, Document $document)
    {
        // Check if the user is authorized to forward this document
        $this->authorizeDocument($document);

        // Validate the request based on recipient type and classification
        $validationRules = [
            'classification' => 'required|in:public,confidential',
            'priority' => 'required|in:urgent,routinary',
            'recipient_type' => 'required|in:operating_units,department_units',
        ];

        // For confidential documents, we need specific recipients
        if ($request->classification === 'confidential') {
            // Add validation rules based on recipient type
            if ($request->recipient_type === 'operating_units') {
                $validationRules['operating_units'] = 'required|array';
                $validationRules['operating_units.*'] = 'exists:operating_units,id';
            } else {
                $validationRules['department_units'] = 'required|array';
                $validationRules['department_units.*'] = 'exists:department_units,id';
            }
        }

        $request->validate($validationRules);

        // Get recipient names and create location string based on recipient type and classification
        $locationString = '';
        $recipientNames = '';

        // For public classification, get all recipients of the selected type
        if ($request->classification === 'public') {
            if ($request->recipient_type === 'operating_units') {
                // Get all operating units
                $operatingUnits = OperatingUnit::all();
                $request->operating_units = $operatingUnits->pluck('id')->toArray();
                $recipientNames = $operatingUnits->pluck('name')->implode(', ');
                $locationString = 'All Operating Units: ' . $recipientNames;
            } else {
                // Get all department units for the user's operating unit
                $user = Auth::user();
                $departmentUnits = DepartmentUnit::where('operating_unit_id', $user->operating_unit_id)->get();
                $request->department_units = $departmentUnits->pluck('id')->toArray();
                $recipientNames = $departmentUnits->pluck('name')->implode(', ');
                $locationString = 'All Department Units: ' . $recipientNames;
            }
        } else {
            // For confidential classification, use the selected recipients
            if ($request->recipient_type === 'operating_units') {
                // Get the selected operating units
                $operatingUnits = OperatingUnit::whereIn('id', $request->operating_units)->get();
                $recipientNames = $operatingUnits->pluck('name')->implode(', ');
                $locationString = 'Selected Operating Units: ' . $recipientNames;
            } else {
                // Get the selected department units
                $departmentUnits = DepartmentUnit::whereIn('id', $request->department_units)->get();
                $recipientNames = $departmentUnits->pluck('name')->implode(', ');
                $locationString = 'Selected Department Units: ' . $recipientNames;
            }
        }

        // Create a tracking record
        $document->trackingHistory()->create([
            'status' => 'forwarded',
            'location' => $locationString,
            'notes' => 'Forwarded to ' . $recipientNames .
                '. Recipient Type: ' . ($request->recipient_type === 'operating_units' ? 'Operating Units' : 'Department Units') .
                ($request->classification === 'public' ? ' (All)' : ' (Selected)') .
                '. Classification: ' . ucfirst($request->classification) .
                '. Priority: ' . ucfirst($request->priority),
            'remarks' => $request->remarks,
            'handled_by_id' => Auth::id(),
            'tracked_at' => now(),
            'requires_action' => false,
            'action_deadline' => null,
        ]);

        // Update document status
        $document->update([
            'status' => 'in progress'
        ]);

        // Create inbox messages based on recipient type
        if ($request->recipient_type === 'operating_units') {
            // Create inbox messages for users in the selected operating units
            foreach ($request->operating_units as $unitId) {
                // Get users from this operating unit
                $users = User::where('operating_unit_id', $unitId)->get();

                foreach ($users as $user) {
                    // Skip sending to self
                    if ($user->id === Auth::id()) {
                        continue;
                    }

                    // Create inbox message with simplified text (document preview will be shown directly)
                    $inboxMessage = Inbox::create([
                        'subject' => 'Document Forwarded: ' . $document->control_number,
                        'message' => 'Classification: ' . ucfirst($request->classification) . '\n' .
                            'Priority: ' . ucfirst($request->priority) . '\n' .
                            'Recipient Type: Operating Units' .
                            ($request->classification === 'public' ? ' (All)' : ' (Selected)'),
                        'sender_id' => Auth::id(),
                        'recipient_id' => $user->id,
                        'document_id' => $document->id,
                        'reference' => $document->reference_document,
                        'priority' => $this->mapPriorityToInboxPriority($request->priority),
                    ]);

                    // Log for debugging
                    Log::info('Created inbox message for operating unit user', [
                        'inbox_id' => $inboxMessage->id,
                        'document_id' => $document->id,
                        'reference' => $document->reference_document
                    ]);
                }
            }
        } else {
            // Create inbox messages for users in the selected department units
            foreach ($request->department_units as $deptId) {
                // Get users from this department unit
                $users = User::where('department_unit_id', $deptId)->get();

                foreach ($users as $user) {
                    // Skip sending to self
                    if ($user->id === Auth::id()) {
                        continue;
                    }

                    // Create inbox message with simplified text (document preview will be shown directly)
                    $inboxMessage = Inbox::create([
                        'subject' => 'Document Forwarded: ' . $document->control_number,
                        'message' => 'Classification: ' . ucfirst($request->classification) . '\n' .
                            'Priority: ' . ucfirst($request->priority) . '\n' .
                            'Recipient Type: Department Units' .
                            ($request->classification === 'public' ? ' (All)' : ' (Selected)'),
                        'sender_id' => Auth::id(),
                        'recipient_id' => $user->id,
                        'document_id' => $document->id,
                        'reference' => $document->reference_document,
                        'priority' => $this->mapPriorityToInboxPriority($request->priority),
                    ]);

                    // Log for debugging
                    Log::info('Created inbox message for department unit user', [
                        'inbox_id' => $inboxMessage->id,
                        'document_id' => $document->id,
                        'reference' => $document->reference_document
                    ]);
                }
            }
        }

        return redirect()->route('documents.show', $document->id)
            ->with('success', 'Document forwarded successfully.');
    }

    /**
     * Map the document priority to inbox priority.
     *
     * @param string $priority
     * @return string
     */
    private function mapPriorityToInboxPriority(string $priority)
    {
        switch ($priority) {
            case 'urgent':
                return 'urgent';
            case 'routinary':
                return 'routinary';
        }
    }

    /**
     * Verify user password for anti-date feature.
     *
     * @param Request $request
     * @param Document $document
    //  * @return \Illuminate\Http\JsonResponse
     */
    public function verifyPassword(Request $request, Document $document)
    {
        // Check if the user is authorized to modify this document
        $this->authorizeDocument($document);

        $request->validate([
            'anti_date_password' => 'required|string',
        ]);

        // Get the authenticated user
        $user = Auth::user();

        // Verify the password
        if (!Hash::check($request->anti_date_password, $user->anti_date_password)) {
            throw ValidationException::withMessages([
                'anti_date_password' => 'The provided password is incorrect.',
            ]);
        }

        // If we get here, the password is correct
        return;
    }


    /**
     * Check if the user is authorized to access the document.
     * Throws an exception if not authorized.
     *
     * @param Document $document
     * @return void
     */
    private function authorizeDocument(Document $document): void
    {
        // Get the authenticated user
        $user = Auth::user();

        // Super admin can access all documents
        if ($user->isSuperAdmin()) {
            return;
        }

        // Admin can access documents from their operating unit
        if ($user->isAdmin()) {
            // Check if the document belongs to the admin's operating unit
            // This includes documents where current_unit_id matches the admin's operating unit
            if ($document->current_unit_id === $user->operating_unit_id) {
                return;
            }

            // Check if the document was received by a user from the same operating unit
            $receivedBy = $document->receivedBy;
            if ($receivedBy && $receivedBy->operating_unit_id === $user->operating_unit_id) {
                return;
            }

            // Or if the document was received by the admin themselves
            if ($document->received_by_id === $user->id) {
                return;
            }
        } else {
            // Staff can only access their own documents
            if ($document->received_by_id === $user->id) {
                return;
            }
        }

        // If we get here, the user is not authorized
        abort(403, 'You are not authorized to access this document.');
    }
}
