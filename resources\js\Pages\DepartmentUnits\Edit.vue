<template>
    <Head :title="'Edit Department Unit - ' + departmentUnit.name" />
    <AppSidebarLayout>
        <v-container>
            <v-row>
                <v-col cols="12" class="d-flex justify-space-between align-center">
                    <h1 class="text-h4">Edit Department Unit</h1>
                    <Link :href="route('department-units.index')" class="text-decoration-none">
                    <v-btn color="primary" variant="outlined" prepend-icon="mdi-arrow-left">
                        Back to List
                    </v-btn>
                    </Link>
                </v-col>
            </v-row>

            <v-row>
                <v-col cols="12">
                    <v-card elevation="2">
                        <v-card-title class="mb-5 text-white bg-primary">Department Unit Details</v-card-title>
                        <v-card-text>
                            <v-form @submit.prevent="submit">
                                <v-row>
                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.name" label="Name" :error-messages="errors.name"
                                            required variant="outlined" bg-color="white" color="primary"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.code" label="Code" :error-messages="errors.code"
                                            required hint="Unique identifier for the department unit" persistent-hint
                                            variant="outlined" bg-color="white" color="primary"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" class="justify-end d-flex">
                                        <v-btn type="submit" color="primary" size="large" :loading="form.processing">
                                            Update Department Unit
                                        </v-btn>
                                    </v-col>
                                </v-row>
                            </v-form>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </AppSidebarLayout>
</template>

<script setup>
import { Head, useForm, Link } from '@inertiajs/vue3';
const route = window.route;

import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
    departmentUnit: Object,
    errors: Object,
});

const form = useForm({
    name: props.departmentUnit.name,
    code: props.departmentUnit.code,
});

const submit = () => {
    form.put(route('department-units.update', props.departmentUnit.id), {
        onSuccess: () => {
            // Redirect handled by controller
        },
    });
};
</script>

<style scoped>
/* Table header styling */
:deep(.v-data-table-header th) {
    font-weight: bold;
    color: rgba(0, 0, 0, 0.87) !important;
}

:deep(.v-data-table .v-data-table__td) {
    color: rgba(0, 0, 0, 0.87) !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
}

/* Remove blue focus outline from input fields */
:deep(.v-field__input:focus-visible) {
    outline: none !important;
}
</style>
