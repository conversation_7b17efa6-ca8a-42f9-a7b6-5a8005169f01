<?php

namespace App\Observers;

use App\Models\Document;
use App\Services\OperatingUnitSyncService;
use Illuminate\Support\Facades\Log;

class DocumentObserver
{
    /**
     * @var OperatingUnitSyncService
     */
    protected $syncService;

    /**
     * Constructor
     *
     * @param OperatingUnitSyncService $syncService
     */
    public function __construct(OperatingUnitSyncService $syncService)
    {
        $this->syncService = $syncService;
    }

    /**
     * Handle the Document "created" event.
     */
    public function created(Document $document): void
    {
        $this->clearCacheForDocument($document);
        Log::info('Document created, cache cleared', ['document_id' => $document->id]);
    }

    /**
     * Handle the Document "updated" event.
     */
    public function updated(Document $document): void
    {
        $this->clearCacheForDocument($document);
        Log::info('Document updated, cache cleared', ['document_id' => $document->id]);
    }

    /**
     * Handle the Document "deleted" event.
     */
    public function deleted(Document $document): void
    {
        $this->clearCacheForDocument($document);
        Log::info('Document deleted, cache cleared', ['document_id' => $document->id]);
    }

    /**
     * Clear cache for the document's operating unit
     */
    protected function clearCacheForDocument(Document $document): void
    {
        // Clear cache for the document's origin unit
        if ($document->origin_unit_id) {
            $this->syncService->clearOperatingUnitCache($document->origin_unit_id);
        }

        // Clear cache for the document's current unit
        if ($document->current_unit_id) {
            $this->syncService->clearOperatingUnitCache($document->current_unit_id);
        }

        // Clear cache for the document receiver's operating unit
        if ($document->receivedBy && $document->receivedBy->operating_unit_id) {
            $this->syncService->clearOperatingUnitCache($document->receivedBy->operating_unit_id);
        }
    }
}
