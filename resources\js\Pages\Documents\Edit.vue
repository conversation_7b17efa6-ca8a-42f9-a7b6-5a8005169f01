<template>

    <Head title="Edit Document" />
    <AppSidebarLayout>
        <v-container>
            <v-row>
                <v-col cols="12" class="d-flex justify-space-between align-center">
                    <h1 class="mb-4 text-h4">Edit Document</h1>
                    <v-btn color="primary" variant="outlined" @click="goToDocumentDetails">
                        Cancel
                    </v-btn>
                </v-col>
            </v-row>

            <v-row>
                <v-col cols="12">
                    <v-card>
                        <v-card-text>
                            <v-form @submit.prevent="submit">
                                <v-row>
                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.signatory" label="Signatory"
                                            :error-messages="errors.signatory || validationErrors.signatory" required
                                            variant="outlined" bg-color="white" color="primary"
                                            :error="!!validationErrors.signatory"
                                            @input="validationErrors.signatory = ''"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.origin" label="Origin"
                                            :error-messages="errors.origin || validationErrors.origin" required
                                            variant="outlined" bg-color="white" color="primary"
                                            :error="!!validationErrors.origin"
                                            @input="validationErrors.origin = ''"></v-text-field>
                                    </v-col>

                                    <v-col cols="12">
                                        <v-textarea v-model="form.subject_matter" label="Subject Matter of the Document"
                                            :error-messages="errors.subject_matter || validationErrors.subject_matter"
                                            required variant="outlined" bg-color="white" color="primary"
                                            :error="!!validationErrors.subject_matter"
                                            @input="validationErrors.subject_matter = ''" rows="3"></v-textarea>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.referred_to" label="Referred to"
                                            :error-messages="errors.referred_to || validationErrors.referred_to"
                                            required variant="outlined" bg-color="white" color="primary"
                                            :error="!!validationErrors.referred_to"
                                            @input="validationErrors.referred_to = ''"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.document_code" label="Document Code"
                                            :error-messages="errors.document_code || validationErrors.document_code"
                                            required variant="outlined" bg-color="white" color="primary"
                                            :error="!!validationErrors.document_code"
                                            @input="validationErrors.document_code = ''"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-text-field v-model="form.control_number" label="Control Number" disabled
                                            readonly variant="outlined" bg-color="white"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-select v-model="form.status" label="Status" :items="statusOptions"
                                            :error-messages="errors.status || validationErrors.status" required
                                            variant="outlined" bg-color="white" color="primary"
                                            :error="!!validationErrors.status"
                                            @update:model-value="validationErrors.status = ''"></v-select>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <v-text-field
                                            :model-value="document.received_by ? document.received_by.name : 'N/A'"
                                            label="Received by" readonly disabled variant="outlined"
                                            bg-color="white"></v-text-field>
                                    </v-col>

                                    <v-col cols="12" md="6">
                                        <div class="d-flex align-center">
                                            <v-text-field v-if="editDateMode" v-model="form.document_date"
                                                label="Date Received" type="date" variant="outlined" bg-color="white"
                                                color="primary"
                                                :error-messages="form.errors.date_received || validationErrors.date_received"
                                                :error="!!validationErrors.date_received"
                                                @input="validationErrors.date_received = ''"
                                                class="flex-grow-1"></v-text-field>
                                            <v-text-field v-else
                                                :model-value="document.date_received ? formatDate(document.date_received, true) : 'N/A'"
                                                label="Date Received" readonly disabled variant="outlined"
                                                bg-color="white" class="flex-grow-1"></v-text-field>
                                            <v-tooltip text="Anti-Date: Modify the received date">
                                                <template v-slot:activator="{ props }">
                                                    <v-btn v-bind="props" icon color="warning" class="ml-2"
                                                        @click="showAntiDateDialog" :disabled="editDateMode">
                                                        <v-icon>mdi-calendar-edit</v-icon>
                                                    </v-btn>
                                                </template>
                                            </v-tooltip>
                                        </div>
                                    </v-col>

                                    <v-col cols="12">
                                        <v-divider class="my-4"></v-divider>
                                        <h3 class="mb-4 text-h6">Document File</h3>

                                        <div v-if="document.file_path" class="mb-4">
                                            <div class="mb-2 d-flex align-center">
                                                <v-icon color="success" class="mr-2">mdi-file-check</v-icon>
                                                <span>Current file: {{ getFileName(document.file_path) }}</span>
                                            </div>

                                            <div class="d-flex align-center">
                                                <v-btn color="primary" variant="outlined" size="small"
                                                    :href="`/storage/${document.file_path}`" target="_blank"
                                                    prepend-icon="mdi-eye" class="mr-2">
                                                    View File
                                                </v-btn>

                                                <v-btn color="error" variant="outlined" size="small"
                                                    @click="showReplaceFile = true" prepend-icon="mdi-file-replace"
                                                    v-if="!showReplaceFile">
                                                    Replace File
                                                </v-btn>
                                            </div>
                                        </div>

                                        <div v-if="showReplaceFile || !document.file_path">
                                            <v-file-input v-model="form.file" label="Upload Document File"
                                                accept=".pdf,.jpg,.jpeg,.png"
                                                :error-messages="errors.file || validationErrors.file"
                                                prepend-icon="mdi-file-upload" show-size variant="outlined"
                                                bg-color="white" color="primary" counter
                                                :hint="document.file_path ? 'This will replace the existing file' : 'Upload a document file'"
                                                persistent-hint :error="!!validationErrors.file"
                                                @update:model-value="validationErrors.file = ''"></v-file-input>

                                            <div v-if="showReplaceFile" class="mt-2">
                                                <v-btn color="grey" variant="text" size="small"
                                                    @click="cancelReplaceFile" class="mr-2">
                                                    Cancel
                                                </v-btn>
                                            </div>
                                        </div>
                                    </v-col>

                                    <v-col cols="12" class="justify-end d-flex">
                                        <v-btn type="submit" color="primary" size="large" :loading="form.processing"
                                            :disabled="hasValidationErrors">
                                            Update Document
                                        </v-btn>
                                    </v-col>
                                </v-row>
                            </v-form>
                        </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
        <!-- Anti-Date Password Verification Dialog -->
        <v-dialog v-model="antiDateDialog" max-width="500" persistent>
            <v-card>
                <v-card-title class="text-white text-h5 bg-warning">Password Verification Required</v-card-title>
                <v-card-text class="pt-4">
                    <p class="mb-4">
                        <strong>Warning:</strong> You are about to modify the received date of this document.
                        This action requires password verification for security purposes.
                    </p>
                    <p class="mb-4">
                        Please enter your password to continue:
                    </p>
                    <v-text-field v-model="passwordForm.password" label="Password"
                        :type="showPassword ? 'text' : 'password'"
                        :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                        @click:append-inner="showPassword = !showPassword" variant="outlined" bg-color="white"
                        :error-messages="passwordForm.errors.password" @keyup.enter="verifyPassword"></v-text-field>
                    <div v-if="passwordError" class="mb-2 text-error">
                        {{ passwordError }}
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="grey-darken-1" variant="text" @click="cancelAntiDate">
                        Cancel
                    </v-btn>
                    <v-btn color="warning" variant="text" @click="verifyPassword" :loading="verifying">
                        Verify
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </AppSidebarLayout>
</template>

<script setup>
import { Head, useForm, router } from '@inertiajs/vue3';
import { ref, computed, watch } from 'vue';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
    document: Object,
    errors: Object,
});

const statusOptions = [
    'pending',
    'in progress',
    'completed',
    'rejected',
];

// UI state variables
const showReplaceFile = ref(false);
const editDateMode = ref(false);
const antiDateDialog = ref(false);
const showPassword = ref(false);
const verifying = ref(false);
const passwordError = ref('');

// Custom validation errors
const validationErrors = ref({
    signatory: '',
    origin: '',
    subject_matter: '',
    referred_to: '',
    document_code: '',
    status: '',
    document_date: '',
    file: '',
});

// Computed property to check if there are any validation errors
const hasValidationErrors = computed(() => {
    return Object.values(validationErrors.value).some(error => error !== '');
});

// Format date for display
const formatDate = (dateString, dateOnly = false) => {
    const date = new Date(dateString);
    if (dateOnly) {
        return date.toLocaleDateString();
    }
    return date.toLocaleString();
};

// Format date for input field
const formatDateForInput = (dateString) => {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input type="date"
};

const getFileName = (path) => {
    if (!path) return '';
    return path.split('/').pop();
};

const cancelReplaceFile = () => {
    showReplaceFile.value = false;
    form.file = null;
};

// Main form for document editing
const form = useForm({
    signatory: props.document.signatory,
    origin: props.document.origin,
    subject_matter: props.document.subject_matter,
    referred_to: props.document.referred_to,
    document_code: props.document.document_code,
    control_number: props.document.control_number,
    status: props.document.status,
    date_received: formatDateForInput(props.document.date_received),
    file: null,
    _method: 'PUT',
});

// Password verification form
const passwordForm = useForm({
    password: '',
    document_id: props.document.id,
});

// Watch for changes in form fields to clear validation errors
watch(() => form.signatory, () => {
    validationErrors.value.signatory = '';
});

watch(() => form.origin, () => {
    validationErrors.value.origin = '';
});

watch(() => form.subject_matter, () => {
    validationErrors.value.subject_matter = '';
});

watch(() => form.referred_to, () => {
    validationErrors.value.referred_to = '';
});

watch(() => form.document_code, () => {
    validationErrors.value.document_code = '';
});

watch(() => form.status, () => {
    validationErrors.value.status = '';
});

watch(() => form.document_date, () => {
    validationErrors.value.date_received = '';
});

watch(() => form.file, () => {
    validationErrors.value.file = '';
});

// Show the anti-date dialog
const showAntiDateDialog = () => {
    passwordForm.reset();
    passwordError.value = '';
    antiDateDialog.value = true;
};

// Cancel the anti-date operation
const cancelAntiDate = () => {
    antiDateDialog.value = false;
    passwordForm.reset();
    passwordError.value = '';
};

// Verify password and enable date editing if successful
const verifyPassword = () => {
    verifying.value = true;
    passwordError.value = '';

    // Send password to server for verification
    passwordForm.post(`/documents/${props.document.id}/verify-password`, {
        preserveScroll: true,
        onSuccess: () => {
            verifying.value = false;
            antiDateDialog.value = false;
            editDateMode.value = true;
        },
        onError: (errors) => {
            verifying.value = false;
            if (errors.password) {
                passwordError.value = errors.password;
            } else {
                passwordError.value = 'An error occurred during verification. Please try again.';
            }
        }
    });
};

const goToDocumentDetails = () => {
    router.visit(`/documents/${props.document.id}`);
};

// Validate form before submission
const validateForm = () => {
    let isValid = true;

    // Reset all validation errors
    Object.keys(validationErrors.value).forEach(key => {
        validationErrors.value[key] = '';
    });

    // Validate signatory
    if (!form.signatory) {
        validationErrors.value.signatory = 'Signatory is required';
        isValid = false;
    } else if (form.signatory.length > 255) {
        validationErrors.value.signatory = 'Signatory must be less than 255 characters';
        isValid = false;
    }

    // Validate origin
    if (!form.origin) {
        validationErrors.value.origin = 'Origin is required';
        isValid = false;
    } else if (form.origin.length > 255) {
        validationErrors.value.origin = 'Origin must be less than 255 characters';
        isValid = false;
    }

    // Validate subject matter
    if (!form.subject_matter) {
        validationErrors.value.subject_matter = 'Subject matter is required';
        isValid = false;
    }

    // Validate referred to
    if (!form.referred_to) {
        validationErrors.value.referred_to = 'Referred to is required';
        isValid = false;
    } else if (form.referred_to.length > 255) {
        validationErrors.value.referred_to = 'Referred to must be less than 255 characters';
        isValid = false;
    }

    // Validate document code
    if (!form.document_code) {
        validationErrors.value.document_code = 'Document code is required';
        isValid = false;
    } else if (form.document_code.length > 255) {
        validationErrors.value.document_code = 'Document code must be less than 255 characters';
        isValid = false;
    }

    // Validate status
    if (!form.status) {
        validationErrors.value.status = 'Status is required';
        isValid = false;
    }

    // Validate date received if in edit mode
    if (editDateMode.value && !form.document_date) {
        validationErrors.value.date_received = 'Date received is required';
        isValid = false;
    }

    // Validate file if uploading
    if (form.file) {
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (!allowedTypes.includes(form.file.type)) {
            validationErrors.value.file = 'File must be a PDF, JPG, JPEG, or PNG';
            isValid = false;
        } else if (form.file.size > maxSize) {
            validationErrors.value.file = 'File size must be less than 10MB';
            isValid = false;
        }
    }

    return isValid;
};

const submit = () => {
    // Perform client-side validation first
    if (!validateForm()) {
        return;
    }

    // If we have a file, we need to use post with _method: 'PUT' for file uploads
    if (form.file) {
        form.post(`/documents/${props.document.id}`, {
            onSuccess: () => {
                showReplaceFile.value = false;
                editDateMode.value = false;
                router.visit(`/documents/${props.document.id}`);
            },
            onError: (errors) => {
                // Map backend errors to our validation errors if needed
                Object.keys(errors).forEach(key => {
                    if (validationErrors.value.hasOwnProperty(key)) {
                        validationErrors.value[key] = errors[key];
                    }
                });
            }
        });
    } else {
        // If no file, we can use the regular put method
        form.put(`/documents/${props.document.id}`, {
            onSuccess: () => {
                editDateMode.value = false;
                router.visit(`/documents/${props.document.id}`);
            },
            onError: (errors) => {
                // Map backend errors to our validation errors if needed
                Object.keys(errors).forEach(key => {
                    if (validationErrors.value.hasOwnProperty(key)) {
                        validationErrors.value[key] = errors[key];
                    }
                });
            }
        });
    }
};
</script>

<style scoped>
/* Error message styling */
:deep(.v-messages__message) {
    color: rgb(var(--v-theme-error)) !important;
    font-size: 0.875rem !important;
    margin-top: 4px !important;
    font-weight: 500 !important;
}

/* Increase text size in all form inputs */
:deep(.v-field__input),
:deep(.v-select__selection),
:deep(.v-textarea__input) {
    font-size: 1.1rem !important;
}

/* Increase height of form inputs for better touch targets */
:deep(.v-input--density-compact .v-field__input) {
    min-height: 44px !important;
    padding-top: 10px !important;
    padding-bottom: 10px !important;
}
</style>
