<template>

  <Head title="Users Management" />

  <AppSidebarLayout>


    <v-container>
      <v-row>
        <v-col cols="12">
          <v-card elevation="2">
            <v-card-title class="flex-wrap mt-2 d-flex align-center pa-4">
              <!-- Search field on the left side -->
              <div style="width: 300px" class="mr-4">
                <v-text-field v-model="search" append-icon="mdi-magnify" label="Search users..." single-line
                  hide-details variant="outlined" density="compact"></v-text-field>
              </div>

              <v-spacer></v-spacer>

              <!-- Add New User button on the right side -->
              <v-btn class="bg-blue-lighten-1" prepend-icon="mdi-plus" @click="userTypeDialog = true">
                Add New User
              </v-btn>
            </v-card-title>

            <v-data-table :headers="headers" :items="users.data" :search="search" :loading="loading" class="elevation-1"
              density="compact" fixed-header hover :items-per-page-options="itemsPerPageOptions"
              :items-per-page="itemsPerPage" @update:options="handleTableOptions">
              <template v-slot:item.role="{ item }">
                <v-chip v-if="item.role" :color="getRoleColor(item.role.slug)" size="small" class="text-white">
                  {{ item.role.name }}
                </v-chip>
                <v-chip v-else color="grey" size="small" class="text-white">
                  Not Assigned
                </v-chip>
              </template>

              <template v-slot:item.operating_unit="{ item }">
                {{
                  item.operating_unit
                    ? item.operating_unit.name
                    : "Not Assigned"
                }}
              </template>

              <template v-slot:item.actions="{ item }">
                <div class="d-flex justify-center">
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <Link :href="route('users.show', item.id)" class="text-decoration-none mr-2">
                      <v-btn icon size="small" color="blue-lighten-1" v-bind="props">
                        <v-icon>mdi-eye</v-icon>
                      </v-btn>
                      </Link>
                    </template>
                    View
                  </v-tooltip>

                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <Link :href="route('users.edit', item.id)" class="text-decoration-none mr-2">
                      <v-btn icon size="small" color="secondary" v-bind="props">
                        <v-icon>mdi-pencil</v-icon>
                      </v-btn>
                      </Link>
                    </template>
                    Edit
                  </v-tooltip>

                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <v-btn icon size="small" color="error" @click="confirmDelete(item)" v-bind="props">
                        <v-icon>mdi-delete</v-icon>
                      </v-btn>
                    </template>
                    Delete
                  </v-tooltip>
                </div>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
      </v-row>
    </v-container>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="deleteDialog" max-width="500">
      <v-card>
        <v-card-title class="text-white text-h5 bg-error">Confirm Delete</v-card-title>
        <v-card-text class="pt-4">
          <p>Are you sure you want to delete this user?</p>
          <p v-if="userToDelete" class="mt-2 font-weight-medium">
            User: {{ userToDelete.name }} ({{ userToDelete.email }})
          </p>
          <p class="mt-4 text-error font-weight-bold">
            This action cannot be undone.
          </p>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey-darken-1" variant="text" @click="deleteDialog = false">
            Cancel
          </v-btn>
          <v-btn color="error" variant="text" @click="deleteUser" :loading="deleting">
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- User Type Selection Dialog -->
    <v-dialog v-model="userTypeDialog" max-width="600" persistent>
      <v-card>
        <v-card-title class="text-white text-h5 bg-primary">Select User Type</v-card-title>
        <v-card-text class="pt-4">
          <v-container>
            <v-row>
              <v-col cols="12">
                <div class="mb-6">
                  <div class="d-flex align-center mb-2">
                    <v-icon color="primary" class="mr-2">mdi-office-building</v-icon>
                    <span class="text-h6">Operating Unit User</span>
                  </div>
                  <div class="text-body-2 text-grey-darken-1 ml-8 mb-4">
                    User will be assigned to an operating unit and will have access to all documents within that unit.
                  </div>
                  <div class="ml-8">
                    <v-btn color="primary" prepend-icon="mdi-plus" @click="navigateToUserCreation('operating_unit')">
                      Create Operating Unit User
                    </v-btn>
                  </div>
                </div>

                <v-divider class="my-6"></v-divider>

                <div class="mt-6">
                  <div class="d-flex align-center mb-2">
                    <v-icon color="indigo" class="mr-2">mdi-domain</v-icon>
                    <span class="text-h6">Department Unit User</span>
                  </div>
                  <div class="text-body-2 text-grey-darken-1 ml-8 mb-4">
                    User will be assigned to a specific department unit within an operating unit and will have access
                    only
                    to documents related to that department.
                  </div>
                  <div class="ml-8">
                    <v-btn color="indigo" prepend-icon="mdi-plus" @click="navigateToUserCreation('department_unit')">
                      Create Department Unit User
                    </v-btn>
                  </div>
                </div>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey-darken-1" variant="text" @click="userTypeDialog = false">
            Cancel
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import { router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
  users: Object,
});

const headers = [
  { title: 'Name', key: 'name', width: '25%' },
  { title: 'Email', key: 'email', width: '25%' },
  { title: 'Role', key: 'role', width: '15%' },
  { title: 'Operating Unit', key: 'operating_unit', width: '20%' },
  { title: 'Actions', key: 'actions', sortable: false, width: '15%', align: 'center' },
];

const search = ref('');
const loading = ref(false);
const deleting = ref(false);
const currentPage = ref(props.users.current_page);
const lastPage = computed(() => props.users.last_page);
const deleteDialog = ref(false);
const userToDelete = ref(null);
const itemsPerPage = ref(10);
const itemsPerPageOptions = [
  { title: '5', value: 5 },
  { title: '10', value: 10 },
  { title: '15', value: 15 },
  { title: '20', value: 20 },
  { title: 'All', value: -1 },
];

// User type selection dialog
const userTypeDialog = ref(false);

// Function to navigate directly to the user creation form with the specified type
const navigateToUserCreation = (userType) => {
  // Navigate to the user creation form with the selected type as a query parameter
  router.visit('/users/create', {
    data: {
      user_type: userType
    },
    method: 'get',
    preserveState: false
  });

  // Close the dialog
  userTypeDialog.value = false;
};

const getRoleColor = (roleSlug) => {
  // Handle null or undefined roleSlug
  if (!roleSlug) return 'grey';

  switch (roleSlug) {
    case 'super_admin':
      return 'deep-purple';
    case 'admin':
      return 'indigo';
    case 'staff':
      return 'teal';
    default:
      return 'grey';
  }
};

// Handle table options changes (pagination, sorting, etc.)
const handleTableOptions = (options) => {
  if (options.itemsPerPage !== itemsPerPage.value) {
    itemsPerPage.value = options.itemsPerPage;
  }

  // Handle page changes
  if (options.page !== currentPage.value) {
    changePage(options.page);
  }
};

const changePage = (page) => {
  loading.value = true;
  router.visit(`/users?page=${page}`, {
    preserveState: true,
    onSuccess: () => {
      loading.value = false;
      currentPage.value = page;
    }
  });
};

const confirmDelete = (item) => {
  userToDelete.value = item;
  deleteDialog.value = true;
};

const deleteUser = () => {
  if (userToDelete.value) {
    deleting.value = true;
    router.delete(`/users/${userToDelete.value.id}`, {
      onSuccess: () => {
        deleteDialog.value = false;
        userToDelete.value = null;
        deleting.value = false;
      },
      onError: () => {
        deleting.value = false;
      }
    });
  }
};
</script>

<style scoped>
/* Table header styling */
:deep(.v-data-table-header th) {
  font-weight: bold;
  color: rgba(0, 0, 0, 0.87) !important;
}

:deep(.v-data-table .v-data-table__td) {
  color: rgba(0, 0, 0, 0.87) !important;
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

/* Remove blue focus outline from input fields */
:deep(.v-field__input:focus-visible) {
  outline: none !important;
}

/* Table row hover styling */
:deep(.v-data-table .v-data-table__tr:hover) {
  background-color: rgba(25, 118, 210, 0.05) !important;
}

/* Pagination styling */
:deep(.v-data-table-footer) {
  border-top: thin solid rgba(0, 0, 0, 0.12);
  padding: 0 16px;
}

:deep(.v-data-table-footer__items-per-page) {
  margin-right: 16px;
}

:deep(.v-data-table-footer__info) {
  margin-right: 16px;
}

/* Button styling */
:deep(.v-btn--icon.v-btn--density-default) {
  width: 36px;
  height: 36px;
}

/* Tooltip styling */
:deep(.v-tooltip > .v-overlay__content) {
  background-color: rgba(33, 33, 33, 0.9);
  color: white;
  border-radius: 4px;
  font-size: 0.75rem;
  padding: 4px 8px;
}

/* Hide the extra pagination arrows at the bottom of the page */
:deep(.flex.items-center.gap-4) {
  display: none !important;
}
</style>
