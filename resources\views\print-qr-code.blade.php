
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print QR Code</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: white;
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .released-qr-code-container {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 150px;
            height: 150px;
            z-index: 10;
            /* border: 1px solid black; */
        }

        .received-qr-code-container {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 150px;
            height: 150px;
            z-index: 10;
            /* border: 1px solid black; */
        }

        .qr-code {
            width: 100%;
            height: 100%;
        }

        @media print {
            @page {
                margin: 0;
            }

            body {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
            }

            .qr-code-container {
                position: absolute;
                width: 63px;
                height: 63px;
                right: 15px;
                top: 10px;
            }


            .text-container {
                font-size: 9pt;
                position: absolute;
                right: 10px;
                width: 70px;
                height: 70px;
                z-index: 10;
                top: 75px;

            }

            p,
            .text-container {
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>

@php
$type = request()->query('type');
$opUnit = request()->query('opUnit');
$contNo = request()->query('contNo');
$parts = explode('-', $contNo);

$contNoPrint = $parts[0]. '-' .$parts[2];

@endphp

<body>
    @if ($type === 'released')
    <div class="qr-code-container">
        <img src="{{ $qrCodeUrl }}" alt="QR Code" class="qr-code">
    </div>
    <div class="text-container">
        <p>RELEASED</p>
        <p>{{ $opUnit ?? 'N/A' }} RECORDS
            {{ $contNoPrint ?? 'N/A' }} ________
        </p>
        <p>DATE: {{ now()->format('m/d/Y') }}</p>
    </div>
    @endif

    @if ($type === 'received')
    <div class="qr-code-container">
        <img src="{{ $qrCodeUrl }}" alt="QR Code" class="qr-code">
    </div>
    <div class="text-container">
        <p>RECEIVED</p>
        <p>{{ $opUnit ?? 'N/A' }} RECORDS
            {{ $contNoPrint ?? 'N/A' }} ________
        </p>

        <p>DATE: {{ now()->format('m/d/Y') }}</p>
    </div>
    @endif



</body>

</html>