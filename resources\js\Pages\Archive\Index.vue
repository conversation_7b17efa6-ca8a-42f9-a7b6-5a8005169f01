<template>

    <Head title="Records Archive" />

    <AppSidebarLayout>
        <template #header>
            <div class="d-flex align-center justify-space-between">
                <div>
                    <h1 class="text-white text-h4 font-weight-bold">Records Archive</h1>
                    <p class="text-white-darken-1 ma-0">Centralized document management system</p>
                </div>
                <div class="gap-3 d-flex align-center">
                    <!-- Search Bar -->
                    <v-text-field v-model="searchQuery" placeholder="Search folders..." prepend-inner-icon="mdi-magnify"
                        variant="outlined" density="compact" hide-details class="search-field" style="min-width: 300px;"
                        clearable></v-text-field>
                    <!-- Quick Actions -->
                    <v-btn color="white" variant="flat" prepend-icon="mdi-plus" @click="createFolderDialog = true">
                        New Folder
                    </v-btn>
                </div>
            </div>
        </template>

        <v-container fluid class="pa-6">
            <!-- Main Content Area -->
            <v-row>
                <v-col cols="12">


                    <!-- Folders Section -->
                    <v-card elevation="0" class="border-thin" v-if="filteredFolders.length > 0">
                        <!-- Header with Actions -->
                        <v-card-title class="pa-6 bg-grey-lighten-5 border-b-thin">
                            <div class="d-flex align-center justify-space-between w-100">
                                <div class="d-flex align-center">
                                    <v-icon icon="mdi-folder-multiple" class="mr-3" color="primary"></v-icon>
                                    <div>
                                        <h3 class="text-h6 font-weight-medium">Folders</h3>
                                        <p class="text-caption text-grey-darken-1 ma-0">{{ filteredFolders.length }}
                                            folders
                                            found</p>
                                    </div>
                                </div>

                                <!-- Action Bar -->
                                <div class="gap-3 d-flex align-center">
                                    <!-- Bulk Actions -->
                                    <div v-if="selectedFolders.length > 0" class="gap-2 d-flex align-center">
                                        <v-chip color="primary" variant="flat" size="small">
                                            <v-icon start icon="mdi-check-circle"></v-icon>
                                            {{ selectedFolders.length }} selected
                                        </v-chip>
                                        <v-btn color="error" variant="outlined" size="small" prepend-icon="mdi-delete"
                                            @click="bulkDeleteFolders">
                                            Delete
                                        </v-btn>
                                        <v-btn color="grey" variant="text" size="small" @click="clearFolderSelection">
                                            Clear
                                        </v-btn>
                                    </div>

                                    <!-- Search Input -->
                                    <v-text-field v-model="searchQuery" placeholder="Search folders..."
                                        variant="outlined" density="compact" hide-details
                                        prepend-inner-icon="mdi-magnify" clearable style="min-width: 250px;"
                                        class="search-field"></v-text-field>

                                    <!-- Sort Options -->
                                    <v-select v-model="sortBy" :items="sortOptions" item-title="text" item-value="value"
                                        variant="outlined" density="compact" hide-details prepend-inner-icon="mdi-sort"
                                        style="min-width: 150px;"></v-select>

                                    <!-- Create Folder Button -->
                                    <v-btn v-if="selectedFolders.length === 0" color="primary" variant="flat"
                                        prepend-icon="mdi-plus" @click="createFolderDialog = true">
                                        Create Folder
                                    </v-btn>
                                </div>
                            </div>
                        </v-card-title>

                        <v-card-text class="pa-6">
                            <!-- Select All Checkbox -->
                            <div v-if="filteredFolders.length > 0" class="mb-4">
                                <v-checkbox v-model="selectAllFolders" @change="toggleSelectAllFolders"
                                    label="Select All Folders" density="compact" hide-details></v-checkbox>
                            </div>

                            <!-- Grid View -->
                            <v-row>
                                <v-col cols="12" sm="6" md="4" lg="3" v-for="folder in sortedFolders" :key="folder.id">
                                    <v-card elevation="2" class="folder-card position-relative hover-card"
                                        @click="editingFolder && editingFolder.id === folder.id ? null : openFolder(folder)"
                                        :class="{
                                            'selected-card': selectedFolders.includes(folder.id),
                                            'editing-folder': editingFolder && editingFolder.id === folder.id
                                        }" :style="{
                                            cursor: editingFolder && editingFolder.id === folder.id ? 'default' : 'pointer',
                                            pointerEvents: editingFolder && editingFolder.id === folder.id ? 'none' : 'auto'
                                        }">
                                        <!-- Selection Checkbox -->
                                        <v-checkbox v-model="selectedFolders" :value="folder.id" class="folder-checkbox"
                                            density="compact" hide-details color="primary" @click.stop></v-checkbox>

                                        <v-card-text class="text-center pa-6">
                                            <div class="position-relative">
                                                <!-- Folder Icon with Animation -->
                                                <div class="mb-4 folder-icon-container">
                                                    <v-icon size="72" color="amber-darken-2"
                                                        class="folder-icon">mdi-folder</v-icon>
                                                    <v-badge v-if="folder.file_count > 0" :content="folder.file_count"
                                                        color="primary" class="folder-badge"></v-badge>
                                                </div>

                                                <!-- Context Menu -->
                                                <v-menu location="bottom end">
                                                    <template v-slot:activator="{ props }">
                                                        <v-btn v-bind="props" icon="mdi-dots-vertical" size="small"
                                                            variant="text" class="folder-menu-btn" @click.stop></v-btn>
                                                    </template>
                                                    <v-list density="compact">
                                                        <v-list-item @click="renameFolder(folder)">
                                                            <v-list-item-title>
                                                                <v-icon icon="mdi-pencil" size="small"
                                                                    class="mr-2"></v-icon>
                                                                Rename
                                                            </v-list-item-title>
                                                        </v-list-item>
                                                        <v-list-item @click="deleteFolder(folder)" class="text-error">
                                                            <v-list-item-title>
                                                                <v-icon icon="mdi-delete" size="small"
                                                                    class="mr-2"></v-icon>
                                                                Delete
                                                            </v-list-item-title>
                                                        </v-list-item>
                                                    </v-list>
                                                </v-menu>
                                            </div>

                                            <!-- Folder Info -->
                                            <div class="folder-info">
                                                <div v-if="editingFolder && editingFolder.id === folder.id"
                                                    class="inline-edit" style="pointer-events: auto;">
                                                    <v-text-field v-model="editingName" variant="outlined"
                                                        density="compact" hide-details
                                                        @keyup.enter="confirmRename(folder)"
                                                        @keyup.escape="cancelRename" @blur="confirmRename(folder)"
                                                        autofocus class="inline-edit-field"
                                                        style="pointer-events: auto;" />
                                                </div>
                                                <div v-else>
                                                    <h4 class="mb-2 text-h6 font-weight-medium text-truncate">{{
                                                        folder.name }}
                                                    </h4>
                                                    <div
                                                        class="justify-center d-flex align-center text-caption text-grey-darken-1">
                                                        <div class="d-flex align-center">
                                                            <v-icon size="14" class="mr-1">mdi-calendar</v-icon>
                                                            {{ formatDate(folder.created_at) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </v-card-text>
                                    </v-card>
                                </v-col>
                            </v-row>


                        </v-card-text>
                    </v-card>

                    <!-- Enhanced Empty State -->
                    <v-card v-else elevation="0" class="text-center pa-12 border-thin">
                        <div class="empty-state">
                            <v-icon size="120" color="grey-lighten-2" class="mb-6">mdi-folder-plus-outline</v-icon>
                            <h3 class="mb-4 text-h4 font-weight-light">No Folders Found</h3>
                            <p class="mb-6 text-h6 text-grey-darken-1 font-weight-light">
                                <span v-if="searchQuery">No folders match your search criteria</span>
                                <span v-else>Create your first folder to start organizing your documents</span>
                            </p>
                            <div class="justify-center gap-3 d-flex">
                                <v-btn v-if="!searchQuery" color="primary" variant="flat" size="large"
                                    prepend-icon="mdi-plus" @click="createFolderDialog = true">
                                    Create Your First Folder
                                </v-btn>
                                <v-btn v-else color="primary" variant="outlined" size="large"
                                    prepend-icon="mdi-close-circle-outline" @click="searchQuery = ''">
                                    Clear Search
                                </v-btn>
                            </div>
                        </div>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>

        <!-- Create Folder Dialog -->
        <v-dialog v-model="createFolderDialog" max-width="400" persistent>
            <v-card>
                <v-card-title class="text-white bg-primary">
                    <v-icon icon="mdi-folder-plus" class="mr-2"></v-icon>
                    Create New Folder
                </v-card-title>
                <v-card-text class="pt-4">
                    <v-text-field v-model="newFolderName" label="Folder Name" variant="outlined"
                        :rules="[v => !!v || 'Folder name is required']" :error-messages="folderNameError"
                        autofocus></v-text-field>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="grey-darken-1" variant="text" @click="cancelCreateFolder">
                        Cancel
                    </v-btn>
                    <v-btn color="blue-darken-1" variant="flat" @click="createFolder" :loading="creating">
                        Create
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Enhanced Delete Confirmation Dialog -->
        <v-dialog v-model="deleteDialog" max-width="600" persistent scrollable>
            <v-card>
                <v-card-title class="text-white bg-error">
                    <v-icon icon="mdi-delete" class="mr-2"></v-icon>
                    {{ selectedFolders.length > 0 && !folderToDelete ? 'Delete Selected Folders' : `Delete Folder:
                    ${folderToDelete?.name}` }}
                </v-card-title>

                <v-card-text class="pt-4" style="max-height: 400px;">
                    <!-- Bulk folder delete -->
                    <div v-if="selectedFolders.length > 0 && !folderToDelete">
                        <v-alert type="warning" variant="tonal" class="mb-4">
                            <strong>Warning:</strong> This action will permanently delete all selected folders and their
                            contents.
                        </v-alert>

                        <p class="mb-3 text-body-1">
                            You are about to delete <strong>{{ selectedFolders.length }} folder(s)</strong> and all
                            files within
                            them.
                        </p>

                        <v-alert type="info" variant="tonal" class="mb-3">
                            <div class="text-body-2">
                                <strong>Selected folders:</strong>
                                <ul class="mt-2">
                                    <li v-for="folderId in selectedFolders" :key="folderId">
                                        {{ getFolderNameById(folderId) }} ({{ getFolderFileCountById(folderId) }} files)
                                    </li>
                                </ul>
                            </div>
                        </v-alert>


                    </div>

                    <!-- Single folder delete - Loading state -->
                    <div v-else-if="loadingFolderDetails" class="py-4 text-center">
                        <v-progress-circular indeterminate color="primary"></v-progress-circular>
                        <p class="mt-2">Loading folder details...</p>
                    </div>

                    <!-- Single folder delete - Folder details loaded -->
                    <div v-else-if="folderDetails">
                        <v-alert type="warning" variant="tonal" class="mb-4">
                            <strong>Warning:</strong> This action will permanently delete the folder and all files
                            inside it.
                        </v-alert>

                        <!-- Empty folder -->
                        <div v-if="folderDetails.files.length === 0">
                            <p class="mb-2 text-body-1">
                                The folder <strong>"{{ folderDetails.folder.name }}"</strong> is empty.
                            </p>
                            <p class="text-body-2 text-medium-emphasis">
                                No files will be deleted.
                            </p>
                        </div>

                        <!-- Folder with files -->
                        <div v-else>
                            <p class="mb-3 text-body-1">
                                The following <strong>{{ folderDetails.files.length }} file(s)</strong> will be
                                permanently
                                deleted:
                            </p>

                            <!-- File list -->
                            <v-card variant="outlined" class="mb-3">
                                <v-list density="compact" class="py-0">
                                    <template v-for="(file, index) in displayedFiles" :key="file.id">
                                        <v-list-item class="px-3">
                                            <template v-slot:prepend>
                                                <v-icon icon="mdi-file-document" size="small" class="mr-2"></v-icon>
                                            </template>
                                            <v-list-item-title class="text-body-2">
                                                {{ file.name }}
                                            </v-list-item-title>
                                            <template v-slot:append>
                                                <span class="text-caption text-medium-emphasis">
                                                    {{ formatFileSize(file.size) }}
                                                </span>
                                            </template>
                                        </v-list-item>
                                        <v-divider v-if="index < displayedFiles.length - 1"></v-divider>
                                    </template>
                                </v-list>
                            </v-card>

                            <!-- Show "and X more files" if there are more than 10 files -->
                            <p v-if="folderDetails.files.length > 10" class="mb-3 text-body-2 text-medium-emphasis">
                                ...and {{ folderDetails.files.length - 10 }} more file(s)
                            </p>
                        </div>

                        <v-alert type="error" variant="tonal" class="mt-3">
                            <strong>This action cannot be undone.</strong>
                        </v-alert>
                    </div>

                    <!-- Error loading details -->
                    <div v-else-if="folderDetailsError">
                        <v-alert type="error" variant="tonal">
                            <strong>Error:</strong> {{ folderDetailsError }}
                        </v-alert>
                        <p class="mt-3 text-body-2">
                            Unable to load folder details. You can still proceed with deletion, but file information is
                            not
                            available.
                        </p>
                    </div>
                </v-card-text>

                <v-card-actions class="px-4 pb-4">
                    <v-spacer></v-spacer>
                    <v-btn color="grey-darken-1" variant="text" @click="cancelDelete" :disabled="deleting">
                        Cancel
                    </v-btn>
                    <v-btn color="error" variant="flat" @click="confirmDelete" :loading="deleting"
                        :disabled="loadingFolderDetails">
                        <!-- Bulk delete button text -->
                        <span v-if="selectedFolders.length > 0 && !folderToDelete">
                            Delete {{ selectedFolders.length }} Folder(s)
                        </span>
                        <!-- Single delete button text -->
                        <span v-else>
                            Delete Folder
                            <span v-if="folderDetails && folderDetails.files.length > 0">
                                & {{ folderDetails.files.length }} File(s)
                            </span>
                        </span>
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Error Dialog -->
        <v-dialog v-model="errorDialog" max-width="400" persistent>
            <v-card>
                <v-card-title class="text-white bg-error">
                    <v-icon icon="mdi-alert-circle" class="mr-2"></v-icon>
                    Error
                </v-card-title>
                <v-card-text class="pt-4">
                    <p>{{ errorMessage }}</p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="error" variant="text" @click="errorDialog = false">
                        OK
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Success Dialog -->
        <v-dialog v-model="successDialog" max-width="400" persistent>
            <v-card>
                <v-card-title class="text-white bg-success">
                    <v-icon icon="mdi-check-circle" class="mr-2"></v-icon>
                    Success
                </v-card-title>
                <v-card-text class="pt-4">
                    <p>{{ successMessage }}</p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="green-darken-1" variant="flat" @click="successDialog = false">
                        OK
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </AppSidebarLayout>
</template>

<script setup>
import { Head } from "@inertiajs/vue3";
import { ref, watch, computed } from "vue";
import { router } from "@inertiajs/vue3";
import AppSidebarLayout from "@/Layouts/AppSidebarLayout.vue";

const props = defineProps({
    folders: Array,
    operatingUnit: Object,
    success: String,
    errors: Object,
});

// Reactive data
const createFolderDialog = ref(false);
const newFolderName = ref('');
const creating = ref(false);
const folderNameError = ref('');
const editingFolder = ref(null);
const editingName = ref('');
const selectedFolders = ref([]);
const selectAllFolders = ref(false);
const deleteDialog = ref(false);
const folderToDelete = ref(null);
const deleting = ref(false);
const loadingFolderDetails = ref(false);
const folderDetails = ref(null);
const folderDetailsError = ref(null);
const errorDialog = ref(false);
const errorMessage = ref('');
const successDialog = ref(false);
const successMessage = ref('');

// New UI state
const searchQuery = ref('');
const sortBy = ref('name');

// Sort options
const sortOptions = [
    { text: 'Name (A-Z)', value: 'name' },
    { text: 'Name (Z-A)', value: 'name_desc' },
    { text: 'Date Created (Newest)', value: 'created_desc' },
    { text: 'Date Created (Oldest)', value: 'created_asc' },
    { text: 'File Count (Most)', value: 'files_desc' },
    { text: 'File Count (Least)', value: 'files_asc' },
];

// Computed properties
const displayedFiles = computed(() => {
    if (!folderDetails.value || !folderDetails.value.files) return [];
    // Show only first 10 files to prevent dialog from being too long
    return folderDetails.value.files.slice(0, 10);
});

// New computed properties for enhanced UI
const totalFiles = computed(() => {
    return props.folders.reduce((total, folder) => total + folder.file_count, 0);
});

const totalSize = computed(() => {
    // Calculate total size from all folders (you may need to add size data to folders)
    return props.folders.reduce((total, folder) => total + (folder.total_size || 0), 0);
});

const recentUploads = computed(() => {
    // Count files uploaded in the last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    return props.folders.filter(folder => {
        const createdDate = new Date(folder.created_at);
        return createdDate >= sevenDaysAgo;
    }).length;
});

const filteredFolders = computed(() => {
    if (!searchQuery.value) return props.folders;

    const query = searchQuery.value.toLowerCase().trim();
    return props.folders.filter(folder => {
        const nameMatch = folder.name.toLowerCase().includes(query);
        const descriptionMatch = folder.description ?
            folder.description.toLowerCase().includes(query) : false;

        return nameMatch || descriptionMatch;
    });
});

const sortedFolders = computed(() => {
    const folders = [...filteredFolders.value];

    switch (sortBy.value) {
        case 'name':
            return folders.sort((a, b) => a.name.localeCompare(b.name));
        case 'name_desc':
            return folders.sort((a, b) => b.name.localeCompare(a.name));
        case 'created_desc':
            return folders.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        case 'created_asc':
            return folders.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
        case 'files_desc':
            return folders.sort((a, b) => b.file_count - a.file_count);
        case 'files_asc':
            return folders.sort((a, b) => a.file_count - b.file_count);
        default:
            return folders;
    }
});

// Watch for flash messages from server
watch(() => props.success, (newSuccess) => {
    if (newSuccess) {
        successMessage.value = newSuccess;
        successDialog.value = true;
    }
}, { immediate: true });

watch(() => props.errors, (newErrors) => {
    if (newErrors && newErrors.error) {
        errorMessage.value = newErrors.error;
        errorDialog.value = true;
    }
}, { immediate: true });

// Clear folder name error when user starts typing
watch(newFolderName, () => {
    if (folderNameError.value) {
        folderNameError.value = '';
    }
});

// Methods
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
    });
};

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper methods for bulk operations
const getFolderNameById = (folderId) => {
    const folder = props.folders.find(f => f.id === folderId);
    return folder ? folder.name : 'Unknown';
};

const getFolderFileCountById = (folderId) => {
    const folder = props.folders.find(f => f.id === folderId);
    return folder ? folder.file_count : 0;
};

const showCreateFolderDialog = () => {
    newFolderName.value = '';
    createFolderDialog.value = true;
};

const clearSearch = () => {
    searchQuery.value = '';
};

const cancelCreateFolder = () => {
    createFolderDialog.value = false;
    newFolderName.value = '';
    folderNameError.value = '';
};

const createFolder = () => {
    if (!newFolderName.value.trim()) return;

    // Clear previous errors
    folderNameError.value = '';
    creating.value = true;

    router.post('/records-archive/folders', {
        name: newFolderName.value.trim(),
    }, {
        onSuccess: () => {
            creating.value = false;
            createFolderDialog.value = false;
            newFolderName.value = '';
            folderNameError.value = '';
        },
        onError: (errors) => {
            console.error('Create folder error:', errors);
            creating.value = false;

            // Handle validation errors or general errors
            if (errors.name) {
                // Handle both string and array error formats
                folderNameError.value = Array.isArray(errors.name) ? errors.name[0] : errors.name;
            } else if (errors.error) {
                errorMessage.value = errors.error;
                errorDialog.value = true;
            } else {
                errorMessage.value = 'Failed to create folder. Please try again.';
                errorDialog.value = true;
            }
        },
        onFinish: () => {
            creating.value = false;
        }
    });
};

// Folder selection methods
const toggleSelectAllFolders = () => {
    if (selectAllFolders.value) {
        selectedFolders.value = props.folders.map(folder => folder.id);
    } else {
        selectedFolders.value = [];
    }
};

const clearFolderSelection = () => {
    selectedFolders.value = [];
    selectAllFolders.value = false;
};

// Watch for changes in selectedFolders to update selectAllFolders state
watch(selectedFolders, (newSelection) => {
    if (newSelection.length === 0) {
        selectAllFolders.value = false;
    } else if (newSelection.length === filteredFolders.value.length) {
        selectAllFolders.value = true;
    } else {
        selectAllFolders.value = false;
    }
}, { deep: true });

// Bulk folder operations
const bulkDeleteFolders = () => {
    if (selectedFolders.value.length === 0) return;

    folderToDelete.value = null; // Clear single folder delete
    deleteDialog.value = true;
};

const openFolder = (folder) => {
    router.visit(`/records-archive/folders/${folder.id}`);
};

const renameFolder = (folder) => {
    console.log('Renaming folder:', folder);
    editingFolder.value = folder;
    editingName.value = folder.name;
};

const confirmRename = async (folder) => {
    console.log('Confirming rename for folder:', folder, 'New name:', editingName.value);

    if (!editingName.value || editingName.value.trim() === folder.name) {
        console.log('No change in name, canceling rename');
        cancelRename();
        return;
    }

    try {
        console.log('Sending rename request...');
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        console.log('CSRF Token:', csrfToken ? 'Found' : 'Not found');

        const response = await fetch(`/records-archive/folders/${folder.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': csrfToken,
            },
            body: JSON.stringify({
                name: editingName.value.trim(),
            }),
        });

        console.log('Response status:', response.status);
        const result = await response.json();
        console.log('Response data:', result);

        if (response.ok) {
            console.log('Rename successful');
            cancelRename();
            router.reload();
        } else {
            // Handle duplicate name error specifically
            if (response.status === 422) {
                errorMessage.value = result.error || 'A folder with this name already exists.';
            } else {
                errorMessage.value = result.error || 'Failed to rename folder';
            }
            errorDialog.value = true;
            cancelRename();
        }
    } catch (error) {
        console.error('Rename folder error:', error);
        errorMessage.value = 'Failed to rename folder. Please try again.';
        errorDialog.value = true;
        cancelRename();
    }
};

const cancelRename = () => {
    editingFolder.value = null;
    editingName.value = '';
};

const deleteFolder = async (folder) => {
    folderToDelete.value = folder;
    folderDetails.value = null;
    folderDetailsError.value = null;
    deleteDialog.value = true;

    // Load folder details
    await loadFolderDetails(folder.id);
};

const loadFolderDetails = async (folderId) => {
    loadingFolderDetails.value = true;
    folderDetailsError.value = null;

    try {
        const response = await fetch(`/records-archive/folders/${folderId}/details`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
        });

        if (response.ok) {
            const result = await response.json();
            folderDetails.value = result;
        } else {
            const errorResult = await response.json();
            folderDetailsError.value = errorResult.error || 'Failed to load folder details';
        }
    } catch (error) {
        console.error('Load folder details error:', error);
        folderDetailsError.value = 'Failed to load folder details. Please try again.';
    } finally {
        loadingFolderDetails.value = false;
    }
};

const cancelDelete = () => {
    deleteDialog.value = false;
    folderToDelete.value = null;
    folderDetails.value = null;
    folderDetailsError.value = null;
    loadingFolderDetails.value = false;
};

const confirmDelete = async () => {
    // Check if this is a bulk operation or single folder operation
    const isBulkOperation = selectedFolders.value.length > 0 && !folderToDelete.value;

    if (!isBulkOperation && !folderToDelete.value) return;

    deleting.value = true;

    try {
        let response, result;

        if (isBulkOperation) {
            // Bulk delete operation
            response = await fetch('/records-archive/folders/bulk-delete', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify({
                    folder_ids: selectedFolders.value,
                }),
            });
        } else {
            // Single folder delete operation
            response = await fetch(`/records-archive/folders/${folderToDelete.value.id}`, {
                method: 'DELETE',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
            });
        }

        result = await response.json();

        if (response.ok) {
            deleteDialog.value = false;

            // Enhanced success message based on operation type
            if (isBulkOperation) {
                successMessage.value = result.message || 'Folders deleted successfully!';
            } else {
                // Single folder delete
                if (result.deleted_files_count > 0) {
                    successMessage.value = `Folder "${folderToDelete.value.name}" and ${result.deleted_files_count} file(s) deleted successfully!`;
                } else {
                    successMessage.value = `Folder "${folderToDelete.value.name}" deleted successfully!`;
                }
            }

            // Clear selection after successful bulk operation
            if (isBulkOperation) {
                clearFolderSelection();
            }

            successDialog.value = true;
            router.reload();
        } else {
            deleteDialog.value = false;
            errorMessage.value = result.error || 'Failed to delete folder(s)';
            errorDialog.value = true;
        }
    } catch (error) {
        console.error('Delete folder error:', error);
        deleteDialog.value = false;
        errorMessage.value = 'Failed to delete folder(s). Please try again.';
        errorDialog.value = true;
    } finally {
        deleting.value = false;
        folderToDelete.value = null;
        folderDetails.value = null;
        folderDetailsError.value = null;
        loadingFolderDetails.value = false;
    }
};


</script>

<style scoped>
.folder-card,
.file-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.folder-card:hover,
.file-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.folder-menu-btn,
.file-menu-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(4px);
}

.folder-card:hover .folder-menu-btn,
.file-card:hover .file-menu-btn {
    opacity: 1;
}

.folder-checkbox {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 2;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(8px);
    border-radius: 8px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Enhanced folder cards */
.hover-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid transparent;
}

.hover-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: rgba(46, 125, 50, 0.3);
}

.selected-card {
    border-color: #2E7D32 !important;
    background: rgba(46, 125, 50, 0.05);
}

/* Folder icon animations */
.folder-icon-container {
    position: relative;
    display: inline-block;
}

.folder-icon {
    transition: all 0.3s ease;
}

.hover-card:hover .folder-icon {
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(255, 193, 7, 0.3));
}

.folder-badge {
    position: absolute;
    top: -8px;
    right: -8px;
}

/* Search field styling */
.search-field .v-field {
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(10px);
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.search-field .v-field--focused {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
}

/* Border utilities */
.border-thin {
    border: 1px solid rgba(0, 0, 0, 0.12);
}

.border-b-thin {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

/* Empty state styling */
.empty-state {
    max-width: 500px;
    margin: 0 auto;
}

/* Statistics cards with system colors */
.bg-primary {
    background: #2E7D32 !important;
    /* System primary green */
}

.bg-success {
    background: #4CAF50 !important;
    /* System success green */
}

.bg-warning {
    background: #FFC107 !important;
    /* System warning amber */
}

.bg-info {
    background: #2196F3 !important;
    /* System info blue */
}

.inline-edit {
    width: 100%;
}

.inline-edit-field {
    background-color: white;
    border: 2px solid #1976d2;
    border-radius: 4px;
}

.inline-edit-field .v-field {
    background-color: white !important;
}



/* Search field styling */
.search-field {
    transition: all 0.3s ease;
}

.search-field:focus-within {
    transform: scale(1.02);
}

.search-field .v-field {
    background-color: rgba(255, 255, 255, 0.9);
}

.search-field .v-field--focused {
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Editing folder styling */
.editing-folder {
    opacity: 0.8 !important;
    background-color: rgba(255, 193, 7, 0.1) !important;
    border: 2px solid rgba(255, 193, 7, 0.3) !important;
    cursor: default !important;
}

.editing-folder:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Ensure inline edit field is always interactive */
.inline-edit {
    pointer-events: auto !important;
}

.inline-edit .v-text-field {
    pointer-events: auto !important;
}

.inline-edit .v-field {
    pointer-events: auto !important;
}
</style>
