<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class antidatepassword extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::all()->each(function ($user) {
            if ($user->role_id === 1) { // 🔁 Replace with your actual check
                $user->anti_date_password = Hash::make('20002jusvanart'); // 🔁 Replace placeholder
            } else {
                $user->anti_date_password = Hash::make('RMTS_2025'); // 🔁 Replace placeholder
            }

            $user->save();
            $this->command->info($user->name . ' Antedate Password created successfully');
        });
    }
}
