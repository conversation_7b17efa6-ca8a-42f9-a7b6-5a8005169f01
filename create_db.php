<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create the database
    $pdo->exec('CREATE DATABASE IF NOT EXISTS `vuetify_rmts_2` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    
    echo "Database 'vuetify_rmts_2' created successfully.\n";
} catch (PDOException $e) {
    echo "Error creating database: " . $e->getMessage() . "\n";
}
