<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DocumentCode extends Model
{
    use HasFactory;

    protected $table = 'document_codes';

    // Fields that can be mass-assigned
    protected $fillable = [
        'operating_unit_id',
        'operating_unit_code_key',
        'operating_unit_code_value',
    ];

    // Cast preference_value to array automatically (if you store JSON)
    protected $casts = [
        'operating_unit_code_value' => 'array',
    ];

    /**
     * Get the documents that use this document code.
     */
  /*   public function documents()
    {
        return $this->hasMany(Document::class, 'document_code', 'docu_code');
    } */
}
