<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\DocumentTrackingHistory;

class Document extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'signatory',
        'origin',
        'origin_unit_id',
        'current_unit_id',
        'department_unit_id',
        'subject_matter',
        'referred_to',
        'document_code',
        'reference_document',
        'control_number',
        'received_by_id',
        'date_received',
        'status',
        'remarks',
        'file_path',
        'qr_code_path',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date_received' => 'datetime',
    ];

    /**
     * Get the user who received the document.
     */
    public function receivedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by_id');
    }

    /**
     * Get the operating unit that is the origin of the document.
     */
    public function originUnit(): BelongsTo
    {
        return $this->belongsTo(OperatingUnit::class, 'origin_unit_id');
    }

    /**
     * Get the operating unit that currently owns the document.
     */
    public function currentUnit(): BelongsTo
    {
        return $this->belongsTo(OperatingUnit::class, 'current_unit_id');
    }

    /**
     * Get the tracking history for the document.
     */
    public function trackingHistory(): HasMany
    {
        return $this->hasMany(DocumentTrackingHistory::class);
    }

    /**
     * Get the department unit associated with this document.
     */
    public function departmentUnit(): BelongsTo
    {
        return $this->belongsTo(DepartmentUnit::class, 'department_unit_id');
    }

    /**
     * Generate a unique control number based on reference document and sequence.
     *
     * @param string $referenceDocument
     * @return string
     */
    public static function generateControlNumber(string $referenceDocument, int $userId): string
    {
        $today = now();
        $dateFormat = $today->format('dmY');

        $prefix = "{$referenceDocument}-{$dateFormat}-";

        $latestSequence = 0;

        // Filter by reference document and user ID
        $documents = self::where('reference_document', $referenceDocument)
            ->where('origin_unit_id', $userId)
            ->get();

        foreach ($documents as $doc) {
            if (strpos($doc->control_number, $prefix) === 0) {
                $parts = explode('-', $doc->control_number);
                if (count($parts) === 3) {
                    $seq = (int) $parts[2];
                    if ($seq > $latestSequence) {
                        $latestSequence = $seq;
                    }
                }
            }
        }

        $sequence = $latestSequence + 1;

        return $prefix . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }
}
