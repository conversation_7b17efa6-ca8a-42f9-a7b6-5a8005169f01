<?php

namespace App\Http\Controllers;

use App\Models\DepartmentUnit;
use App\Models\OperatingUnit;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get users based on the authenticated user's role
        $query = User::with(['role', 'operatingUnit'])
            ->whereHas('role', function ($q) {
                // Exclude Super Admin users from the listing
                $q->where('slug', '!=', 'super_admin');
            });

        if ($user->isAdmin()) {
            // Admins can only see users from their own operating unit
            $query->where('operating_unit_id', $user->operating_unit_id);
        }

        $users = $query->paginate(10);

        return Inertia::render('Users/Index', [
            'users' => $users,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get roles based on the authenticated user's role
        $roles = [];
        $operatingUnits = [];
        $departmentUnits = [];

        // Get the user type from the request
        $userType = $request->input('user_type', 'operating_unit');

        if ($user->isSuperAdmin()) {
            // Super admin can assign any role except super admin
            $roles = Role::whereNotIn('slug', ['super_admin'])->get();
            // Super admin can assign any operating unit including Central Administration
            $operatingUnits = OperatingUnit::all();

            // Get department units if user type is department_unit
            if ($userType === 'department_unit') {
                $departmentUnits = DepartmentUnit::all();
            }
        } elseif ($user->isAdmin()) {
            // Check if the admin belongs to Central Administration
            if ($user->isCentralAdministration()) {
                // Central Administration admins can assign both admin and staff roles
                $roles = Role::whereIn('slug', ['admin', 'staff'])->get();
                // Central Administration admins can assign any operating unit
                $operatingUnits = OperatingUnit::all();
            } else {
                // Regular admins can only assign staff role
                $roles = Role::where('slug', 'staff')->get();
                // Regular admins can only assign their own operating unit
                $operatingUnits = OperatingUnit::where('id', $user->operating_unit_id)->get();
            }

            // Get department units for the admin's operating unit if user type is department_unit
            if ($userType === 'department_unit') {
                if ($user->isCentralAdministration()) {
                    // Central Administration admins can see all department units
                    $departmentUnits = DepartmentUnit::all();
                } else {
                    // Regular admins can only see department units in their operating unit
                    $departmentUnits = DepartmentUnit::where('operating_unit_id', $user->operating_unit_id)->get();
                }
            }
        }

        return Inertia::render('Users/Create', [
            'roles' => $roles,
            'operatingUnits' => $operatingUnits,
            'departmentUnits' => $departmentUnits,
            'userRole' => $user->role->slug,
            'userType' => $userType,
            'auth' => [
                'user' => $user->load('operatingUnit'),
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Validate the request
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role_id' => 'required|exists:roles,id',
            'user_type' => 'required|in:operating_unit,department_unit',
        ]);

        // Additional validation based on user role and user type
        if ($user->isSuperAdmin()) {
            // Validate operating unit for both user types
            $request->validate([
                'operating_unit_id' => 'required|exists:operating_units,id',
            ]);

            // Validate department unit if user type is department_unit
            if ($request->user_type === 'department_unit') {
                $request->validate([
                    'department_unit_id' => 'required|exists:department_units,id',
                ]);

                // Verify that the department unit belongs to the selected operating unit
                $departmentUnit = DepartmentUnit::findOrFail($request->department_unit_id);
                if ($departmentUnit->operating_unit_id != $request->operating_unit_id) {
                    return back()->withErrors(['department_unit_id' => 'The selected department unit does not belong to the selected operating unit.']);
                }
            }
        } elseif ($user->isAdmin()) {
            // Admin can only create users in their own operating unit
            $operatingUnitId = $user->operating_unit_id;

            // Validate department unit if user type is department_unit
            if ($request->user_type === 'department_unit') {
                $request->validate([
                    'department_unit_id' => 'required|exists:department_units,id',
                ]);

                // Verify that the department unit belongs to the admin's operating unit
                // Central Administration admins can assign any department unit
                if (!$user->isCentralAdministration()) {
                    $departmentUnit = DepartmentUnit::findOrFail($request->department_unit_id);
                    if ($departmentUnit->operating_unit_id != $operatingUnitId) {
                        return back()->withErrors(['department_unit_id' => 'The selected department unit does not belong to your operating unit.']);
                    }
                }
            }

            // Validate role based on admin's operating unit
            $role = Role::findOrFail($request->role_id);

            // Check if the target operating unit is Central Administration
            $targetOperatingUnit = null;
            if ($request->filled('operating_unit_id')) {
                $targetOperatingUnit = OperatingUnit::findOrFail($request->operating_unit_id);
            } else {
                $targetOperatingUnit = $user->operatingUnit;
            }

            $isTargetCentralAdmin = $targetOperatingUnit && $targetOperatingUnit->isCentralAdministration();

            // Only Super Admin users can create Admin users for Central Administration
            if ($role->isAdmin() && $isTargetCentralAdmin && !$user->isSuperAdmin()) {
                return back()->withErrors(['role_id' => 'Only Super Admin users can create Admin users for Central Administration.']);
            }

            // Only Central Administration admins can create admin users for other operating units
            if ($role->isAdmin() && !$isTargetCentralAdmin && !$user->isCentralAdministration()) {
                return back()->withErrors(['role_id' => 'Only Central Administration admins can create admin users for other operating units.']);
            }

            // Regular admins can only create staff users
            if (!$user->isCentralAdministration() && !$role->isStaff()) {
                return back()->withErrors(['role_id' => 'You can only create staff users.']);
            }

            // If Central Administration admin is creating a user for another operating unit
            if ($user->isCentralAdministration() && $request->operating_unit_id != $user->operating_unit_id) {
                // Validate that the operating unit exists
                $request->validate([
                    'operating_unit_id' => 'required|exists:operating_units,id',
                ]);

                // Update the operating unit ID to the selected one
                $operatingUnitId = $request->operating_unit_id;
            }
        } else {
            return back()->withErrors(['unauthorized' => 'You are not authorized to create users.']);
        }

        // Create the user with appropriate fields based on user type
        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'anti_date_password' => Hash::make($request->anti_date_password),
            'role_id' => $request->role_id,
            'operating_unit_id' => $user->isAdmin() ? $user->operating_unit_id : $request->operating_unit_id,
        ];

        // Add department_unit_id if user type is department_unit
        if ($request->user_type === 'department_unit') {
            $userData['department_unit_id'] = $request->department_unit_id;
        }

        $newUser = User::create($userData);

        return redirect()->route('users.index')->with('success', 'User created successfully.');
    }

    public function show(string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the user to show
        $userToShow = User::with(['role', 'operatingUnit'])->findOrFail($id);

        // Check if the user to show is a Super Admin
        if ($userToShow->isSuperAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'You are not authorized to view Super Admin users.');
        }

        // Check if the authenticated user is authorized to view this user
        if ($user->isAdmin() && $userToShow->operating_unit_id !== $user->operating_unit_id) {
            abort(403, 'You are not authorized to view this user.');
        }

        return Inertia::render('Users/Show', [
            'user' => $userToShow,
            'errors' => session()->get('errors') ? session()->get('errors')->getBag('default')->getMessages() : (object) [],
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the user to edit
        $userToEdit = User::findOrFail($id);

        // Check if the user to edit is a Super Admin
        if ($userToEdit->isSuperAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'You are not authorized to edit Super Admin users.');
        }

        // Check if the authenticated user is authorized to edit this user
        if ($user->isAdmin() && $userToEdit->operating_unit_id !== $user->operating_unit_id) {
            abort(403, 'You are not authorized to edit this user.');
        }

        // Get roles based on the authenticated user's role
        $roles = [];
        $operatingUnits = [];

        if ($user->isSuperAdmin()) {
            // Super admin can assign any role except super admin
            $roles = Role::whereNotIn('slug', ['super_admin'])->get();

            // Include all operating units including Central Administration
            $operatingUnits = OperatingUnit::all();
        } elseif ($user->isAdmin()) {
            // Check if the admin belongs to Central Administration
            if ($user->isCentralAdministration()) {
                // Central Administration admins can assign both admin and staff roles
                $roles = Role::whereIn('slug', ['admin', 'staff'])->get();
                // Central Administration admins can assign any operating unit
                $operatingUnits = OperatingUnit::all();
            } else {
                // Regular admins can only assign staff role
                $roles = Role::where('slug', 'staff')->get();
                // Regular admins can only assign their own operating unit
                $operatingUnits = OperatingUnit::where('id', $user->operating_unit_id)->get();
            }
        }

        return Inertia::render('Users/Edit', [
            'user' => $userToEdit->load(['role', 'operatingUnit']),
            'roles' => $roles,
            'operatingUnits' => $operatingUnits,
            'userRole' => $user->role->slug,
            'auth' => [
                'user' => $user->load('operatingUnit'),
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the user to update
        $userToUpdate = User::findOrFail($id);

        // Check if the user to update is a Super Admin
        if ($userToUpdate->isSuperAdmin() && !$user->isSuperAdmin()) {
            abort(403, 'You are not authorized to update Super Admin users.');
        }

        // Check if the authenticated user is authorized to update this user
        if ($user->isAdmin() && $userToUpdate->operating_unit_id !== $user->operating_unit_id) {
            abort(403, 'You are not authorized to update this user.');
        }

        // Validate the request
        $validationRules = [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
        ];

        // Only validate password if it's provided
        if ($request->filled('password')) {
            $validationRules['password'] = 'string|min:8|confirmed';
        }

        // Additional validation based on user role
        if ($user->isSuperAdmin()) {
            $validationRules['role_id'] = 'required|exists:roles,id';
            $validationRules['operating_unit_id'] = 'required|exists:operating_units,id';
        } elseif ($user->isAdmin()) {
            // Admin validation rules
            $validationRules['role_id'] = 'required|exists:roles,id';

            // Check if the admin belongs to Central Administration
            if ($user->isCentralAdministration()) {
                // Central Administration admins can update operating unit
                $validationRules['operating_unit_id'] = 'required|exists:operating_units,id';

                // Validate role (can be admin or staff)
                $role = Role::findOrFail($request->role_id);

                // Check if the target operating unit is Central Administration
                $targetOperatingUnit = null;
                if ($request->filled('operating_unit_id')) {
                    $targetOperatingUnit = OperatingUnit::findOrFail($request->operating_unit_id);
                } else {
                    $targetOperatingUnit = $user->operatingUnit;
                }

                $isTargetCentralAdmin = $targetOperatingUnit && $targetOperatingUnit->isCentralAdministration();

                // Only Super Admin users can assign Admin role for Central Administration
                if ($role->isAdmin() && $isTargetCentralAdmin && !$user->isSuperAdmin()) {
                    return back()->withErrors(['role_id' => 'Only Super Admin users can assign Admin role for Central Administration.']);
                }

                // Validate that the role is admin or staff
                if (!$role->isAdmin() && !$role->isStaff()) {
                    return back()->withErrors(['role_id' => 'You can only assign admin or staff roles.']);
                }
            } else {
                // Regular admins can only update users to staff role
                $role = Role::findOrFail($request->role_id);
                if (!$role->isStaff()) {
                    return back()->withErrors(['role_id' => 'You can only assign staff role.']);
                }
            }
        }

        $request->validate($validationRules);

        // Update the user
        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
        ];

        // Only update password if it's provided
        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        // Update role and operating unit if the authenticated user is a super admin
        if ($user->isSuperAdmin()) {
            $updateData['role_id'] = $request->role_id;
            $updateData['operating_unit_id'] = $request->operating_unit_id;
        } elseif ($user->isAdmin()) {
            $updateData['role_id'] = $request->role_id;
            // Admin can only update users in their own operating unit
            $updateData['operating_unit_id'] = $user->operating_unit_id;
        }

        $userToUpdate->update($updateData);

        return redirect()->route('users.index')->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the user to delete
        $userToDelete = User::findOrFail($id);

        // Check if the authenticated user is authorized to delete this user
        if ($user->isAdmin() && $userToDelete->operating_unit_id !== $user->operating_unit_id) {
            abort(403, 'You are not authorized to delete this user.');
        }

        // Prevent deleting super admin users
        if ($userToDelete->isSuperAdmin()) {
            return back()->withErrors(['unauthorized' => 'Super Admin users cannot be deleted.']);
        }

        // Prevent deleting yourself
        if ($userToDelete->id === $user->id) {
            return back()->withErrors(['unauthorized' => 'You cannot delete your own account.']);
        }

        // Delete the user
        $userToDelete->delete();

        return redirect()->route('users.index')->with('success', 'User deleted successfully.');
    }
}
