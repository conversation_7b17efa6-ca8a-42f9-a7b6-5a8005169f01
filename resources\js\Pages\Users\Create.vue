<template>
  <Head :title="`Create ${userTypeLabel} User`" />

  <AppSidebarLayout>
    <template #header>
      <h2 class="text-xl font-semibold leading-tight text-gray-800">
        Create New {{ userTypeLabel }} User
      </h2>
    </template>

    <v-container>
      <v-row>
        <v-col cols="12">
          <v-card elevation="2">
            <v-card-title
              class="text-white"
              :class="
                userType === 'department_unit' ? 'bg-indigo' : 'bg-primary'
              "
            >
              <div class="d-flex align-center">
                <v-icon color="white" class="mr-2">{{
                  userType === "department_unit"
                    ? "mdi-domain"
                    : "mdi-office-building"
                }}</v-icon>
                Create New {{ userTypeLabel }} User
              </div>
            </v-card-title>

            <v-card-text>
              <form @submit.prevent="submit">
                <v-row class="mt-4">
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="form.name"
                      label="Name"
                      required
                      :error-messages="errors.name"
                      variant="outlined"
                      bg-color="white"
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="form.email"
                      label="Email"
                      type="email"
                      required
                      :error-messages="errors.email"
                      variant="outlined"
                      bg-color="white"
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="form.password"
                      label="Password"
                      :type="showPassword ? 'text' : 'password'"
                      required
                      :error-messages="errors.password"
                      variant="outlined"
                      bg-color="white"
                      :append-inner-icon="
                        showPassword ? 'mdi-eye-off' : 'mdi-eye'
                      "
                      @click:append-inner="showPassword = !showPassword"
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="form.password_confirmation"
                      label="Confirm Password"
                      :type="showConfirmPassword ? 'text' : 'password'"
                      required
                      variant="outlined"
                      bg-color="white"
                      :append-inner-icon="
                        showConfirmPassword ? 'mdi-eye-off' : 'mdi-eye'
                      "
                      @click:append-inner="
                        showConfirmPassword = !showConfirmPassword
                      "
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12" md="6">
                    <v-select
                      v-model="form.role_id"
                      :items="filteredRoles"
                      item-title="name"
                      item-value="id"
                      label="Role"
                      required
                      :error-messages="errors.role_id"
                      variant="outlined"
                      bg-color="white"
                    >
                      <template v-slot:append>
                        <v-tooltip
                          v-if="userRole === 'admin' && !isCentralAdmin"
                          text="Only Central Administration admins can create admin users for other operating units"
                          location="bottom"
                        >
                          <template v-slot:activator="{ props }">
                            <v-icon v-bind="props" color="info" class="ml-2"
                              >mdi-information</v-icon
                            >
                          </template>
                        </v-tooltip>
                        <v-tooltip
                          v-if="
                            userRole === 'admin' &&
                            isCentralAdmin &&
                            isCentralAdminSelected
                          "
                          text="Only Super Admin users can create Admin users for Central Administration"
                          location="bottom"
                        >
                          <template v-slot:activator="{ props }">
                            <v-icon v-bind="props" color="warning" class="ml-2"
                              >mdi-alert-circle</v-icon
                            >
                          </template>
                        </v-tooltip>
                      </template>
                    </v-select>
                  </v-col>

                  <!-- Operating Unit Selection (for super_admin and when userType is operating_unit) -->
                  <v-col
                    cols="12"
                    md="6"
                    v-if="
                      (userRole === 'super_admin' || userRole === 'admin') &&
                      userType === 'operating_unit'
                    "
                  >
                    <v-select
                      v-model="form.operating_unit_id"
                      :items="operatingUnits"
                      item-title="name"
                      item-value="id"
                      label="Operating Unit"
                      required
                      :error-messages="errors.operating_unit_id"
                      variant="outlined"
                      bg-color="white"
                      @update:model-value="onOperatingUnitChange"
                    ></v-select>
                  </v-col>

                  <!-- Department Unit Selection (when userType is department_unit) -->
                  <template v-if="userType === 'department_unit'">
                    <!-- Operating Unit Selection for Department Unit Users (for super_admin only) -->
                    <v-col cols="12" md="6" v-if="userRole === 'super_admin'">
                      <v-select
                        v-model="form.operating_unit_id"
                        :items="operatingUnits"
                        item-title="name"
                        item-value="id"
                        label="Operating Unit"
                        required
                        :error-messages="errors.operating_unit_id"
                        variant="outlined"
                        bg-color="white"
                        @update:model-value="updateDepartmentUnits"
                      ></v-select>
                    </v-col>

                    <!-- Department Unit Selection -->
                    <v-col cols="12" md="6">
                      <v-select
                        v-model="form.department_unit_id"
                        :items="filteredDepartmentUnits"
                        item-title="name"
                        item-value="id"
                        label="Department Unit"
                        required
                        :error-messages="errors.department_unit_id"
                        variant="outlined"
                        bg-color="white"
                        :disabled="
                          !form.operating_unit_id && userRole === 'super_admin'
                        "
                      ></v-select>
                    </v-col>
                  </template>

                  <v-col cols="12" class="justify-end d-flex">
                    <v-btn
                      color="grey-darken-1"
                      variant="outlined"
                      class="mr-4"
                      prepend-icon="mdi-arrow-left"
                      @click="goBack"
                      size="large"
                    >
                      Back
                    </v-btn>
                    <v-btn
                      type="submit"
                      :color="
                        userType === 'department_unit' ? 'indigo' : 'primary'
                      "
                      size="large"
                      :loading="form.processing"
                      :prepend-icon="
                        userType === 'department_unit'
                          ? 'mdi-domain'
                          : 'mdi-office-building'
                      "
                    >
                      Create {{ userTypeLabel }} User
                    </v-btn>
                  </v-col>
                </v-row>
              </form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, useForm } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
    roles: Array,
    operatingUnits: Array,
    departmentUnits: Array,
    userRole: String,
    userType: String,
    errors: Object,
    auth: Object,
});

// Password visibility toggles
const showPassword = ref(false);
const showConfirmPassword = ref(false);

// Initialize form with appropriate fields based on user type
const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role_id: '',
    operating_unit_id: props.userRole === 'super_admin' ? '' : (props.userRole === 'admin' ? props.operatingUnits[0]?.id : null),
    department_unit_id: '',
    user_type: props.userType, // Add user_type to the form data
});

// Compute a user-friendly label for the user type
const userTypeLabel = computed(() => {
    return props.userType === 'department_unit' ? 'Department Unit' : 'Operating Unit';
});

// Check if the current admin user belongs to Central Administration
const isCentralAdmin = computed(() => {
    if (!props.auth || !props.auth.user || !props.auth.user.operatingUnit) {
        return false;
    }

    const operatingUnit = props.auth.user.operatingUnit;
    return operatingUnit.name === 'Central Administration' ||
        operatingUnit.code === 'CENTRAL' ||
        operatingUnit.code === 'CA';
});

// Check if the selected operating unit is Central Administration
const isCentralAdminSelected = computed(() => {
    if (!form.operating_unit_id) {
        return false;
    }

    const selectedUnit = props.operatingUnits.find(unit => unit.id === form.operating_unit_id);
    if (!selectedUnit) {
        return false;
    }

    return selectedUnit.name === 'Central Administration' ||
        selectedUnit.code === 'CENTRAL' ||
        selectedUnit.code === 'CA';
});

// Filter roles based on user role and selected operating unit
const filteredRoles = computed(() => {
    // Super Admin can assign any role except super_admin
    if (props.userRole === 'super_admin') {
        return props.roles;
    }

    // Admin from Central Administration
    if (props.userRole === 'admin' && isCentralAdmin.value) {
        // If Central Administration is selected as the operating unit,
        // only show staff role (Admin role is restricted to Super Admin)
        if (isCentralAdminSelected.value) {
            return props.roles.filter(role => role.slug === 'staff');
        }
        // For other operating units, show both admin and staff roles
        return props.roles;
    }

    // Regular admin can only assign staff role
    return props.roles.filter(role => role.slug === 'staff');
});

// Filtered department units based on selected operating unit
const filteredDepartmentUnits = computed(() => {
    if (!form.operating_unit_id) {
        return props.departmentUnits;
    }

    return props.departmentUnits.filter(
        dept => dept.operating_unit_id === form.operating_unit_id
    );
});

// Update department units when operating unit changes
const updateDepartmentUnits = () => {
    // Reset department unit selection when operating unit changes
    form.department_unit_id = '';
};

// Handle operating unit change
const onOperatingUnitChange = () => {
    // If Central Administration is selected and user is not Super Admin,
    // reset role selection if it was set to admin
    if (isCentralAdminSelected.value && props.userRole === 'admin') {
        const adminRole = props.roles.find(role => role.slug === 'admin');
        if (adminRole && form.role_id === adminRole.id) {
            form.role_id = '';
        }
    }
};

const submit = () => {
    form.post('/users');
};

// Function to navigate back to the users index page
const goBack = () => {
    window.history.back();
};
</script>

<style scoped>
:deep(.v-field__input) {
  color: rgba(0, 0, 0, 0.87) !important;
}
</style>
