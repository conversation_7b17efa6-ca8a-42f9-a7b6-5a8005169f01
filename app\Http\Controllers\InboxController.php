<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\Inbox;
use App\Models\User;
use App\Services\OperatingUnitSyncService;
use App\Models\DocumentTrackingHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class InboxController extends Controller
{
    /**
     * @var OperatingUnitSyncService
     */
    protected $syncService;

    /**
     * Constructor
     *
     * @param OperatingUnitSyncService $syncService
     */
    public function __construct(OperatingUnitSyncService $syncService)
    {
        $this->syncService = $syncService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Log request parameters for debugging
        Log::info('Inbox index request', [
            'user_id' => $user->id,
            'has_operating_unit' => (bool) $user->operating_unit_id,
            'reference_param' => $request->reference,
            'has_reference' => $request->has('reference'),
        ]);

        // If user doesn't have an operating unit, fall back to user-specific inbox
        if (!$user->operating_unit_id) {
            return $this->getUserSpecificInbox($user, $request);
        }

        // IMPORTANT: For the "All" filter, we must explicitly set referenceFilter to null
        // This ensures we get ALL messages without filtering by reference
        $referenceFilter = null;

        // Only apply reference filter if a specific reference is provided (F01, F02, etc.)
        if ($request->has('reference') && in_array($request->reference, ['F01', 'F02', 'F03', 'F04'])) {
            $referenceFilter = $request->reference;
        }

        Log::info('Using reference filter', [
            'filter' => $referenceFilter ?? 'All',
            'has_reference' => $request->has('reference'),
            'reference_value' => $request->reference
        ]);

        // Get inbox messages for the user's operating unit
        // When referenceFilter is null, this will return ALL messages without filtering by reference
        $allInboxMessages = $this->syncService->getInboxMessagesForOperatingUnit(
            $user->operating_unit_id,
            $referenceFilter
        );

        Log::info('Retrieved inbox messages', [
            'total_count' => $allInboxMessages->count(),
            'reference_filter' => $referenceFilter,
            'first_few_messages' => $allInboxMessages->take(3)->map(function ($message) {
                return [
                    'id' => $message->id,
                    'subject' => $message->subject,
                    'reference' => $message->reference,
                    'recipient_id' => $message->recipient_id
                ];
            })
        ]);

        // Filter to only show messages where this user is the recipient
        $filteredMessages = $allInboxMessages->filter(function ($message) use ($user) {
            return $message->recipient_id === $user->id;
        });

        Log::info('Filtered to user messages', [
            'filtered_count' => $filteredMessages->count(),
            'user_id' => $user->id,
            'first_few_filtered_messages' => $filteredMessages->take(3)->map(function ($message) {
                return [
                    'id' => $message->id,
                    'subject' => $message->subject,
                    'reference' => $message->reference,
                    'recipient_id' => $message->recipient_id
                ];
            })
        ]);

        // Paginate the filtered messages
        $inboxMessages = new \Illuminate\Pagination\LengthAwarePaginator(
            $filteredMessages->forPage($request->page ?? 1, 10),
            $filteredMessages->count(),
            10,
            $request->page ?? 1,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        // Count unread messages for this user
        $unreadCount = $filteredMessages->where('is_read', false)->count();

        // Count messages by reference type for this user
        $referenceTypeCounts = [
            'F01' => $filteredMessages->where('reference', 'F01')->count(),
            'F02' => $filteredMessages->where('reference', 'F02')->count(),
            'F03' => $filteredMessages->where('reference', 'F03')->count(),
            'F04' => $filteredMessages->where('reference', 'F04')->count(),
        ];

        Log::info('Reference type counts', $referenceTypeCounts);

        // Log the final data being sent to the frontend
        Log::info('Final data for frontend', [
            'inboxMessages_total' => $inboxMessages->total(),
            'inboxMessages_per_page' => $inboxMessages->perPage(),
            'inboxMessages_current_page' => $inboxMessages->currentPage(),
            'inboxMessages_data_count' => count($inboxMessages->items()),
            'unreadCount' => $unreadCount,
            'referenceTypeCounts' => $referenceTypeCounts,
            'activeReference' => $request->reference ?? 'null (All)',
        ]);

        return Inertia::render('Inbox/Index', [
            'inboxMessages' => $inboxMessages,
            'unreadCount' => $unreadCount,
            'referenceTypeCounts' => $referenceTypeCounts,
            'activeReference' => $request->reference ?? null,
        ]);
    }

    /**
     * Get user-specific inbox (fallback for users without operating unit)
     */
    private function getUserSpecificInbox($user, $request)
    {
        Log::info('Getting user-specific inbox', [
            'user_id' => $user->id,
            'reference_param' => $request->reference,
            'has_reference' => $request->has('reference'),
        ]);

        // Get inbox messages for the current user with document details including file_path
        $query = Inbox::with(['sender', 'document'])
            ->where('recipient_id', $user->id);

        // IMPORTANT: For the "All" filter, we don't apply any reference filter
        // Only filter by reference if explicitly provided in the request
        if ($request->has('reference') && in_array($request->reference, ['F01', 'F02', 'F03', 'F04'])) {
            // Use F01 format directly (with zero, not letter O)
            $query->where('reference', $request->reference);
            Log::info('Filtering by reference', [
                'reference' => $request->reference,
                'user_id' => $user->id
            ]);
        } else {
            Log::info('No reference filter applied, showing ALL messages', [
                'user_id' => $user->id
            ]);
        }

        // Get paginated results
        $inboxMessages = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        Log::info('User-specific inbox messages retrieved', [
            'total' => $inboxMessages->total(),
            'per_page' => $inboxMessages->perPage(),
            'current_page' => $inboxMessages->currentPage(),
        ]);

        // Count unread messages
        $unreadCount = Inbox::where('recipient_id', $user->id)
            ->where('is_read', false)
            ->count();

        // Count messages by reference type (using F01 format with zero)
        $referenceTypeCounts = [
            'F01' => Inbox::where('recipient_id', $user->id)->where('reference', 'F01')->count(),
            'F02' => Inbox::where('recipient_id', $user->id)->where('reference', 'F02')->count(),
            'F03' => Inbox::where('recipient_id', $user->id)->where('reference', 'F03')->count(),
            'F04' => Inbox::where('recipient_id', $user->id)->where('reference', 'F04')->count(),
        ];

        Log::info('User-specific reference type counts', $referenceTypeCounts);

        // Log the final data being sent to the frontend
        Log::info('Final data for frontend (user-specific)', [
            'inboxMessages_total' => $inboxMessages->total(),
            'inboxMessages_per_page' => $inboxMessages->perPage(),
            'inboxMessages_current_page' => $inboxMessages->currentPage(),
            'inboxMessages_data_count' => count($inboxMessages->items()),
            'unreadCount' => $unreadCount,
            'referenceTypeCounts' => $referenceTypeCounts,
            'activeReference' => $request->reference ?? 'null (All)',
            'first_few_messages' => collect($inboxMessages->items())->take(3)->map(function ($message) {
                return [
                    'id' => $message->id,
                    'subject' => $message->subject,
                    'reference' => $message->reference,
                    'recipient_id' => $message->recipient_idx
                ];
            })
        ]);

        return Inertia::render('Inbox/Index', [
            'inboxMessages' => $inboxMessages,
            'unreadCount' => $unreadCount,
            'referenceTypeCounts' => $referenceTypeCounts,
            'activeReference' => $request->reference ?? null,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get all users except the current user
        $users = User::where('id', '!=', $user->id)->get();

        // Get documents that the user has access to
        $documents = Document::where('created_by', $user->id)
            ->orWhere('assigned_to', $user->id)
            ->get();

        return Inertia::render('Inbox/Create', [
            'users' => $users,
            'documents' => $documents,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'recipient_id' => 'required|exists:users,id',
            'document_id' => 'nullable|exists:documents,id',
            'priority' => 'required|in:low,medium,high',
            'reference' => 'nullable|in:F01,F02,F03,F04',
        ]);

        // Add the sender_id to the validated data
        $validated['sender_id'] = Auth::id();

        // If a document is selected, set the reference field based on the document's reference_document
        if (!empty($validated['document_id'])) {
            $document = Document::find($validated['document_id']);
            if ($document) {
                // Use the reference directly from the document
                $validated['reference'] = $document->reference_document;
            }
        } else if (!empty($validated['reference'])) {
            // Use F01 format directly (with zero, not letter O)
            // No conversion needed - reference is already in the correct format
        }

        // Create the inbox message
        $inbox = Inbox::create($validated);

        return redirect()->route('inbox.index')->with('success', 'Message sent successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the inbox message
        $inbox = Inbox::with(['sender', 'recipient', 'document'])
            ->where('id', $id)
            ->where(function ($query) use ($user) {
                $query->where('sender_id', $user->id)
                    ->orWhere('recipient_id', $user->id);
            })
            ->firstOrFail();

        // Mark as read if the current user is the recipient and the message is unread
        if ($user->id === $inbox->recipient_id && !$inbox->is_read) {
            $inbox->is_read = true;
            $inbox->save();
        }

        return Inertia::render('Inbox/Show', [
            'inbox' => $inbox,
            'errors' => session()->get('errors') ? session()->get('errors')->getBag('default')->getMessages() : (object) [],
        ]);
    }

    /**
     * Mark a message as read.
     */
    public function markAsRead(string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the inbox message
        $inbox = Inbox::where('id', $id)
            ->where('recipient_id', $user->id)
            ->firstOrFail();

        // Mark as read
        $inbox->is_read = true;
        $inbox->save();

        return redirect()->back()->with('success', 'Message marked as read.');
    }

    /**
     * Mark a message as unread.
     */
    public function markAsUnread(string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the inbox message
        $inbox = Inbox::where('id', $id)
            ->where('recipient_id', $user->id)
            ->firstOrFail();

        // Mark as unread
        $inbox->is_read = false;
        $inbox->save();

        return redirect()->back()->with('success', 'Message marked as unread.');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the inbox message
        $inbox = Inbox::with(['sender', 'recipient', 'document'])
            ->where('id', $id)
            ->where('sender_id', $user->id) // Only allow editing of messages sent by the current user
            ->firstOrFail();

        // Get all users except the current user
        $users = User::where('id', '!=', $user->id)->get();

        // Get documents that the user has access to
        $documents = Document::where('created_by', $user->id)
            ->orWhere('assigned_to', $user->id)
            ->get();

        return Inertia::render('Inbox/Edit', [
            'inbox' => $inbox,
            'users' => $users,
            'documents' => $documents,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the inbox message
        $inbox = Inbox::where('id', $id)
            ->where('sender_id', $user->id) // Only allow updating of messages sent by the current user
            ->firstOrFail();

        // Validate the request
        $validated = $request->validate([
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'recipient_id' => 'required|exists:users,id',
            'document_id' => 'nullable|exists:documents,id',
            'priority' => 'required|in:low,medium,high',
            'reference' => 'nullable|in:F01,F02,F03,F04',
        ]);

        // If a document is selected, set the reference field based on the document's reference_document
        if (!empty($validated['document_id'])) {
            $document = Document::find($validated['document_id']);
            if ($document) {
                // Use the reference directly from the document
                $validated['reference'] = $document->reference_document;
            }
        } else if (empty($validated['reference'])) {
            // If no document is selected and no reference provided, set reference to null
            $validated['reference'] = null;
        }
        // else - reference is already set correctly in the validated data

        // Update the inbox message
        $inbox->update($validated);

        // Reset read status if recipient has changed
        if ($inbox->recipient_id !== $inbox->getOriginal('recipient_id')) {
            $inbox->is_read = false;
            $inbox->save();
        }

        return redirect()->route('inbox.index')->with('success', 'Message updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the inbox message
        $inbox = Inbox::where('id', $id)
            ->where(function ($query) use ($user) {
                $query->where('sender_id', $user->id)
                    ->orWhere('recipient_id', $user->id);
            })
            ->firstOrFail();

        // Delete the inbox message
        $inbox->delete();

        return redirect()->route('inbox.index')->with('success', 'Message deleted successfully.');
    }

    /**
     * Acknowledge a document from an inbox message.
     * This will:
     * 1. Mark the message as read
     * 2. Update the original document status to "completed"
     * 3. Create a new document record for the recipient's operating unit with status "pending"
     * 4. Create a tracking history record
     * 5. Return file path for automatic download
     */
    public function acknowledgeDocument(string $id)
    {
        // Get the authenticated user
        $user = Auth::user();

        // Get the inbox message with document
        $inbox = Inbox::with(['document', 'document.originUnit'])
            ->where('id', $id)
            ->where('recipient_id', $user->id)
            ->firstOrFail();

        // Check if the message has an associated document
        if (!$inbox->document) {
            return redirect()->back()->with('error', 'No document associated with this message.');
        }

        // Check if the user has an operating unit
        if (!$user->operating_unit_id) {
            return redirect()->back()->with('error', 'You must be associated with an operating unit to acknowledge documents.');
        }

        // Use a database transaction to ensure data integrity
        try {
            DB::beginTransaction();

            // Mark the message as read
            $inbox->is_read = true;
            $inbox->save();

            // Get the original document
            $originalDocument = $inbox->document;

            // Store the file path for download
            $filePath = $originalDocument->file_path;

            // Update the original document status to "completed"
            $originalDocument->status = 'completed';
            $originalDocument->current_unit_id = $user->operating_unit_id;
            $originalDocument->save();

            // Create a new document record for the recipient's operating unit
            // Always set reference_document to 'F03' for acknowledged documents
            $newDocument = new Document([
                'signatory' => $originalDocument->signatory,
                'origin' => $originalDocument->origin,
                'origin_unit_id' => $user->operating_unit_id,
                'current_unit_id' => $user->operating_unit_id,
                'department_unit_id' => $originalDocument->department_unit_id,
                'subject_matter' => $originalDocument->subject_matter,
                'referred_to' => $originalDocument->referred_to,
                'document_code' => $originalDocument->document_code,
                'reference_document' => 'F03', // Always set to F03 for incoming documents
                'control_number' => Document::generateControlNumber('F03',  $user->operating_unit_id), // Generate control number based on F03
                'received_by_id' => $user->id,
                'date_received' => now(),
                'status' => 'pending', // Set status to pending for the new document
                'file_path' => null, // The recipient must upload their own document
                'qr_code_path' => null, // The recipient must generate their own QR code
            ]);

            $newDocument->save();

            // Create a tracking history record for the original document
            $originalDocument->trackingHistory()->create([
                'status' => 'completed',
                'location' => $user->operatingUnit ? $user->operatingUnit->name : 'User Location',
                'notes' => "Document acknowledged by {$user->name} and transferred to {$user->operatingUnit->name}",
                'handled_by_id' => $user->id,
                'tracked_at' => now(),
                'requires_action' => false,
                'action_completed' => true,
                'action_completed_at' => now(),
            ]);

            // Create a tracking history record for the new document
            $newDocument->trackingHistory()->create([
                'status' => 'pending',
                'location' => $user->operatingUnit ? $user->operatingUnit->name : 'User Location',
                'notes' => "New F03 document created from acknowledged document {$originalDocument->control_number}. Categorized as incoming document (F03).",
                'handled_by_id' => $user->id,
                'tracked_at' => now(),
                'requires_action' => true,
                'action_completed' => false,
                'action_completed_at' => null,
            ]);

            DB::commit();

            // Return success response with file path for download
            return response()->json([
                'success' => true,
                'message' => 'Document acknowledged successfully and transferred to your operating unit.',
                'file_path' => $filePath ? '/storage/' . $filePath : null,
                'new_document_id' => $newDocument->id
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while acknowledging the document: ' . $e->getMessage()
            ], 500);
        }
    }
}
