<template>
  <Head :title="`User: ${user.name}`" />

  <AppSidebarLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">User Details</h2>
    </template>

    <v-container>
      <v-row>
        <v-col cols="12" class="d-flex justify-end">
          <Link :href="`/users/${user.id}/edit`" class="text-decoration-none mr-2">
            <v-btn color="secondary" prepend-icon="mdi-pencil">
              Edit User
            </v-btn>
          </Link>
          <Link href="/users" class="text-decoration-none">
            <v-btn color="primary" variant="outlined" prepend-icon="mdi-arrow-left">
              Back to Users
            </v-btn>
          </Link>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12">
          <v-card elevation="2">
            <v-card-title class="text-white bg-primary">
              User Information
            </v-card-title>

            <v-card-text>
              <v-row class="mt-4">
                <v-col cols="12" md="6">
                  <v-list>
                    <v-list-item>
                      <template v-slot:prepend>
                        <v-icon color="primary" class="mr-2">mdi-account</v-icon>
                      </template>
                      <v-list-item-title class="text-subtitle-1 font-weight-medium">Name</v-list-item-title>
                      <v-list-item-subtitle class="text-body-1">{{ user.name }}</v-list-item-subtitle>
                    </v-list-item>

                    <v-list-item>
                      <template v-slot:prepend>
                        <v-icon color="primary" class="mr-2">mdi-email</v-icon>
                      </template>
                      <v-list-item-title class="text-subtitle-1 font-weight-medium">Email</v-list-item-title>
                      <v-list-item-subtitle class="text-body-1">{{ user.email }}</v-list-item-subtitle>
                    </v-list-item>
                  </v-list>
                </v-col>

                <v-col cols="12" md="6">
                  <v-list>
                    <v-list-item>
                      <template v-slot:prepend>
                        <v-icon color="primary" class="mr-2">mdi-shield-account</v-icon>
                      </template>
                      <v-list-item-title class="text-subtitle-1 font-weight-medium">Role</v-list-item-title>
                      <v-list-item-subtitle class="text-body-1">
                        <v-chip
                          :color="getRoleColor(user.role.slug)"
                          size="small"
                          class="text-white"
                        >
                          {{ user.role.name }}
                        </v-chip>
                      </v-list-item-subtitle>
                    </v-list-item>

                    <v-list-item>
                      <template v-slot:prepend>
                        <v-icon color="primary" class="mr-2">mdi-office-building</v-icon>
                      </template>
                      <v-list-item-title class="text-subtitle-1 font-weight-medium">Operating Unit</v-list-item-title>
                      <v-list-item-subtitle class="text-body-1">
                        {{ user.operating_unit ? user.operating_unit.name : 'Not Assigned' }}
                      </v-list-item-subtitle>
                    </v-list-item>
                  </v-list>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
  user: Object,
});

const getRoleColor = (roleSlug) => {
  switch (roleSlug) {
    case 'super_admin':
      return 'deep-purple';
    case 'admin':
      return 'indigo';
    case 'staff':
      return 'teal';
    default:
      return 'grey';
  }
};
</script>

<style scoped>
.v-list-item {
  margin-bottom: 8px;
}

.text-subtitle-1.font-weight-medium {
  color: rgba(0, 0, 0, 0.87);
  font-weight: 500;
}

.text-body-1 {
  color: rgba(0, 0, 0, 0.6);
}
</style>
