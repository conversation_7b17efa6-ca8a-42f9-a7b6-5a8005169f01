<template>
    <Head title="Forwarded Document Details" />

    <AppSidebarLayout>
        <template #header>
            <h2 class="text-xl font-semibold leading-tight text-gray-800">Forwarded Document Details</h2>
        </template>

        <v-container>
            <v-row>
                <v-col cols="12" class="justify-end d-flex">
                    <Link href="/forwarded-documents" class="text-decoration-none">
                        <v-btn color="primary" variant="outlined" prepend-icon="mdi-arrow-left">
                            Back to Forwarded Documents
                        </v-btn>
                    </Link>
                </v-col>
            </v-row>

            <v-row>
                <v-col cols="12">
                    <v-card>
                        <v-card-title class="bg-primary text-white d-flex justify-space-between align-center">
                            <div>
                                <div class="text-h5">
                                    {{ trackingEntry.document ? trackingEntry.document.control_number : 'Unknown Document' }}
                                </div>
                                <div class="text-subtitle-1 mt-1">
                                    <v-chip size="small" color="success" class="mr-2">
                                        Forwarded
                                    </v-chip>
                                    <span v-if="trackingEntry.document && trackingEntry.document.reference_document">
                                        <v-chip size="small" color="info" class="mr-2">
                                            {{ trackingEntry.document.reference_document.replace('FO', 'F0') }}
                                        </v-chip>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <v-btn icon variant="text" color="white" @click="printDetails">
                                    <v-icon>mdi-printer</v-icon>
                                </v-btn>
                            </div>
                        </v-card-title>

                        <v-card-text class="pa-4">
                            <div class="d-flex justify-space-between align-center mb-4 pb-4 border-b">
                                <div>
                                    <div class="text-subtitle-1 font-weight-bold">
                                        Forwarded to: {{ trackingEntry.location }}
                                    </div>
                                </div>
                                <div class="text-caption text-grey-darken-1">
                                    {{ formatDate(trackingEntry.tracked_at) }}
                                </div>
                            </div>

                            <div v-if="trackingEntry.document" class="mb-4 pb-4 border-b">
                                <div class="text-subtitle-1 font-weight-bold mb-2">Document Details</div>
                                <v-card variant="outlined" class="pa-3">
                                    <div class="d-flex align-center">
                                        <v-icon color="primary" class="mr-2">mdi-file-document-outline</v-icon>
                                        <div>
                                            <div class="text-subtitle-2 font-weight-bold">
                                                {{ trackingEntry.document.control_number }}
                                            </div>
                                            <div class="text-caption">
                                                {{ trackingEntry.document.subject_matter }}
                                            </div>
                                        </div>
                                        <v-spacer></v-spacer>
                                        <Link :href="route('documents.show', trackingEntry.document.id)" class="text-decoration-none">
                                            <v-btn color="primary" size="small" variant="text">
                                                View Document
                                            </v-btn>
                                        </Link>
                                    </div>
                                </v-card>
                            </div>

                            <div class="tracking-notes">
                                <div class="text-subtitle-1 font-weight-bold mb-2">Notes</div>
                                <div class="text-body-1 notes-content">
                                    {{ trackingEntry.notes || 'No notes provided' }}
                                </div>
                            </div>
                        </v-card-text>

                        <v-card-actions class="pa-4 pt-0">
                            <v-spacer></v-spacer>
                            <Link :href="route('documents.index')" class="text-decoration-none">
                                <v-btn color="primary" prepend-icon="mdi-file-document-multiple-outline">
                                    View All Documents
                                </v-btn>
                            </Link>
                        </v-card-actions>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </AppSidebarLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const props = defineProps({
    trackingEntry: Object,
});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
};

const printDetails = () => {
    window.print();
};
</script>

<style scoped>
.border-b {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.notes-content {
    white-space: pre-wrap;
    line-height: 1.6;
}

@media print {
    .v-btn {
        display: none !important;
    }
}
</style>
