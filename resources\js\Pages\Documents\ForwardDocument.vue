<template>
  <Head :title="'Forward Document - ' + document.control_number" />
  <AppSidebarLayout>
    <v-container>
      <v-row>
        <v-col cols="12" class="d-flex justify-space-between align-center">
          <div>
            <h1 class="mb-2 text-h4">Forward Document</h1>
            <p class="text-subtitle-1">
              Control Number: {{ document.control_number }}
            </p>
          </div>
          <div>
            <v-btn
              color="primary"
              variant="outlined"
              class="mr-2"
              @click="goToDocumentDetails"
            >
              Back to Document
            </v-btn>
          </div>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" md="6">
          <v-card elevation="2">
            <v-card-title class="mb-4 text-white bg-primary"
              >Document Information</v-card-title
            >
            <v-card-text>
              <!-- Document Image Preview -->
              <div v-if="document.file_path" class="mb-4 document-preview">
                <h3 class="mb-2 text-subtitle-1 font-weight-medium">
                  Document Preview
                </h3>
                <div class="document-image-container">
                  <img
                    v-if="isImageFile(document.file_path)"
                    :src="getFileUrl(document.file_path)"
                    alt="Document Preview"
                    class="document-image"
                  />
                  <div v-else class="pdf-preview">
                    <v-icon size="large" color="primary"
                      >mdi-file-pdf-box</v-icon
                    >
                    <span>PDF Document</span>
                    <v-btn
                      color="primary"
                      size="small"
                      variant="text"
                      :href="getFileUrl(document.file_path)"
                      target="_blank"
                    >
                      View PDF
                    </v-btn>
                  </div>
                </div>
              </div>

              <v-list>
                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-file-document" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">
                      Control Number
                    </div>
                    <div class="text-body-2">{{ document.control_number }}</div>
                  </div>
                </v-list-item>

                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-account" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">
                      Signatory
                    </div>
                    <div class="text-body-2">{{ document.signatory }}</div>
                  </div>
                </v-list-item>

                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-office-building" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">Origin</div>
                    <div class="text-body-2">{{ document.origin }}</div>
                  </div>
                </v-list-item>

                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-information" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">
                      Subject Matter
                    </div>
                    <div class="text-body-2">{{ document.subject_matter }}</div>
                  </div>
                </v-list-item>

                <v-list-item>
                  <template v-slot:prepend>
                    <v-icon icon="mdi-tag" color="primary"></v-icon>
                  </template>
                  <div>
                    <div class="text-subtitle-1 font-weight-medium">Status</div>
                    <div>
                      <v-chip
                        :color="getStatusColor(document.status)"
                        size="small"
                        class="text-white"
                      >
                        {{ document.status }}
                      </v-chip>
                    </div>
                  </div>
                </v-list-item>
              </v-list>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="6">
          <v-card elevation="2">
            <v-card-title class="mb-4 text-white bg-primary"
              >Forward Document</v-card-title
            >
            <v-card-text>
              <v-form @submit.prevent="submit">
                <v-radio-group
                  v-model="form.classification"
                  label="Document Classification"
                  :error-messages="errors.classification"
                  required
                  class="mb-4"
                >
                  <v-radio
                    value="public"
                    label="Public (All operating units can receive)"
                  ></v-radio>
                  <v-radio
                    value="confidential"
                    label="Confidential (Select specific operating units)"
                  ></v-radio>
                </v-radio-group>

                <div v-if="form.classification === 'public'">
                  <p class="mb-2 text-body-2">
                    Document will be forwarded to all operating units.
                  </p>
                </div>

                <div v-if="form.classification === 'confidential'">
                  <div class="mb-2 operating-units-selection">
                    <div class="mb-2 text-subtitle-1">
                      Select Operating Units:
                    </div>

                    <div class="operating-units-container">
                      <v-checkbox
                        v-for="unit in props.operatingUnits"
                        :key="unit.id"
                        v-model="selectedOperatingUnitIds"
                        :value="unit.id"
                        :label="unit.name"
                        color="primary"
                        density="compact"
                        hide-details
                        class="operating-unit-checkbox"
                        @update:model-value="updateSelectedOperatingUnits"
                      ></v-checkbox>
                    </div>
                  </div>

                  <div
                    v-if="selectedOperatingUnits.length > 0"
                    class="mb-2 selected-count"
                  >
                    <v-chip color="primary" class="text-white">
                      Selected Units: {{ selectedOperatingUnits.length }}
                    </v-chip>
                  </div>

                  <div
                    v-if="errors['operating_units']"
                    class="mb-4 text-error text-caption"
                  >
                    {{ errors["operating_units"] }}
                  </div>
                </div>

                <!-- Priority Selection -->
                <v-radio-group
                  v-model="form.priority"
                  label="Priority Classification"
                  :error-messages="errors.priority"
                  required
                  class="mb-4"
                >
                  <v-radio value="critical" label="Critical" color="error">
                    <template v-slot:label>
                      <div class="d-flex align-center">
                        <v-icon color="error" class="mr-2"
                          >mdi-alert-circle</v-icon
                        >
                        <span>Critical</span>
                      </div>
                    </template>
                  </v-radio>
                  <v-radio value="urgent" label="Urgent" color="warning">
                    <template v-slot:label>
                      <div class="d-flex align-center">
                        <v-icon color="warning" class="mr-2">mdi-alert</v-icon>
                        <span>Urgent</span>
                      </div>
                    </template>
                  </v-radio>
                  <v-radio value="routinary" label="Routinary" color="info">
                    <template v-slot:label>
                      <div class="d-flex align-center">
                        <v-icon color="info" class="mr-2"
                          >mdi-information</v-icon
                        >
                        <span>Routinary</span>
                      </div>
                    </template>
                  </v-radio>
                </v-radio-group>

                <div class="justify-end d-flex">
                  <v-btn
                    type="submit"
                    color="primary"
                    size="large"
                    :loading="form.processing"
                  >
                    Forward Document
                  </v-btn>
                </div>
              </v-form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </AppSidebarLayout>
</template>

<script setup>
import { Head, useForm, router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';
import { ref, watch } from 'vue';

const props = defineProps({
    document: Object,
    operatingUnits: Array,
    errors: Object,
});

// Operating units management
const selectedOperatingUnitIds = ref([]);
const selectedOperatingUnits = ref([]);

const updateSelectedOperatingUnits = () => {
    selectedOperatingUnits.value = props.operatingUnits.filter(unit =>
        selectedOperatingUnitIds.value.includes(unit.id)
    );
     form.operating_units = selectedOperatingUnitIds.value;
};

// Initialize the form
const form = useForm({
    classification: 'public',
    operating_units: [],
    priority: 'routinary',
});

// Function to get file URL
const getFileUrl = (path) => {
    if (!path) return '';
    return `/storage/${path}`;
};

// Function to check if file is an image
const isImageFile = (path) => {
    if (!path) return false;
    const extension = path.split('.').pop().toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension);
};

// Watch for classification changes
watch(() => form.classification, (newValue) => {
    if (newValue === 'public') {
        // If public, select all operating units
        form.operating_units = props.operatingUnits.map(unit => unit.id);
    } else {
        // If confidential, use the current selection
        form.operating_units = selectedOperatingUnitIds.value;
    }
});

// Initialize with public classification (all operating units)
if (props.operatingUnits) {
    const allOperatingUnitIds = props.operatingUnits.map(unit => unit.id);
    form.operating_units = allOperatingUnitIds;

    // Also initialize the selectedOperatingUnitIds for when user switches to confidential
    selectedOperatingUnitIds.value = [];
}

const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
        case 'pending':
            return 'warning';
        case 'in progress':
            return 'info';
        case 'completed':
            return 'success';
        case 'rejected':
            return 'error';
        default:
            return 'grey';
    }
};

const goToDocumentDetails = () => {
    router.visit(`/documents/${props.document.id}`);
};

const submit = () => {
    form.post(`/documents/${props.document.id}/forward`, {
        onSuccess: () => {
            // Redirect to document details page after successful forward
            router.visit(`/documents/${props.document.id}`);
        }
    });
};
</script>

<style scoped>
.v-list-item {
  margin-bottom: 8px;
}

.text-subtitle-1.font-weight-medium {
  color: rgba(0, 0, 0, 0.87);
  font-weight: 500;
}

.text-body-2 {
  color: rgba(0, 0, 0, 0.6);
}

/* Document preview styling */
.document-preview {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background-color: #f9f9f9;
}

.document-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.document-image {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
}

.pdf-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  width: 100%;
}

/* Operating unit selection styling */
.operating-units-selection {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background-color: #f9f9f9;
  max-height: 400px;
  display: flex;
  flex-direction: column;
}

.operating-units-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
  overflow-y: auto;
  max-height: 300px;
  padding: 12px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.operating-unit-checkbox {
  margin: 0 !important;
  padding: 4px 8px !important;
}

.selected-units-summary {
  margin-top: 16px;
  padding: 8px;
  background-color: #f0f8ff;
  border-radius: 4px;
  border: 1px dashed #2196f3;
}

.selected-count {
  margin-top: 16px;
  padding: 8px;
  display: flex;
  justify-content: flex-start;
}

.v-chip-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 0;
}

.v-chip {
  margin: 0 !important;
}

.text-error {
  color: #f44336;
  margin-top: -8px;
}
</style>
