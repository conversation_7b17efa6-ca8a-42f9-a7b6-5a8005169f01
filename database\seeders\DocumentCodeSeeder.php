<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DocumentCode;

class DocumentCodeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if there are already document codes in the database
        if (DocumentCode::count() > 0) {
            $this->command->info('Document codes already exist. Skipping seeding.');
            return;
        }

        // Actual document codes from the database
        $documentCodes = [
            "201",
            "ALUMNI",
            "AO",
            "AUX",
            "BAO",
            "BOARD RES",
            "CAS",
            "CE",
            "CGS",
            "CHANC",
            "CIT",
            "COA",
            "COE",
            "COL",
            "COM",
            "COT",
            "CULTURAL",
            "FAD",
            "FIN",
            "HI",
            "HRMO",
            "ICJE",
            "INFRA",
            "INTERNATIONAL",
            "LIB",
            "MED",
            "MIS",
            "NSTP",
            "NTA",
            "OUS",
            "PRES",
            "QA",
            "R&E",
            "R<PERSON><PERSON><PERSON>",
            "R<PERSON><PERSON><PERSON><PERSON>",
            "RES<PERSON>UR<PERSON>",
            "SAS/SBO",
            "SECURITY",
            "SPORTS",
            "<PERSON>ROJ",
            "SUPPLY",
            "TRAV",
        ];

        // Insert the document codes
        foreach ($documentCodes as $code) {
            DocumentCode::create([
                'docu_code' => $code,
            ]);
        }

        $this->command->info('Document codes seeded successfully.');
    }
}
