<?php

namespace App\Http\Controllers;

use App\Models\DocumentCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DocumentCodeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Check if user is super admin
        // if (!Auth::user()->isSuperAdmin()) {
        //     abort(403, 'Unauthorized action.');
        // }

        // Get the per page parameter, default to 10
        // $perPage = $request->input('per_page');

        // // If perPage is -1, get all records
        // if ($perPage == -1) {
        //     $documentCodes = DocumentCode::orderBy('operating_unit_code_value')->get();
        //     // Format the response to match pagination structure
        //     $documentCodes = [
        //         'data' => $documentCodes,
        //         'total' => $documentCodes->count(),
        //         'per_page' => -1,
        //         'current_page' => 1,
        //         'last_page' => 1,
        //     ];
        // } else {
        //     $documentCodes = DocumentCode::orderBy('operating_unit_code_value')
        //         ->paginate($perPage);
        // }

        $user = Auth::user();

        $codes_per_op_unit = $user->operatingUnit->documentCodes()
            ->orderBy('operating_unit_code_value')
            ->get()
            ->mapWithKeys(function ($code) {
                return [$code->operating_unit_code_key => $code->operating_unit_code_value];
            });

        return Inertia::render('DocumentCodes/Index', [
            'documentCodes' => $codes_per_op_unit,
            'flash' => [
                'success' => session('success'),
                'error' => session('error')
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * Note: This method is no longer used as creation is handled directly on the index page.
     * Keeping it for API completeness.
     */
    public function create()
    {
        // Check if user is super admin
        // if (!Auth::user()->isSuperAdmin()) {
        //     abort(403, 'Unauthorized action.');
        // }

        return redirect()->route('document-codes.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'key' => 'required|string',
            'value' => 'required|json', // or 'required|string' if raw JSON string
        ]);

        $operatingUnitId = Auth::user()->operating_unit_id;

        // Find existing DocumentCode or null
        $docCode = DocumentCode::where('operating_unit_id', $operatingUnitId)
            ->where('operating_unit_code_key', $request->key)
            ->first();

        // Decode existing JSON or initialize empty array
        $existingValues = $docCode ? json_decode($docCode->operating_unit_code_value, true) : [];

        if (!is_array($existingValues)) {
            $existingValues = []; // fallback if decoding failed
        }

        // Decode incoming JSON value (array or single value wrapped as array)
        $incomingValues = json_decode($request->value, true);

        if (!is_array($incomingValues)) {
            // if incoming is not an array, make it one
            $incomingValues = [$incomingValues];
        }

        // Merge arrays, append new values
        $mergedValues = array_unique(array_merge($existingValues, $incomingValues));

        // Save updated JSON string back
        DocumentCode::updateOrCreate(
            [
                'operating_unit_id' => $operatingUnitId,
                'operating_unit_code_key' => $request->key,
            ],
            [
                'operating_unit_code_value' => json_encode(array_values($mergedValues)),
            ]
        );

        return redirect()->route('document-codes.index')
            ->with('success', 'Document code saved successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show($docu_code)
    {
        // Check if user is super admin
        // if (!Auth::user()->isSuperAdmin()) {
        //     abort(403, 'Unauthorized action.');
        // }

        $document_code = DocumentCode::where('docu_code', $docu_code)->firstOrFail();

        return Inertia::render('DocumentCodes/Show', [
            'documentCode' => $document_code
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($docu_code)
    {
        // Check if user is super admin
        // if (!Auth::user()->isSuperAdmin()) {
        //     abort(403, 'Unauthorized action.');
        // }

        $document_code = DocumentCode::where('docu_code', $docu_code)->firstOrFail();

        return Inertia::render('DocumentCodes/Edit', [
            'documentCode' => $document_code
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {
        $request->validate([
            'key' => 'required|string',
            'old_code' => 'required|string',
            'new_code' => 'required|string',
        ]);

        $operatingUnitId = Auth::user()->operating_unit_id;

        $documentCode = DocumentCode::where('operating_unit_id', $operatingUnitId)
            ->where('operating_unit_code_key', $request->key)
            ->firstOrFail();

        // Decode JSON to array
        $codes = json_decode($documentCode->operating_unit_code_value, true);

        if (!is_array($codes)) {
            $codes = []; // fallback if null or invalid JSON
        }

        // Replace old_code with new_code in the array
        $updated = false;
        foreach ($codes as &$code) {
            if ($code === $request->old_code) {
                $code = $request->new_code;
                $updated = true;
                break;
            }
        }

        if (!$updated) {
            return redirect()->back()->with('error', 'Old code not found in the list.');
        }

        // Save updated JSON back to DB
        $documentCode->operating_unit_code_value = json_encode($codes);
        $documentCode->save();

        return redirect()->route('document-codes.index')->with('success', 'Document code updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request)
    {
        $key = $request->input('key');
        $code = $request->input('code');

        if (!$key || !$code) {
            return response()->json(['message' => 'Missing key or code.'], 400);
        }

        $operatingUnitId = Auth::user()->operating_unit_id;

        $documentCode = DocumentCode::where('operating_unit_id', $operatingUnitId)
            ->where('operating_unit_code_key', $key)
            ->first();

        if (!$documentCode) {
            return response()->json(['message' => 'Document code key not found.'], 404);
        }

        $codes = json_decode($documentCode->operating_unit_code_value, true);

        if (!is_array($codes) || !in_array($code, $codes)) {
            return response()->json(['message' => 'Code not found.'], 404);
        }

        $updatedCodes = array_values(array_filter($codes, fn($c) => $c !== $code));
        $documentCode->operating_unit_code_value = json_encode($updatedCodes);
        $documentCode->save();

        return redirect()->route('document-codes.index')
            ->with('success', 'Document Code deleted successfully.');
    }
}
