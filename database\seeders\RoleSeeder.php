<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create the three main roles
        Role::create([
            'name' => 'Super Admin',
            'slug' => 'super_admin',
            'description' => 'Super Administrator with full system access',
        ]);

        Role::create([
            'name' => 'Admin',
            'slug' => 'admin',
            'description' => 'Administrator with limited system access',
        ]);

        Role::create([
            'name' => 'Staff',
            'slug' => 'staff',
            'description' => 'Regular staff member with basic access',
        ]);
    }
}
