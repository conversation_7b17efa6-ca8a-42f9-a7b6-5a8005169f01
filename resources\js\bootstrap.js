import axios from 'axios';
window.axios = axios;

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

/**
 * Echo exposes an expressive API for subscribing to channels and listening
 * for events that are broadcast by Laravel. Echo and event broadcasting
 * allows your team to easily build robust real-time web applications.
 */

import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

// Use the environment variables or fallback to hardcoded values from .env
const pusherKey = import.meta.env.VITE_PUSHER_APP_KEY || 'bf70f5a8cb8d8d416e4b';
const pusherCluster = import.meta.env.VITE_PUSHER_APP_CLUSTER || 'ap1';

window.Echo = new Echo({
    broadcaster: 'pusher',
    key: pusherKey,
    cluster: pusherCluster,
    forceTLS: true,
});
