<?php

namespace App\Http\Controllers;

use App\Models\OperatingUnit;
use Illuminate\Http\Request;
use Inertia\Inertia;

class OperatingUnitController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $operatingUnits = OperatingUnit::orderBy('name')->paginate(10);

        return Inertia::render('OperatingUnits/Index', [
            'operatingUnits' => $operatingUnits
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('OperatingUnits/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:operating_units,code',
            'email' => 'nullable|email|max:255',
        ]);

        OperatingUnit::create($request->all());

        return redirect()->route('operating-units.index')
            ->with('success', 'Operating Unit created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(OperatingUnit $operatingUnit)
    {
        return Inertia::render('OperatingUnits/Show', [
            'operatingUnit' => $operatingUnit,
            'errors' => session()->get('errors') ? session()->get('errors')->getBag('default')->getMessages() : (object) [],
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OperatingUnit $operatingUnit)
    {
        return Inertia::render('OperatingUnits/Edit', [
            'operatingUnit' => $operatingUnit
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OperatingUnit $operatingUnit)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:operating_units,code,' . $operatingUnit->id,
            'email' => 'nullable|email|max:255',
        ]);

        $operatingUnit->update($request->all());

        return redirect()->route('operating-units.index')
            ->with('success', 'Operating Unit updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OperatingUnit $operatingUnit)
    {
        // Check if the operating unit is being used by any documents
        if ($operatingUnit->documents()->count() > 0) {
            return redirect()->route('operating-units.index')
                ->with('error', 'Cannot delete operating unit because it is being used by documents.');
        }

        $operatingUnit->delete();

        return redirect()->route('operating-units.index')
            ->with('success', 'Operating Unit deleted successfully.');
    }
}
