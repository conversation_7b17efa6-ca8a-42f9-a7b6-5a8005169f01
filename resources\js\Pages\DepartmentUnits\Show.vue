<template>
  <Head title="Department Units" />
  <AppSidebarLayout>
    <v-container>
      <v-row>
        <v-col cols="12" class="d-flex justify-space-between align-center">
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-card elevation="2">
            <v-card-title class="bg-primary text-white"
              >Department Unit Information</v-card-title
            >
            <v-card-text>
              <v-row class="ma-0 mt-4 mb-1 pa-0">
                <v-col
                  cols="1"
                  class="text-black text-subtitle-1 font-weight-bold pa-0"
                >
                  Unit Name:
                </v-col>
                <v-col class="d-flex align-center">
                  <div v-if="!editMode" class="text-black pa-0 mb-5">
                    {{ form.name }}
                  </div>
                  <v-text-field
                    v-else
                    density="compact"
                    variant="outlined"
                    color="primary"
                    class="text-black"
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
              <v-row class="ma-0 mb-1 pa-0">
                <v-col
                  cols="1"
                  class="text-black text-subtitle-1 font-weight-bold pa-0"
                >
                  Unit Code:
                </v-col>
                <v-col class="d-flex align-center">
                  <div v-if="!editMode" class="text-black pa-0 mb-3">
                    {{ form.code }}
                  </div>
                  <v-text-field
                    v-else
                    density="compact"
                    variant="outlined"
                    color="primary"
                    class="text-black"
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
              <!-- <v-row class="ma-0 mb-1 pa-0">
                <v-col
                  cols="1"
                  class="text-black text-subtitle-1 font-weight-bold pa-0 d-flex align-center"
                >
                  Status:
                </v-col>
                <v-col class="d-flex align-center">
                  <div v-if="!editMode" class="d-flex align-center">
                    {{ form.name }}
                  </div>
                </v-col>
                <v-col>
                  <v-text-field
                    density="compact"
                    variant="outlined"
                    color="primary"
                    class="text-black"
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row> -->
              <v-row>
                <v-col class="d-flex justify-end">
                  <v-btn @click="goEditMode" color="blue-lighten-1"
                    >Edit Document</v-btn
                  >
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </AppSidebarLayout>
</template>
<script setup>
  
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import { router } from '@inertiajs/vue3';
import AppSidebarLayout from '@/Layouts/AppSidebarLayout.vue';

const editMode = ref(false);

const props = defineProps({
  departmentUnit: Object
});

const form = useForm({
  name: props.departmentUnit.name,
  code: props.departmentUnit.code
});

const goEditMode = () => {
  editMode.value = true;
};

</script>
<style scoped>
</style>
