<?php

namespace App\Providers;

use App\Models\Inbox;
use App\Observers\InboxObserver;
use App\Services\OperatingUnitSyncService;
use Dompdf\Dompdf;
use Dompdf\FontMetrics;
use Dompdf\Options;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Inertia\Inertia;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register the OperatingUnitSyncService as a singleton
        $this->app->singleton(OperatingUnitSyncService::class, fn() => new OperatingUnitSyncService());
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Vite::prefetch(concurrency: 3);
        Schema::defaultStringLength(191);

        // Register observers for data synchronization
        Inbox::observe(InboxObserver::class);
        \App\Models\Document::observe(\App\Observers\DocumentObserver::class);
        \App\Models\DocumentTracking::observe(\App\Observers\DocumentTrackingObserver::class);
        \App\Models\DocumentTrackingHistory::observe(\App\Observers\DocumentTrackingHistoryObserver::class);

        Inertia::share('preferences', function () {
            $user = Auth::user();

            if (!$user) {
                return [];
            }

            $currentRoute = Route::currentRouteName();

            // Map route names to preference keys
            $scopedKeys = match ($currentRoute) {
                'documents.index' => ['documents.column_arrangement'],
                'dashboard.index' => ['dashboard.widget_order'],
                // add more routes with their keys here
                default => [],
            };

            return $user->preferences()
                ->whereIn('preference_key', $scopedKeys)
                ->get()
                ->mapWithKeys(fn($pref) => [$pref->preference_key => $pref->preference_value])
                ->toArray();
        });
    }
}
